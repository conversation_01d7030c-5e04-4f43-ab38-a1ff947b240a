// import { openDB } from "idb";

// const DB_NAME = "ServiceLevelData";
// const CUSTOMER_LIST_STORE_NAME = "customerList";
// const CUSTOMER_DATA_STORE_NAME="customerData";
// const PROUDUCTS_STORE_NAME="productsList";
// const VERSION = 1;

// let dbPromise;

// const getDBPromise = () => {
//   if (typeof window === "undefined") {
//     return null; // Return null if it's not running in the browser
//   }

//   if (!dbPromise) {
//     dbPromise = openDB(DB_NAME, VERSION, {
//       upgrade(db) {
//         if (!db.objectStoreNames.contains(CUSTOMER_LIST_STORE_NAME)) {
//           db.createObjectStore(CUSTOMER_LIST_STORE_NAME, {
//             keyPath: "label",
//           });
//         }
//         if (!db.objectStoreNames.contains(CUSTOMER_DATA_STORE_NAME)) {
//           db.createObjectStore(
//             CUSTOMER_DATA_STORE_NAME,
//             {
//               keyPath: "ALTFILID",
//             }
//           );
//         }

//         if (!db.objectStoreNames.contains(PROUDUCTS_STORE_NAME)) {
//           db.createObjectStore(PROUDUCTS_STORE_NAME, {
//             keyPath: "value",
//           });
//         }
//       },
//     });
//   }

//   return dbPromise;
// };

// // Clear the idb
// export const clearIDB = async () => {
//   const db = await getDBPromise();
//   if (!db) return;

//   // Get all existing entries for the given customer_code
//   const txDelete = db.transaction(CUSTOMER_LIST_STORE_NAME, "readwrite");
//   const storeDelete = txDelete.objectStore(CUSTOMER_LIST_STORE_NAME);
//   storeDelete.clear();
//   await txDelete.done;
// };


// // on change of selected customer, replace the data with the new one
// export const replaceCustomersListWithTheNew = async (newCustomers) => {
//   const db = await getDBPromise();
//   if (!db) return;

//   if (newCustomers?.length === 0) return;
//   // Get all existing entries for the given customer_code
//   const txDelete = db.transaction(CUSTOMER_LIST_STORE_NAME, "readwrite");
//   const storeDelete = txDelete.objectStore(CUSTOMER_LIST_STORE_NAME);
//   storeDelete.clear();
//   await txDelete.done;

//   // Add new data
//   const txAdd = db.transaction(CUSTOMER_LIST_STORE_NAME, "readwrite");
//   const storeAdd = txAdd.objectStore(CUSTOMER_LIST_STORE_NAME);
//   await Promise.all(newCustomers.map((customer) => storeAdd.put(customer)));
//   await txAdd.done;
// };
// export const replaceCustomersDataWithTheNew = async (newCustomerData) => {
//   const db = await getDBPromise();
//   if (!db) return;

//   if (newCustomerData?.length === 0) return;
//   // Get all existing entries for the given customer_code
//   const txDelete = db.transaction(CUSTOMER_DATA_STORE_NAME, "readwrite");
//   const storeDelete = txDelete.objectStore(CUSTOMER_DATA_STORE_NAME);
//   storeDelete.clear();
//   await txDelete.done;

//   // Add new data
//   const txAdd = db.transaction(CUSTOMER_DATA_STORE_NAME, "readwrite");
//   const storeAdd = txAdd.objectStore(CUSTOMER_DATA_STORE_NAME);
//   await Promise.all(newCustomerData.map((customer) => {
//     storeAdd.put(customer)}));
//   await txAdd.done;
// };


// export const replaceProductsWithTheNew = async (newProductsList) => {
//   const db = await getDBPromise();
//   if (!db) return;

//   if (newProductsList?.length === 0) return;
//   // Get all existing entries for the given customer_code
//   const txDelete = db.transaction(PROUDUCTS_STORE_NAME, "readwrite");
//   const storeDelete = txDelete.objectStore(PROUDUCTS_STORE_NAME);
//   storeDelete.clear();
//   await txDelete.done;

//   // Add new data
//   const txAdd = db.transaction(PROUDUCTS_STORE_NAME, "readwrite");
//   const storeAdd = txAdd.objectStore(PROUDUCTS_STORE_NAME);
//   await Promise.all(newProductsList.map((product) => {
//     storeAdd.put(product)}));
//   await txAdd.done;
// };

// // Get data by pkey
// // export const getProductFromIdbByPkey = async (label) => {
// //   const db = getDBPromise();
// //   if (!db) return null;

// //   return await db.get(CUSTOMER_LIST_STORE_NAME, label);
// // };