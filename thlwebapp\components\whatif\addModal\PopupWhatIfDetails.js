import React, { useEffect, useState } from "react";
import {
  GRID_BE,
  GRID_PRICE,
  GRID_VOLUME,
  WHATIF_TYPE_BREAKEVEN_ID,
  WHATIF_TYPE_PRICE_ID,
  WHATIF_TYPE_PROMO_ID,
  WHATIF_TYPE_RR_VOLUME_ID,
  WHATIF_TYPE_SUPPLIR_ISSUE_ID,
} from "../../../utils/whatif/utils/constants";
import { Tooltip } from "@fluentui/react-components";

export default function PopupWhatIfDetails({
  modalConfig,
  popupData,
  setPopupData,
  isRemoveConfirmationShown,overlappingWeek
}) {
  const [prevWhatIfType, setPrevWhatIfType] = useState(popupData.whatIfType);
  const [prevWhatIfTypeSelectedWeeks, setPrevWhatIfTypeSelectedWeeks] =
    useState(popupData.selectedWeeks);
  const [initialRender, setIsInitialRender] = useState(true);

  useEffect(() => {
    if (
      popupData.selectedWeeks &&
      popupData.selectedWeeks.length > 0
    ) {
      setPrevWhatIfType(popupData.whatIfType);

      setPrevWhatIfTypeSelectedWeeks(popupData.selectedWeeks);
      setIsInitialRender(false);
    }
  }, [popupData.selectedWeeks]);

  return (
    <>
      <div className="flex flex-col gap-4 w-1/3 pt-2">
        <div className="flex flex-col gap-2 w-full">
          <label className="labels">Product</label>
          <input
            type="text"
            name=""
            className="px-2 2xl:px-3 border rounded-md"
            value={modalConfig.productName}
            disabled={true}
            readOnly
          />
        </div>
        <div className="flex flex-col gap-2 w-full">
          <div className="flex flex-row justify-between">
            <label className="labels">Task Type</label>
            <div className="flex flex-row items-center gap-2 cursor-pointer">
              {popupData.isEditMode && modalConfig.grid === GRID_VOLUME && (
                <>
                  <span
                    className={`flex flex-row gap-2 ${overlappingWeek.length==0?"text-skin-primary cursor-pointer":"text-gray-300 cursor-not-allowed"}`}
                    onClick={() => {
                      if(overlappingWeek.length>0){
                        return;
                      }
                      if(isRemoveConfirmationShown)return;
                      setPopupData((prev) => {
                        return { ...prev, isEditMode: false };
                      });
                    }}
                  >
                    Add New Task
                  </span>
                  <Tooltip
                    content="If adding the new task type, that will be overrriden by promotion"
                    relationship="label"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 512 512"
                      className={`w-4 h-4 ${overlappingWeek.length==0?"fill-skin-primary":"fill-gray-300"}`}
                     
                    >
                      <path d="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416zm0 464A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM216 336c-13.3 0-24 10.7-24 24s10.7 24 24 24h80c13.3 0 24-10.7 24-24s-10.7-24-24-24h-8V248c0-13.3-10.7-24-24-24H216c-13.3 0-24 10.7-24 24s10.7 24 24 24h24v64H216zm40-144a32 32 0 1 0 0-64 32 32 0 1 0 0 64z" />
                    </svg>
                  </Tooltip>
                </>
              )}
            </div>
          </div>

          <select
            className="px-2 2xl:px-3 border rounded-md"
            value={popupData.whatIfType}
            readOnly={popupData.isEditMode}
            disabled={popupData.isEditMode || isRemoveConfirmationShown}
            onChange={(e) => {
              if (!!prevWhatIfType) {
                if (prevWhatIfType === +e.target.value) {
                  setPopupData((prev) => {
                    return {
                      ...prev,
                      selectedWeeks: prevWhatIfTypeSelectedWeeks,
                    };
                  });
                } else {

                  setPopupData((prev) => {
                    return {
                      ...prev,
                      selectedWeeks: [...prevWhatIfTypeSelectedWeeks.map((week, i) => {
                          return {
                            ...prev.selectedWeeks[i],
                            current: { ...week.previous },
                          };
                      })],
                    };
                  });
                }
              }

              setPopupData((prev) => {
                return { ...prev, whatIfType: +e.target.value };
              });
            }}
          >
            {modalConfig.grid === GRID_VOLUME && (
              <option value={WHATIF_TYPE_PROMO_ID}>Add Promotion</option>
            )}
            {modalConfig.grid === GRID_VOLUME && (
              <option value={WHATIF_TYPE_SUPPLIR_ISSUE_ID}>
                Supplier Issue
              </option>
            )}
            {modalConfig.grid === GRID_VOLUME && (
              <option value={WHATIF_TYPE_RR_VOLUME_ID}>
                Amending Run Rate Volume
              </option>
            )}
            {modalConfig.grid === GRID_PRICE && (
              <option value={WHATIF_TYPE_PRICE_ID}>Update Price</option>
            )}
            {modalConfig.grid === GRID_BE && (
              <option value={WHATIF_TYPE_BREAKEVEN_ID}>Breakeven</option>
            )}
          </select>
        </div>

        <div className="flex flex-col gap-2 w-full">
          <label className="labels">Description</label>
          <textarea
            className="px-2 2xl:px-3 border rounded-md"
            rows={5}
            value={popupData.description}
            onChange={(e) => {
              setPopupData((prev) => {
                return { ...prev, description: e.target.value };
              });
            }}
            maxLength={500}
            disabled={isRemoveConfirmationShown}
          ></textarea>
        </div>
      </div>
    </>
  );
}
