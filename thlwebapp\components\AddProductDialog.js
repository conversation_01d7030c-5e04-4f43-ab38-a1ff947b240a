import React, { useState } from "react";
import {
  Dialog,
  DialogSurface,
  DialogTitle,
  DialogBody,
  DialogActions,
  DialogContent,
  Button as FluentButton,
  Switch,
  FluentProvider,
  webLightTheme,
} from "@fluentui/react-components";

const AddProductDialog = ({
  open,
  onOpenChange,
  onSubmit,
  initialProductName = "",
  initialPrinting = false,
  initialSizes = [""],
}) => {
  const [productName, setProductName] = useState(initialProductName);
  const [printing, setPrinting] = useState(initialPrinting);
  const [sizes, setSizes] = useState(initialSizes);
  const [newSize, setNewSize] = useState("");
  const [draggedIdx, setDraggedIdx] = useState(null);

  // Drag and drop handlers
  const handleDragStart = (idx) => setDraggedIdx(idx);

  const handleDragOver = (e, idx) => {
    e.preventDefault();
  };

  const handleDrop = (e, idx) => {
    e.preventDefault();
    if (draggedIdx === null || draggedIdx === idx) return;
    const updated = [...sizes];
    const [removed] = updated.splice(draggedIdx, 1);
    updated.splice(idx, 0, removed);
    setSizes(updated);
    setDraggedIdx(null);
  };

  const handleDragEnd = () => setDraggedIdx(null);

  // Add/remove size handlers
  const handleAddSize = () => {
    if (newSize.trim() !== "") {
      setSizes([...sizes, newSize]);
      setNewSize("");
    }
  };

  const handleRemoveSize = (idx) => {
    setSizes(sizes.filter((_, i) => i !== idx));
  };

  // Submit handler
  const handleSubmit = () => {
    onSubmit({ productName, printing, sizes: sizes.filter((s) => s) });
    setProductName("");
    setPrinting(false);
    setSizes([""]);
    setNewSize("");
  };

  return (
    <FluentProvider theme={webLightTheme} className="!bg-transparent" style={{ fontFamily: "poppinsregular" }}>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogSurface className="!bg-white">
          <DialogBody>
            <DialogTitle>Add New Product</DialogTitle>
            <DialogContent>
              <div style={{ marginBottom: 12 }}>
                <input
                  placeholder="Product Name"
                  value={productName}
                  onChange={(e) => setProductName(e.target.value)}
                  className="flex w-full p-2 mt-2 border rounded-md"
                />
              </div>
              <div style={{ marginBottom: 12 }}>
                <Switch
                  checked={printing}
                  onChange={(_, data) => setPrinting(data.checked)}
                  label="Printing"
                />
              </div>
              <div>
                <div className="!flex !flex-row !justify-between !items-center mb-2">
                  <label>Sizes:</label>
                  <label className="!text-xs !text-red-500">
                    add sizes in order you want them to be displayed
                  </label>
                </div>
                <div style={{ display: "flex", gap: 8, marginBottom: 8 }}>
                  <input
                    placeholder="Add size"
                    value={newSize}
                    onChange={(e) => setNewSize(e.target.value)}
                    className="flex p-2 border rounded-md"
                    style={{ flex: 1 }}
                  />
                  <button
                    className="bg-skin-primary p-1 px-3 text-white rounded-md"
                    onClick={handleAddSize}
                  >
                    Add Size Option
                  </button>
                </div>
                <ul className="space-y-2">
                  {sizes
                    .filter((s) => s)
                    .map((size, idx) => (
                      <li
                        key={size + idx}
                        className={`flex items-center justify-between bg-gray-100 rounded-md px-3 py-1 transition-shadow ${
                          draggedIdx === idx ? "ring-2 ring-skin-primary" : ""
                        }`}
                        draggable
                        onDragStart={() => handleDragStart(idx)}
                        onDragOver={(e) => handleDragOver(e, idx)}
                        onDrop={(e) => handleDrop(e, idx)}
                        onDragEnd={handleDragEnd}
                        style={{ cursor: "grab" }}
                      >
                        <span className="inline-block bg-skin-primary/10 text-skin-primary font-medium px-3 py-1 rounded-full text-xs">
                          {size}
                        </span>
                        <FluentButton
                          size="small"
                          appearance="subtle"
                          icon={<span className="fa fa-trash text-red-500" />}
                          onClick={() => handleRemoveSize(idx)}
                          className="ml-2"
                          aria-label="Remove size"
                        >
                          Remove
                        </FluentButton>
                      </li>
                    ))}
                </ul>
              </div>
            </DialogContent>
            <DialogActions>
              <FluentButton appearance="secondary" onClick={() => onOpenChange(null, { open: false })}>
                Cancel
              </FluentButton>
              <button
                className="bg-skin-primary text-white p-2 rounded-md"
                onClick={handleSubmit}
              >
                Add Product
              </button>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>
    </FluentProvider>
  );
};

export default AddProductDialog;