import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import LoginSectionSecure from '@/components/LoginSectionSecure';
import { useSecureAuth } from '@/utils/auth/useSecureAuth';
import { getSecureUserData, getCookieDataSecure } from '@/utils/getSecureUserData';

export default function TestSecureAuth() {
  const router = useRouter();
  const { user, isAuthenticated, isLoading, secureLogout } = useSecureAuth();
  const [testData, setTestData] = useState({});
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Run tests when authenticated and mounted
  useEffect(() => {
    if (isAuthenticated && user && isMounted) {
      runSecurityTests();
    }
  }, [isAuthenticated, user, isMounted]);

  const runSecurityTests = async () => {
    setIsRunningTests(true);
    
    try {
      const tests = {
        secureUserData: await getSecureUserData(),
        cookieUser: await getCookieDataSecure('user'),
        cookieTheme: await getCookieDataSecure('theme'),
        cookieCompany: await getCookieDataSecure('company'),
        localStorageCheck: checkLocalStorage(),
        documentCookieCheck: checkDocumentCookies(),
      };
      
      setTestData(tests);
    } catch (error) {
      console.error('Security tests failed:', error);
      setTestData({ error: error.message });
    } finally {
      setIsRunningTests(false);
    }
  };

  const checkLocalStorage = () => {
    if (!isMounted) return 'Loading...';
    
    const sensitiveKeys = ['superUser', 'id', 'name', 'role', 'email', 'token'];
    const foundKeys = [];
    
    sensitiveKeys.forEach(key => {
      const value = localStorage.getItem(key);
      if (value) {
        foundKeys.push({ key, hasValue: !!value, valueLength: value.length });
      }
    });
    
    return foundKeys.length > 0 ? foundKeys : 'Clean ✅';
  };

  const checkDocumentCookies = () => {
    if (!isMounted) return 'Loading...';
    
    const cookies = document.cookie;
    const sensitivePatterns = ['user=', 'token=', 'session='];
    const foundCookies = [];
    
    sensitivePatterns.forEach(pattern => {
      if (cookies.includes(pattern)) {
        foundCookies.push(pattern);
      }
    });
    
    return {
      allCookies: cookies.split(';').map(c => c.trim().split('=')[0]),
      sensitiveCookies: foundCookies,
      httpOnlyCookies: 'Cannot access HTTP-only cookies from JavaScript ✅'
    };
  };

  const handleLogout = async () => {
    await secureLogout();
    router.push('/test-secure-auth');
  };

  // Show loading state until mounted to prevent hydration mismatch
  if (!isMounted || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl">Loading secure authentication...</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-100">
        <div className="container mx-auto p-4">
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">
              🔒 Secure Authentication Test
            </h1>
            <p className="text-gray-600">
              This page tests our new secure authentication system
            </p>
          </div>
          
          <LoginSectionSecure />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto p-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold text-green-600">
              ✅ Authentication Successful!
            </h1>
            <button
              onClick={handleLogout}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg"
            >
              Logout
            </button>
          </div>

          {/* User Info */}
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h2 className="text-xl font-bold text-green-800 mb-3">User Information</h2>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <strong>Name:</strong> {user?.name || 'N/A'}
              </div>
              <div>
                <strong>Email:</strong> {user?.email || 'N/A'}
              </div>
              <div>
                <strong>ID:</strong> {user?.id || 'N/A'}
              </div>
              <div>
                <strong>Role:</strong> {user?.role || 'N/A'}
              </div>
            </div>
          </div>

          {/* Security Tests */}
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-800">Security Tests</h2>
            
            {isRunningTests ? (
              <div className="text-center py-4">Running security tests...</div>
            ) : (
              <div className="space-y-4">
                
                {/* localStorage Check */}
                <div className="p-4 border rounded-lg">
                  <h3 className="font-bold text-lg mb-2">localStorage Security Check</h3>
                  <div className="bg-gray-50 p-3 rounded text-sm">
                    <strong>Sensitive data found:</strong>
                    <pre className="mt-2">{JSON.stringify(testData.localStorageCheck, null, 2)}</pre>
                  </div>
                </div>

                {/* Document Cookies Check */}
                <div className="p-4 border rounded-lg">
                  <h3 className="font-bold text-lg mb-2">Cookie Security Check</h3>
                  <div className="bg-gray-50 p-3 rounded text-sm">
                    <pre>{JSON.stringify(testData.documentCookieCheck, null, 2)}</pre>
                  </div>
                </div>

                {/* Secure Data Access */}
                <div className="p-4 border rounded-lg">
                  <h3 className="font-bold text-lg mb-2">Secure Data Access</h3>
                  <div className="bg-gray-50 p-3 rounded text-sm">
                    <div className="mb-2">
                      <strong>Secure User Data:</strong> {testData.secureUserData ? '✅ Working' : '❌ Failed'}
                    </div>
                    <div className="mb-2">
                      <strong>Cookie Data (user):</strong> {testData.cookieUser ? '✅ Working' : '❌ Failed'}
                    </div>
                    <div className="mb-2">
                      <strong>Cookie Data (theme):</strong> {testData.cookieTheme ? '✅ Found' : 'ℹ️ None'}
                    </div>
                    <div>
                      <strong>Cookie Data (company):</strong> {testData.cookieCompany ? '✅ Found' : 'ℹ️ None'}
                    </div>
                  </div>
                </div>

                {/* Raw Test Data */}
                <details className="p-4 border rounded-lg">
                  <summary className="font-bold cursor-pointer">Raw Test Data</summary>
                  <div className="bg-gray-50 p-3 rounded text-xs mt-2">
                    <pre>{JSON.stringify(testData, null, 2)}</pre>
                  </div>
                </details>
              </div>
            )}
          </div>

          {/* Instructions */}
          <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-bold text-lg mb-2 text-blue-800">Test Instructions</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Check that localStorage is clean (no sensitive data)</li>
              <li>• Verify that document.cookie doesn't contain sensitive info</li>
              <li>• Confirm that secure data access works through our API</li>
              <li>• Test logout functionality</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
} 