WITH sales AS (
    SELECT
  orddetid,
  CONCAT_WS('|', cust_ref, ds.altfilid) AS ORD_ID,
  ordstatusdesc AS ORD_STATUS,                           
  delivery_date AS DEPOT_DATE,
  canc_flag AS IS_CANCELLED,
  mascode_desc as MASTER_PRODUCT_CODE,
  ds.altfilid AS ALTFILID,
  ds.countsize AS CASE_SIZE,
  ds.product_desc AS PRODUCT_DESCRIPTION,
  ds.delcustcode AS CUSTOMER,
  delcust_customer.hocustcode [HO Cust Code],
  cust_ref AS CUST_REF,
  CASE WHEN start_tc_qty = 0 THEN COALESCE(orig_qty, 0)
   ELSE cast(coalesce([start_tc_qty], 0) as int) END AS TC_ORDERED,
  COALESCE(shipped_qty_cases, 0) AS CASES_DELIVERED,
--   ds.new_line_flag,              
  ds.unit_price,
  ROW_NUMBER() over (partition by cust_ref, ds.altfilid order by shipped_qty_cases desc) as RNO
  
    FROM
        FLR_DEV_TEST_dps_BI_lookup.[dbo].vw_dps_service_lvl_tc_sales_additional ds
        JOIN FLR_DEV_TEST_dps_BI_lookup.[dbo].sales_dm_stage_altfil ALT ON ALT.altfilid = ds.altfilid
        JOIN FLR_DEV_TEST_dps_BI_lookup.[dbo].sales_dm_stage_customer delcust_customer on ds.delcustcode = delcust_customer.custcode
  LEFT JOIN FLR_DEV_TEST_dps_BI_lookup.[dbo].sales_dm_stage_product_lvl_grouping SPLG ON SPLG.prodnum = ds.product_number
     
    WHERE
        delcust_customer.hocustcode NOT IN ('PPACK', 'REJISS', 'SMOVE')
        AND ds.altfilid > 0
        AND Prov_Order_Flag = 0
        AND delivery_date >= @start_date
        AND delivery_date <= @end_date
        AND delcust_customer.category_no = 1 -- AND canc_flag = 0 cancelled products to be shown in the portal to add cancellation reasons
        AND deptcode = 1 --dps
  AND ordhed_type = 3
  AND (start_tc_qty <> 0 OR orig_qty <> 0 OR shipped_qty_cases <> 0)
  AND (
   @cust_code = 'All Customers'
   OR hocustcode = @cust_code
  )
--   AND (
--    @product = 'All Products'
--    OR mascode_desc = @product
--   )
),

CTE_TC_Difference AS (
 SELECT 
  CUST_REF,
  ALTFILID,
  MAX(TC_ORDERED) TC_ORDERED,
  SUM(CASES_DELIVERED) TC_DELIVERED

 FROM sales s
 GROUP BY CUST_REF, ALTFILID
),

CTE_Cases_Added_Reasons AS (
    SELECT
        s.ORD_ID,
        COALESCE(SUM(slr.quantity), 0) AS CASES_ADDED_REASONS
    FROM
        sales s
        LEFT JOIN [THL_Webapp_Portal].[dbo].[sl_reasons] slr ON slr.order_id COLLATE DATABASE_DEFAULT = s.ORD_ID COLLATE DATABASE_DEFAULT
        AND slr.is_deleted = 0
  --  WHERE
  --(
  -- @cust_code = 'All Customers'
  -- OR s.[HO Cust Code] = @cust_code
  --)
    GROUP BY
        s.ORD_ID
),

CTE_locks AS (
    SELECT
        DISTINCT custcode,
        order_id,
        [user_name],
        lock_attained_at
    FROM
        sl_locks
    WHERE
        lock_active = 1
),
CTE_calender AS (
    SELECT
        dps_fiscal_wk,
        startweek,
        endweek
    FROM
        FLR_DEV_TEST_dps_BI_lookup.[dbo].dps_tbl_cal_start_end_week
    WHERE
        calendar_name = 'Default'
  AND startweek >= '20231001'
),
CTE_Canc_Ord_Price as (
 SELECT DISTINCT
  dup_chk.orddetid,
  od.altfilid,
  dup_chk.uomnprice
 FROM (
  SELECT DISTINCT [orddetid],
   lag(orddetid) over (order by orddetid) prev_value,
   case when [orddetid] = lag(orddetid) over (order by orddetid) then 1 else 0 end duplicate_chk,
   [altfilid],
   max([uomnprice]) uomnprice
  FROM FLR_DEV_TEST_dps_BI_lookup.[dbo].[sales_dm_stage_dim_sales_audit_price]
  GROUP BY [orddetid],[altfilid]
  HAVING max([uomnprice]) > '0.02'

 ) AS dup_chk
 inner join FLR_DEV_TEST_dps_BI_lookup.[dbo].dps_procure_fact_orddet od on dup_chk.orddetid = od.orddetid and dup_chk.altfilid = od.altfilid
)

SELECT 
 s.IS_CANCELLED,
 0 as new_line_flag,
 s.ORD_ID,
 S.CUSTOMER,
 s.DEPOT_DATE,
 cal.dps_fiscal_wk AS FISCAL_WEEK,
 s.ORD_STATUS,
 s.CUST_REF,
 s.ALTFILID,
 s.CASE_SIZE,
 s.MASTER_PRODUCT_CODE,
 s.PRODUCT_DESCRIPTION,
 tcd.TC_ORDERED,
 tcd.TC_DELIVERED,
 abs(tcd.TC_ORDERED - tcd.TC_DELIVERED) as TC_DIFFERENCE,
 CASE
        WHEN tcd.TC_ORDERED > 0 
  THEN (cast(tcd.TC_DELIVERED as decimal(18,2)) / tcd.TC_ORDERED) * 100
        ELSE 0
    END AS SERVICE_LEVEL_PERCENT,
 cnr.CASES_ADDED_REASONS,
 case
  when IS_CANCELLED = 1 then COALESCE(uomnprice, 0)
  else unit_price
  end as UNIT_PRICE,
 CASE
  WHEN IS_CANCELLED = 1 THEN COALESCE(uomnprice, 0)
  ELSE unit_price
  END * abs(tcd.TC_ORDERED - tcd.TC_DELIVERED) AS TOTAL_VALUE,
 slr.id AS REASON_ID,
 slr.reason_id AS MAIN_REASON_ID,
 rmm.reason AS MAIN_REASON,
 slr.subreason_id AS SUB_REASON_ID,
 rms.reason AS SUB_REASON,
 slr.added_by AS REASON_ADDED_BY,
 slr.quantity AS REASON_QTY,
 slr.comment AS REASON_COMMENT,
 slr.updated_by AS REASON_UPDATED_BY,
 slr.[added_timestamp] AS REASON_ADDED_TIMESTAMP,
 slr.[updated_timestamp] as REASON_UPDATED_TIMESTAMP,
 l.[user_name] AS LOCKED_BY
FROM
    sales s
 LEFT JOIN CTE_calender cal ON s.DEPOT_DATE >= cal.startweek
  AND s.DEPOT_DATE <= cal.endweek
 LEFT JOIN CTE_Cases_Added_Reasons cnr ON cnr.ORD_ID = s.ORD_ID
 LEFT JOIN [THL_Webapp_Portal].[dbo].[sl_reasons] slr ON slr.order_id COLLATE DATABASE_DEFAULT = s.ORD_ID COLLATE DATABASE_DEFAULT
  AND slr.is_deleted = 0
 LEFT JOIN CTE_locks l ON l.custcode COLLATE DATABASE_DEFAULT = s.CUSTOMER COLLATE DATABASE_DEFAULT
  AND l.order_id COLLATE DATABASE_DEFAULT = s.ORD_ID COLLATE DATABASE_DEFAULT 
 LEFT JOIN [THL_Webapp_Portal].[dbo].[sl_reasons_master] rmm ON rmm.id = slr.reason_id
 LEFT JOIN [THL_Webapp_Portal].[dbo].[sl_reasons_master] rms ON rms.id = slr.subreason_id
 left join CTE_Canc_Ord_Price cop on s.orddetid = cop.orddetid
 LEFT JOIN CTE_TC_Difference tcd on tcd.altfilid = s.altfilid 
  AND tcd.cust_ref = s.cust_ref
WHERE 
 (
  @get_all = 1
  OR (
   @get_all = 0
   AND (
    abs(tcd.TC_ORDERED - tcd.TC_DELIVERED) - cnr.CASES_ADDED_REASONS <> 0
   )
  )
 )
--  AND (
--   @orderId is NULL
--   AND 1 = 1
--    OR (
--     @orderId is not NULL
--     AND s.ORD_ID = @orderId
--    )
--  )
 AND (tcd.TC_ORDERED - tcd.TC_DELIVERED) <> 0
 AND s.RNO = 1

ORDER BY 
    s.DEPOT_DATE,
    s.PRODUCT_DESCRIPTION,
    s.CUSTOMER;