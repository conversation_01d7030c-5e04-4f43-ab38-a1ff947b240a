SELECT
      reason.id,
      reason.order_id,
      reason.quantity,
      reason.reason_id,
      reason_master.reason as reason,
      reason.subreason_id,
      reason_master_sub.reason as sub_reason,
      reason.comment,
      reason.added_by,
      reason.is_deleted,
      reason.deleted_by,
      reason.delete_reason,
      reason.cust_code
FROM
      sl_reasons reason
      LEFT JOIN sl_reasons_master reason_master ON reason.reason_id = reason_master.id
      AND reason_master.is_active = 1
      LEFT JOIN sl_reasons_master reason_master_sub ON reason.subreason_id = reason_master_sub.id
      AND reason_master_sub.is_active = 1
where
      reason.is_deleted = 0
      and order_id = @orderId
order by
      id desc