-- DECLAR<PERSON> @change_launch_date DATETIME = '2023-12-15'
-- DECLARE @master_product_code_id INT = 12
-- DECLARE @existing_packaging_code INT = 101
-- DECLARE @end_customer INT = 5
-- DECLARE @packaging_name VARCHAR(100) = 'Eco-Friendly Boxhhhhhhhhhhhhhhhhhhh'
-- DECLARE @type_of_packaging INT = 3
-- DECLARE @type_of_material INT = 2
-- DECLARE @colour_of_material INT = 4
-- DECLARE @dimension_size VARCHAR(50) = '200x150x75'
-- DECLARE @component_weight VARCHAR(50) = '125'
-- DECLARE @recyclable_to_oprl INT = 1
-- DECLARE @paper_from_sustainable_forestry INT = 1
-- DECLARE @recyclable_content INT = 80
-- DECLARE @supplier VARCHAR(100) = 'EcoPack Solutions'
-- DECLARE @trading_business INT = 2
-- DECLARE @actionId INT = 1
-- DECLAR<PERSON> @actionedByEmail VARCHAR(100) = '<EMAIL>'
-- <PERSON><PERSON><PERSON><PERSON> @actionedByName VARCHAR(100) = 'Tester'
-- DECLARE @comment VARCHAR(100) = 'Initial draft of new eco-friendly packaging'
-- DECLARE @company VARCHAR(100) = 'dps'
BEGIN TRANSACTION;

BEGIN TRY DECLARE @last_request_number INT = 0;

SELECT
    @last_request_number = COALESCE(request_no, 0)
FROM
    product_request_no
WHERE
    product_type = 4;

DECLARE @padded_request_number NVARCHAR(6);

SET
    @padded_request_number = RIGHT(
        '000000' + CAST(@last_request_number + 1 AS VARCHAR),
        6
    );

INSERT INTO
    product_request_no (
        [company],
        [request_no],
        [product_type]
    ) OUTPUT INSERTED.request_no,
    INSERTED.company,
    INSERTED.id
VALUES
    (
        @company,
        @padded_request_number,
        4
    );

DECLARE @generatedRequestNo INT = SCOPE_IDENTITY();

INSERT INTO
    product_packaging_request (
        [request_no],
        [change_launch_date],
        [master_product_code_id],
        [existing_packaging_code],
        [end_customer],
        [packaging_name],
        [type_of_packaging],
        [type_of_material],
        [colour_of_material],
        [dimension_size(mm)],
        [component_weight(g)],
        [recyclable_to_oprl],
        [paper_from_sustainable_forestry],
        [recyclable_content],
        [supplier],
        [trading_business],
        [reason],
        [sub_product_code],
        [sub_product_code_label]
    )
VALUES
    (
        @generatedRequestNo,
        @change_launch_date,
        @master_product_code_id,
        @existing_packaging_code,
        @end_customer,
        @packaging_name,
        @type_of_packaging,
        @type_of_material,
        @colour_of_material,
        @dimension_size,
        @component_weight,
        @recyclable_to_oprl,
        @paper_from_sustainable_forestry,
        @recyclable_content,
        @supplier,
        @trading_business,
        @reason_for_request,
        @subProdCode,
        @subProdCodeLabel

    );

DECLARE @packagingRequestId INT = SCOPE_IDENTITY();

INSERT INTO
    product_packaging_status (
        packaging_request_id,
        action_id,
        actioned_by_email,
        actioned_by_name,
        comment
    )
VALUES
    (
        @packagingRequestId,
        @actionId,
        @actionedByEmail,
        @actionedByName,
        @comment
    );

COMMIT TRANSACTION;

PRINT 'Transaction committed.';

END 

TRY 

BEGIN CATCH 

ROLLBACK TRANSACTION;

PRINT 'Transaction rolled back.';

PRINT ERROR_MESSAGE();

    SELECT ERROR_MESSAGE() AS error;

END CATCH