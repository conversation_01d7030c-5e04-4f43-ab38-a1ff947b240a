// components/OverlaySpinner.js
import React from "react";
import { ThreeCircles } from "react-loader-spinner";
import { useLoading } from "./loadingContext";

const OverlaySpinner = () => {
  const { isLoading } = useLoading();
  return isLoading ? (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        height: "100vh",
        width: "100vw",
        position: "fixed",
        backgroundColor: "white",
        zIndex: 999999
      }}
    >
      <ThreeCircles
        color="#002D73"
        height={50}
        width={50}
        visible={isLoading}
        ariaLabel="oval-loading"
        secondaryColor="#0066FF"
        strokeWidth={2}
        strokeWidthSecondary={2}
      />
    </div>
  ) : (
    <></>
  );
};

export default OverlaySpinner;
