"use strict";
const express = require("express");
const config = require("./config");
const cors = require("cors");
const bodyParser = require("body-parser");
const cookieParser = require("cookie-parser");

// Import routes
const supplierRoutes = require("./routes/supplier");
const userRoutes = require("./routes/users");
const emailRoutes = require("./routes/email");
const productsRoutes = require("./routes/products");
const whatifRoutes = require("./routes/whatif");
const logData = require("./routes/logs");
const authRoutes = require("./routes/auth"); // Add this
const servicelevel = require("./routes/servicelevel");
const ppeConsumables = require("./routes/ppeComsumables");

const app = express();

const http = require("http");
const socket = require("./utils/socket");

const server = http.createServer(app);
const io = socket.init(server);

const corsOptions = {
  origin: [
    'http://localhost:3000',
    'http://localhost:3001', 
    'http://localhost:3002',
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Cookie'],
  exposedHeaders: ['Set-Cookie']
};

app.use(cors(corsOptions));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(cookieParser()); // Important: Add cookie parser

// Routes
app.use("/api/auth", authRoutes); // Add auth routes
app.use("/api/suppliers", supplierRoutes);
app.use("/api/users", userRoutes);
app.use("/api/email", emailRoutes);
app.use("/api/products", productsRoutes);
app.use("/api/whatif", whatifRoutes);
app.use("/api/logs", logData);
app.use("/api/servicelevel", servicelevel);
app.use("/api/ppe-consumables", ppeConsumables);


io.on("connection", (socket) => {
  console.log("A user connected");

  socket.on("disconnect", () => {
    console.log("User disconnected");
  });

  socket.on("message", (msg) => {
    console.log("Message received:", msg);
    io.emit("message", msg);
  });
});


server.listen(config.port, () =>
  console.log("Server is listening on http://localhost:" + config.port)
);