SELECT
    MIN([id]) AS [id],
    [supcode],
    MIN([name]) AS [name]
FROM
    [dbo].[supplier_iss]
WHERE
    [name] LIKE '%' + @value + '%'
GROUP BY
    [supcode]
UNION
SELECT
    s.[id],
    LTRIM(RTRIM(sp.[prophet_code])) AS supcode,
    LTRIM(RTRIM(s.[name]))
FROM
    [dbo].[supplier] s
    LEFT JOIN [dbo].[supplier_prophets] sp ON s.[id] = sp.[supplier_id]
WHERE
    [name] LIKE '%' + @value + '%' AND in_prophet=0