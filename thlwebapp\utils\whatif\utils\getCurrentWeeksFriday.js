function getCurrentWeeksFriday(dateString) {

    const inputDate = new Date(dateString);
    const currentDay = inputDate.getDay();
  
    // Calculate the number of days to add to get to Friday (5)
    const daysToAdd = (5 - currentDay + 7) % 7;
  
    const fridayDate = new Date(inputDate);
    fridayDate.setDate(inputDate.getDate() + daysToAdd);
  
    // Format the date as 'YYYY-MM-DD'
    const formattedFridayDate = fridayDate.toISOString().split('T')[0];
  
    return new Date(formattedFridayDate);
  }

  export {getCurrentWeeksFriday}