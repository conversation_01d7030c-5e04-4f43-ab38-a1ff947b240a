DECLARE @CurrentDate DATE;
DECLARE @CurrentQuarter INT;
DECLARE @currentWeek INT;
DECLARE @currentStartWeek DATE;

DECLARE @CurrentFinancialYear INT;
SET @CurrentFinancialYear = CASE 
    WHEN MONTH(GETDATE()) >= 10 THEN YEAR(GETDATE()) + 1
    ELSE YEAR(GETDATE())
END;

SET @CurrentDate = 
    CASE 
        WHEN @financialYear = @CurrentFinancialYear THEN GETDATE() -- Use current date if year is current
        ELSE CONVERT(DATE, CAST(@financialYear - 1 AS CHAR(4)) + '1001', 112) -- 1 Oct of the input year if it's a past year
    END;
    
SELECT
    @CurrentQuarter = fiscalquarter
FROM
    FLR_DEV_TEST_off_BI_lookup.dbo.off_cal_start_end_week
WHERE
    calendar_name = 'StdYearlyCalendar'
    AND fiscalyear = YEAR(@CurrentDate)
    AND @CurrentDate BETWEEN startweek
    AND endweek;

SELECT
    std.[fiscalweek],std.[fiscalquarter] std_fiscalquarter,offcal.fiscalquarter as fiscalquarter
FROM
    [FLR_DEV_TEST_off_BI_lookup].[dbo].[off_cal_start_end_week] std
    LEFT JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[off_cal_start_end_week] offcal ON std.off_fiscal_wk = offcal.fiscalweek
    AND std.fiscalyear = offcal.fiscalyear
    AND offcal.calendar_name = 'OFF Financial Calendar'
WHERE
    std.calendar_name = 'StdYearlyCalendar'
    AND std.fiscalyear = YEAR(@CurrentDate)
    AND std.fiscalquarter = @CurrentQuarter
ORDER BY
    offcal.fiscalquarter,
    std.fiscalweek;