import { useState, useEffect } from 'react';

export const usePermissions = (userData) => {
  const [permissions, setPermissions] = useState({
    isAdmin: false,
    canViewSuppliers: false,
    canEditSuppliers: false,
    canViewUsers: false,
    role_id: null,
    department_id: null
  });

  useEffect(() => {
    if (userData?.role_id) {
      const roleId = userData.role_id;
      
      setPermissions({
        isAdmin: roleId === 1,
        canViewSuppliers: [1, 2, 5, 6].includes(roleId),
        canEditSuppliers: [1, 2].includes(roleId),
        canViewUsers: [1].includes(roleId),
        role_id: roleId,
        department_id: userData.department_id
      });
    }
  }, [userData]);

  return permissions;
}; 