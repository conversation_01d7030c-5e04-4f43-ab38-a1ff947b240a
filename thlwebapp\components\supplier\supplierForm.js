import React, { useState, useEffect, Fragment, useRef } from "react";
import Layout from "@/components/Layout";
import users from "@/public/images/users.png";
import Image from "next/image";
import { useRouter } from "next/router";
import ProphetCode from "@/components/ProphetCode";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faTrash,
  faFloppyDisk,
  faXmark,
} from "@fortawesome/free-solid-svg-icons";
import Select from "react-select";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { ThreeCircles } from "react-loader-spinner";
import { logout } from "@/utils/secureStorage";
import DebouncedAutocomplete from "@/utils/DebouncedAutocomplete";
import { faInfo, faClose } from "@fortawesome/free-solid-svg-icons";
import { faPlus } from "@fortawesome/free-solid-svg-icons";
import { usePermissions } from "@/utils/rolePermissionsContext";
import { padEnd, set } from "lodash";
import { Dialog, Transition } from "@headlessui/react";
import { useLoading } from "@/utils/loaders/loadingContext";
import { titleCase } from "@/utils/titlCase";

const SupplierForm = ({ userData }) => {
  const router = useRouter();
  const { id, edit } = router.query;
  const apiBase =
    process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8081";
  const [supplierName, setSupplierName] = useState("");
  const [supplierNameChange, setSupplierNameChange] = useState(false);
  const [prophets, setProphets] = useState([]);
  const [prophetCodesDPS, setProphetCodesDPS] = useState("");
  const [fetchedProphetCodesDPS, setFetchedProphetCodesDPS] = useState("");
  const [prophetCodesDPSMS, setProphetCodesDPSMS] = useState("");
  const [fetchedProphetCodesDPSMS, setFetchedProphetCodesDPSMS] = useState("");
  const [prophetCodesEFC, setProphetCodesEFC] = useState("");
  const [fetchedProphetCodesEFC, setFetchedProphetCodesEFC] = useState("");
  const [prophetCodesFPP, setProphetCodesFPP] = useState("");
  const [fetchedProphetCodesFPP, setFetchedProphetCodesFPP] = useState("");
  const [supplierRoles, setSupplierRoles] = useState([]);
  const [urgentRequest, setUrgentRequest] = useState(false);
  const [gdprCompliant, setGdprCompliant] = useState(false);
  const [produceSupplier, setProduceSupplier] = useState(false);
  const [sendacGroups, setSendacGroups] = useState([]);
  const [isInactiveGroup, setIsInactiveGroup] = useState(false);
  const [supplierLinks, setSupplierLinks] = useState([]);
  const [sendacGroup, setSendacGroup] = useState("");
  const [isVisibleSendacGroup, setIsVisibleSendacGroup] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [data, setData] = useState([]);
  const [supplierId, setSupplierId] = useState(0);
  const [isProphetChecked, setIsProphetChecked] = useState([]);
  const [roles, setRoles] = useState([]);
  const [sendacGroupList, setGroupList] = useState([]);
  const [sendacAllGroupList, setGroupAllList] = useState([]);
  const [linkList, setLinkList] = useState([]);
  const [linkListByRole, setLinkListByRole] = useState([]);
  const [userId, setUserId] = useState(0);
  const [currency, setCurrency] = useState("");
  const [countError, setErrorCount] = useState(0);
  //allerrorStates //
  const [editing, setEditing] = useState(false);
  const [isValidName, setValidName] = useState(true);
  const [isValidSupplierByProphet, setValidSupplierByProphet] = useState(true);
  const [isValidProphets, setValidProphets] = useState(true);
  const [isValidRoles, setValidRoles] = useState(true);
  const [isValidSandacGroup, setValidSandacGroup] = useState(true);
  const [isValidGroup, setIsValidGroup] = useState(true);
  const [isValidLinks, setValidLinks] = useState(true);
  const [isCommonError, setCommonError] = useState("");
  const [loading, setLoading] = useState(false);
  const { setIsLoading } = useLoading();
  const [isProdSupDisabled, setProdSupDisabled] = useState(false);
  //const alphaNumericRegex = /^([a-zA-Z0-9 ]+)$/;
  const alphaNumericRegex = /^[A-Za-z? ,_-]{0,50}$/;
  const [selectedCheckboxes, setSelectedCheckboxes] = useState([]);
  const [visibleLinks, setVisibleLinks] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [newSendacGroupSelected, setNewSendacGroupSelected] = useState([]);
  const [isAddingNewSendac, setIsAddingNewSendac] = useState(false);
  const [isDisabledDPS, setIsDisabledDPS] = useState(false);
  const [isDisabledDPSMS, setIsDisabledDPSMS] = useState(false);
  const [isDisabledEFC, setIsDisabledEFC] = useState(false);
  const [isDisabledFPP, setIsDisabledFPP] = useState(false);
  const [initialFormValues, setInitialFormValues] = useState({});
  const [initiallySupplierLinksExist, setInitiallySupplierLinksExist] =
    useState(false);
  const [isNewSendacGroup, setIsNewSendacGroup] = useState(false);
  const [saveDataMoveOrRemoveLinks, setSaveDataMoveOrRemoveLinks] = useState(
    []
  );
  const [status, setStatus] = useState(null);
  const [supplierLinksChanged, setSupplierLinksChanges] = useState(false);
  const [formChange, setFormChange] = useState(false);
  const [clearedLinks, setClearedLinks] = useState([]);
  const [prophetDPSUnique, setProphetDPSUnique] = useState(true);
  const [prophetDPSMSUnique, setProphetDPSMSUnique] = useState(true);
  const [prophetEFCUnique, setProphetEFCUnique] = useState(true);
  const [newProphets, setNewProphets] = useState([]);
  const [prophetFPPUnique, setProphetFPPUnique] = useState(true);
  const [prophetChange, setProphetChange] = useState(false);
  const [symbol, setSymbol] = useState("");
  const [mandatorySendacDPS, setMandatorySendacDPS] = useState(false);
  const [mandatorySendacDPSMS, setMandatorySendacDPSMS] = useState(false);
  const [mandatorySendacFPP, setMandatorySendacFPP] = useState(false);
  const [mandatorySendacEFC, setMandatorySendacEFC] = useState(false);
  const [isProphetCodeUnique, setIsProphetCodeUnique] = useState(true);
  const [prophetCodeNum, setProphetCodeNum] = useState("");
  const [isDPSProphetCodeValid, setIsDPSProphetCodeValid] = useState(true);
  const [isDPSMSProphetCodeValid, setIsDPSMSProphetCodeValid] = useState(true);
  const [isEFCProphetCodeValid, setIsEFCProphetCodeValid] = useState(true);
  const [isFPPProphetCodeValid, setIsFPPProphetCodeValid] = useState(true);
  const [isCodeSystemGenerated, setIsCodeSystemGenerated] = useState(true);
  const [isCurrencyMandatory, setIsCurrencyMandatory] = useState(false);
  const [prophetCodeValidationMessage, setIsProphetCodeValidtionMessage] =
    useState("");
  const [supplierType, setSupplierType] = useState([]);

  const handleSessionError = async () => {
    toast.error("Your session has expired. Please log in again.");
    setTimeout(async () => {
      await logout();
      router.push("/login");
    }, 3000);
  };

  useEffect(() => {
    if (
      ((supplierRoles && supplierRoles?.includes(1)) ||
        supplierRoles?.includes(6)) &&
      urgentRequest &&
      !currency
    ) {
      setIsCurrencyMandatory(true);
      setIsProphetCodeValidtionMessage("Invalid code - Missing currency.");
    } else {
      setIsCurrencyMandatory(false);
    }
  }, [supplierRoles, urgentRequest]);

  const closeModal = (e) => {
    if (e) {
      e.preventDefault();
    }
    setIsOpen(false);
  };

  useEffect(() => {
    if (
      prophetCodesDPSMS &&
      supplierRoles?.length > 0 &&
      (supplierRoles?.includes(1) || supplierRoles?.includes(6))
    ) {
      let actualCurr = currency;
      let displayCurr = actualCurr; // Use this for display purposes
      actualCurr = actualCurr == "$" ? `\\${actualCurr}` : actualCurr;
      const regexPattern = new RegExp(`^([A-Z]{4})9${actualCurr}$`);
      const isValid = regexPattern.test(prophetCodesDPSMS);

      if (!isValid) {
        if (!actualCurr) {
          setIsProphetCodeValidtionMessage(
            `No currency found. Please select a currency from Finance section.`
          );
        } else if (prophetCodesDPSMS.length < 6) {
          setIsProphetCodeValidtionMessage(
            `The prophet code must be 6 characters long, including the currency code '${displayCurr}'.`
          );
        } else if (!/^[A-Z0]{4}/.test(prophetCodesDPSMS.slice(0, 4))) {
          setIsProphetCodeValidtionMessage(
            "First 4 characters must be uppercase letters."
          );
        } else if (prophetCodesDPSMS[4] !== "9") {
          setIsProphetCodeValidtionMessage("Fifth character must be digit 9.");
        } else if (actualCurr && !prophetCodesDPSMS.endsWith(actualCurr)) {
          setIsProphetCodeValidtionMessage(
            `Must end with the currency code '${displayCurr}'.`
          );
        } else if (prophetCodesDPSMS.length > 6) {
          setIsProphetCodeValidtionMessage(
            "The prophet code contains extra characters beyond the expected format."
          );
        } else {
          setIsProphetCodeValidtionMessage(
            "The prophet code format is invalid. Please follow the expected structure."
          );
        }
      } else {
        setIsProphetCodeValidtionMessage("");
      }

      setIsDPSMSProphetCodeValid(isValid);
    } else if (
      ADCompanyName == "DPS MS" &&
      supplierRoles?.length > 0 &&
      (!supplierRoles?.includes(1) || !supplierRoles?.includes(6))
    ) {
      setIsDPSMSProphetCodeValid(true);
      setIsProphetCodeValidtionMessage("");
    }
  }, [prophetCodesDPSMS, currency, supplierRoles, urgentRequest]);

  useEffect(() => {
    if (
      prophetCodesDPS &&
      supplierRoles?.length > 0 &&
      (supplierRoles?.includes(1) || supplierRoles?.includes(6))
    ) {
      let actualCurr = currency;
      let displayCurr = actualCurr; // Use this for display purposes
      actualCurr = actualCurr == "$" ? `\\${actualCurr}` : actualCurr;
      let isValid = true;
      if (actualCurr) {
        const regexPattern = new RegExp(`^[A-Z]{5}${actualCurr}$`);
        isValid = regexPattern.test(prophetCodesDPS);
      } else {
        isValid = false;
      }

      if (!isValid) {
        if (!actualCurr) {
          setIsProphetCodeValidtionMessage(
            `No currency found. Please select a currency from Finance section.`
          );
        } else if (prophetCodesDPS.length < 5) {
          setIsProphetCodeValidtionMessage(
            "The prophet code must contain 5 uppercase letters + currency."
          );
        } else if (!/^[A-Z]+$/.test(prophetCodesDPS.slice(0, 5))) {
          setIsProphetCodeValidtionMessage(
            "The first 5 characters must be uppercase letters (A-Z)."
          );
        } else if (actualCurr && !prophetCodesDPS.endsWith(actualCurr)) {
          setIsProphetCodeValidtionMessage(
            `The prophet code must end with the currency code '${displayCurr}'.`
          );
        } else if (prophetCodesDPS.length > 6) {
          setIsProphetCodeValidtionMessage(
            "The prophet code contains extra characters beyond the expected length."
          );
        } else {
          setIsProphetCodeValidtionMessage(
            "The prophet code format is invalid. Please follow the expected structure."
          );
        }
      } else {
        setIsProphetCodeValidtionMessage("");
      }

      setIsDPSProphetCodeValid(isValid);
    } else if (
      (ADCompanyName == "DPS" || ADCompanyName == "Direct Produce Supplies") &&
      supplierRoles?.length > 0 &&
      (!supplierRoles?.includes(1) || !supplierRoles?.includes(6))
    ) {
      setIsDPSProphetCodeValid(true);
      setIsProphetCodeValidtionMessage("");
    }
  }, [prophetCodesDPS, currency, supplierRoles, urgentRequest]);

  useEffect(() => {
    if (
      prophetCodesEFC &&
      supplierRoles?.length > 0 &&
      (supplierRoles?.includes(1) || supplierRoles?.includes(6))
    ) {
      let regexPattern = new RegExp(`^[A-Z0-9]{4}[A-Z01345678][A-Z0-9]*$`);
      const isValid =
        regexPattern.test(prophetCodesEFC) && prophetCodesEFC.length === 6;

      if (!isValid) {
        if (prophetCodesEFC.length !== 6) {
          setIsProphetCodeValidtionMessage(
            "The prophet code must be exactly 6 characters long."
          );
        } else if (!/^[A-Z0-9]+$/.test(prophetCodesEFC)) {
          setIsProphetCodeValidtionMessage(
            "The prophet code can only contain uppercase letters and numbers."
          );
        } else if (!/^[A-Z0-9]{4}[A-Z01345678]/.test(prophetCodesEFC)) {
          setIsProphetCodeValidtionMessage(
            "The fifth character cannot be 2 or 9."
          );
        } else {
          setIsProphetCodeValidtionMessage("Invalid prophet code format.");
        }
      } else {
        setIsProphetCodeValidtionMessage("");
      }

      setIsEFCProphetCodeValid(isValid);
    } else if (
      ADCompanyName == "EFC" &&
      supplierRoles?.length > 0 &&
      (!supplierRoles?.includes(1) || !supplierRoles?.includes(6))
    ) {
      setIsEFCProphetCodeValid(true);
      setIsProphetCodeValidtionMessage("");
    }
  }, [prophetCodesEFC, supplierRoles, urgentRequest]);

  useEffect(() => {
    if (
      prophetCodesFPP &&
      supplierRoles?.length > 0 &&
      (supplierRoles?.includes(1) || supplierRoles?.includes(6))
    ) {
      let actualCurr = currency;
      let displayCurr = actualCurr;
      let isValid = true;
      actualCurr = actualCurr == "$" ? `\\${actualCurr}` : actualCurr;
      let regexPattern;
      actualCurr = actualCurr == "$" ? `\\${actualCurr}` : actualCurr;

      if (actualCurr) {
        regexPattern = new RegExp(`^([A-Z0]{4})2${actualCurr}$`);
        isValid = regexPattern.test(prophetCodesFPP);
      } else {
        isValid = false;
      }

      if (!isValid) {
        if (!actualCurr) {
          setIsProphetCodeValidtionMessage(
            `No currency found. Please select a currency from Finance section.`
          );
        } else if (prophetCodesFPP.length < 5 + actualCurr.length) {
          setIsProphetCodeValidtionMessage(
            `The prophet code must be 6 characters long, including the currency code '${displayCurr}'.`
          );
        } else if (!/^[A-Z0]{4}/.test(prophetCodesFPP.slice(0, 4))) {
          setIsProphetCodeValidtionMessage(
            "The first 4 characters of the prophet code must be uppercase letters (A-Z)."
          );
        } else if (prophetCodesFPP[4] !== "9") {
          setIsProphetCodeValidtionMessage(
            "The fifth character of the prophet code must be the digit '2'."
          );
        } else if (actualCurr && !prophetCodesFPP.endsWith(actualCurr)) {
          setIsProphetCodeValidtionMessage(
            `The prophet code must end with the currency code '${displayCurr}'.`
          );
        } else if (prophetCodesFPP.length > 6) {
          setIsProphetCodeValidtionMessage(
            "The prophet code contains extra characters beyond the expected format."
          );
        } else {
          setIsProphetCodeValidtionMessage(
            "The prophet code format is invalid. Please follow the expected structure."
          );
        }
      } else {
        setIsProphetCodeValidtionMessage("");
      }

      setIsFPPProphetCodeValid(isValid);
    } else if (
      ADCompanyName == "FPP" &&
      supplierRoles?.length > 0 &&
      (!supplierRoles?.includes(1) || !supplierRoles?.includes(6))
    ) {
      setIsFPPProphetCodeValid(true);
      setIsProphetCodeValidtionMessage("");
    }
  }, [prophetCodesFPP, currency, supplierRoles, urgentRequest]);

  useEffect(() => {
    if (prophetCodeNum == 1) {
      setProphetDPSUnique(isProphetCodeUnique);
    } else if (prophetCodeNum == 2) {
      setProphetDPSMSUnique(isProphetCodeUnique);
    } else if (prophetCodeNum == 3) {
      setProphetEFCUnique(isProphetCodeUnique);
    } else if (prophetCodeNum == 4) {
      setProphetFPPUnique(isProphetCodeUnique);
    }
  }, [isProphetCodeUnique]);

  let duplicateFound = false;
  const [ADCompanyName, setADCompanyName] = useState("");
  useEffect(() => {
    if (typeof window !== "undefined") {
      setEditing(localStorage.getItem("isEdit"));
    }
    const domain = userData?.company || "";

    const { supplierId } = router.query;
    const isEditRoute = router.asPath?.includes("/edit");
    let ADCompanyNameData =
      userData?.ADCompanyName || userData?.companyName || "";
    if (ADCompanyNameData == "Fresh Produce Partners") {
      ADCompanyNameData = "FPP";
    } else if (ADCompanyNameData == "Ethical Food Company") {
      ADCompanyNameData = "EFC";
    } else if (ADCompanyNameData == "Direct Produce Supplies") {
      ADCompanyNameData = "DPS";
    } else if (
      ADCompanyNameData == "FLR Spectron Ltd" ||
      ADCompanyNameData == "THL" ||
      ADCompanyNameData == "Integrated Service Solutions Ltd" ||
      ADCompanyNameData == "DPS MS"
    ) {
      ADCompanyNameData = "DPS MS";
    }
    setADCompanyName(ADCompanyNameData);

    if (!isEditRoute) {
      if (ADCompanyNameData == "DPS") {
        setMandatorySendacDPS(true);
        if (!formChange) {
          setProphets([...prophets, [1, "", ""]]);
          fetchSendacGroupByProphet([[1, null, null]]);
        }
      } else if (ADCompanyNameData == "EFC") {
        setMandatorySendacEFC(false);
        if (!formChange) {
          setProphets([...prophets, [3, "", ""]]);
          fetchSendacGroupByProphet([[3, null, null]]);
        }
      } else if (ADCompanyNameData == "FPP") {
        setMandatorySendacFPP(false);
        if (!formChange) {
          setProphets([...prophets, [4, "", ""]]);
          fetchSendacGroupByProphet([[4, null, null]]);
        }
      } else if (ADCompanyNameData == "DPS MS") {
        setMandatorySendacDPSMS(true);
        if (!formChange) {
          setProphets([...prophets, [2, "", ""]]);
          fetchSendacGroupByProphet([[2, null, null]]);
        }
      } else {
        setMandatorySendacDPSMS(true);
        if (!formChange) {
          setProphets([...prophets, [2, "", ""]]);
          fetchSendacGroupByProphet([[2, null, null]]);
        }
      }
    }

    setSupplierId(supplierId);
    if (isEditRoute) {
      setIsEdit(true);
    }
    if (supplierId) {
      setLoading(true);
      fetch(`${apiBase}/api/suppliers/get-supplier-by-id/${supplierId}`, {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        credentials: "include", // Use session authentication
      })
        .then(async (res) => {
          if (res.status === 400) {
            toast.error(
              "There was an error with your request. Please check your data and try again."
            );
          } else if (res.status === 401) {
            await handleSessionError();
            return;
          }
          if (res.status === 200) {
            return res.json();
          }
          return Promise.reject(res);
        })
        .then((json) => {
          if (json?.length > 0) {
            setLoading(false);
            const res = json[0];
            setStatus(res?.status);
            setSymbol(res?.symbol);
            setIsCodeSystemGenerated(res?.is_code_system_generated);
            let initialFormValues = {
              supplierName: res?.name ?? "",
              emergency_request: res?.emergency_request ?? false,
              gdpr_compliant: res?.gdpr_compliant ?? false,
              product_supplier: res?.product_supplier ?? false,
              sendac_groups: res?.supplier_sendac_group_json
                ? JSON.parse(res.supplier_sendac_group_json)[0]
                : undefined,
              roles: JSON.parse(res?.role_ids)?.map((role) => role?.role_id),
              supplier_links: JSON.parse(res?.supplier_links_json),
              status: res?.status,
            };

            setSelectedSupplierType({
              value: res?.supplier_type_id,
              label: res?.supplier_type_label,
            });

            setInitialFormValues(initialFormValues);

            if (res?.role_ids) {
              const roles = JSON.parse(res?.role_ids ?? "[]");
              const role_ids = roles?.map((item) => item?.role_id);
              if (role_ids?.includes(3) || role_ids?.includes(4)) {
                setProdSupDisabled(true);
              } else {
                setProdSupDisabled(false);
              }
              setSupplierRoles(role_ids);
            } else {
              setSupplierRoles([]);
            }

            if (res?.prophets_id_code) {
              const prophetsL = res?.prophets_id_code
                ? JSON.parse(res?.prophets_id_code)
                : [];
              const prophet_data = prophetsL?.map((item) => {
                return [
                  item?.prophet_id,
                  item?.prophet_code?.trim(),
                  item?.code_count,
                ];
              });

              prophet_data?.map((item) => {
                if (item[0] == 1) {
                  setProphetCodeNum(1);
                  setMandatorySendacDPS(true);
                  setProphetCodesDPS(item[1]?.trim());
                  setFetchedProphetCodesDPS(item[1]?.trim());
                  // if (item[2] > 1) {
                  //   setProphetDPSUnique(false);
                  // }
                  setADCompanyName("DPS");
                }
                if (item[0] == 2) {
                  setProphetCodeNum(2);
                  setMandatorySendacDPSMS(true);
                  setProphetCodesDPSMS(item[1]?.trim());
                  setFetchedProphetCodesDPSMS(item[1]?.trim());
                  // if (item[2] > 1) {
                  //   setProphetDPSMSUnique(false);
                  // }
                  setADCompanyName("DPS MS");
                }
                if (item[0] == 3) {
                  setProphetCodeNum(3);
                  setProphetCodesEFC(item[1]?.trim());
                  setFetchedProphetCodesEFC(item[1]?.trim());
                  setMandatorySendacEFC(false);
                  // if (item[2] > 1) {
                  //   setProphetEFCUnique(false);
                  // }
                  setADCompanyName("EFC");
                }
                if (item[0] == 4) {
                  setADCompanyName("FPP");
                  setProphetCodeNum(4);
                  setProphetCodesFPP(item[1]?.trim());
                  setMandatorySendacFPP(false);
                  setFetchedProphetCodesFPP(item[1]?.trim());
                  // if (item[2] > 1) {
                  //   setProphetFPPUnique(false);
                  // }
                }
              });
              setProphets(prophet_data);
              // Store prophet data in localStorage instead of cookies
              if (typeof window !== "undefined") {
                localStorage.setItem("prophet", prophet_data[0][0]);
              }

              const isEditProphetId = prophet_data?.map((item) => {
                return item[0];
              });
              setIsProphetChecked(isEditProphetId);

              fetchSendacGroupByProphet(prophet_data);
            }

            let sendacValue = "";
            if (res?.supplier_sendac_group_json) {
              const sandacGr = JSON.parse(res?.supplier_sendac_group_json);

              const group_data = sandacGr?.map((item) => {
                if (item?.value) {
                  sendacValue = item?.value + " - ";
                } else {
                  sendacValue = "TBC - ";
                }
                return {
                  value: item?.group_id,
                  label: sendacValue + item?.label,
                };
              });

              if (group_data) {
                fetch(`${apiBase}/api/suppliers/get-supplier-links/${domain}`, {
                  method: "GET",
                  headers: {
                    Accept: "application/json",
                    "Content-Type": "application/json",
                  },
                  credentials: "include", // Use session authentication
                })
                  .then((res) => {
                    if (res.status === 400) {
                      console.error("Error");
                    }
                    if (res.status === 200) {
                      return res.json();
                    }
                    return Promise.reject(res);
                  })
                  .then((res) => {
                    if (res?.length > 0) {
                      setInitiallySupplierLinksExist(true);
                      const roleMap = new Map();
                      res?.forEach((item) => {
                        if (!roleMap.has(item?.name)) {
                          roleMap.set(item?.name, {
                            id: item?.id,
                            role_id: [],
                            roles: [],
                          });
                        }
                        roleMap.get(item?.name)?.roles.push(item?.role_names);
                        roleMap.get(item?.name)?.role_id.push(item?.role_id);
                      });

                      const result = [...roleMap.entries()].map(
                        ([name, { id, role_id, roles }]) => ({
                          id,
                          name,
                          role_id,
                          role_names: roles.join(" , "),
                        })
                      );

                      const formattedLinks = result?.map((row) => {
                        return {
                          value: row?.id,
                          label:
                            row?.name + " ( " + row?.role_names.trim() + ")",
                          role_id: row?.role_id,
                        };
                      });

                      setLinkListByRole(
                        formattedLinks.filter(
                          (item) => item?.value != supplierId
                        )
                      );
                    }
                  })
                  .catch((error) => {
                    // toast.error(error.message, {
                    //   position: "top-right",
                    // });
                  });
              }
              //setIsSandacSelected(group_data);
              setSendacGroups(group_data);
            }

            if (res?.supplier_links_json) {
              const supplierLinkParse = JSON.parse(res?.supplier_links_json);

              const roleMap = new Map();
              supplierLinkParse?.forEach((item) => {
                if (!roleMap.has(item?.name)) {
                  roleMap.set(item?.name, {
                    id: item?.supplier_id,
                    role_id: [],
                    roles: [],
                  });
                }
                roleMap.get(item?.name)?.roles.push(item?.role_names);
                roleMap.get(item?.name)?.role_id.push(item?.role_id);
              });

              const result = [...roleMap.entries()].map(
                ([name, { id, role_id, roles }]) => ({
                  id,
                  name,
                  role_id,
                  role_names: roles.join(" , "),
                })
              );

              const link_data = result?.map((row) => {
                return {
                  value: row?.id,
                  label: row?.name + " ( " + row?.role_names.trim() + ")",
                  role_id: row?.role_id,
                };
              });

              //setIsSupplierLinkSelected(link_data);
              setSupplierLinks(
                link_data.filter((item) => item?.value != supplierId)
              );
            }

            setData(res);
            setSupplierName(res?.name);
            setUrgentRequest(res?.emergency_request);
            setGdprCompliant(res?.gdpr_compliant);
            setProduceSupplier(res?.product_supplier);
            setCurrency(res?.symbol);
          }
        })
        .catch((error) => {
          console.error(error);
          setLoading(false);
          toast.error(
            `Failed to fetch supplier data with message : ${error.message}`,
            {
              position: "top-right",
            }
          );
          return;
        });
    }
    setIsLoading(false);
  }, [router.query, userData]);

  async function getData(param) {
    let serverAddress = apiBase;

    try {
      const res = await fetch(`${serverAddress}/api/suppliers/${param}`, {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        credentials: "include", // Use session authentication
      });

      if (res.status === 400) {
        toast.error(
          "There was an error with your request. Please check your data and try again."
        );
      } else if (res.status === 401) {
        await handleSessionError();
        return;
      }
      if (res.status === 200) {
        return res.json();
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  }
  useEffect(() => {
    const rolesRoutePath = "get-supplier-roles";
    getData(rolesRoutePath).then((data) => {
      setRoles(data);
    });
    const supplierTypeRoutePath = "get-supplier-types";
    getData(supplierTypeRoutePath).then((data) => {
      const supplierTypes = data.map((item) => {
        return {
          value: item?.id,
          label: item?.supplier_type,
        };
      });
      setSupplierType(supplierTypes);
    });
  }, [0]);

  let error = 0;
  const handleValidationChange = () => {
    if (!supplierName || supplierName?.length > 50) {
      setValidName(false);
      return false;
    } else {
      setValidName(true);
    }

    // if (prophets.length == 0) {
    //   setValidProphets(false);
    //   return false;
    // } else {
    //   setValidProphets(true);
    //   //return true;
    // }

    if (prophets?.length == 0) {
      setValidProphets(false);
      return false;
    }

    if (supplierRoles == null || supplierRoles?.length == 0) {
      setValidRoles(false);
      return false;
    } else {
      setValidRoles(true);
      //return true;
    }

    if (isVisibleSendacGroup) {
      if (!sendacGroup) {
        setIsValidGroup(false);
        return false;
      } else {
        setIsValidGroup(true);
      }
    } else {
      if (
        sendacGroups?.length == 0 &&
        (mandatorySendacDPS ||
          mandatorySendacDPSMS ||
          mandatorySendacFPP ||
          mandatorySendacEFC)
      ) {
        setValidSandacGroup(false);
        return false;
      } else {
        setValidSandacGroup(true);
      }
    }

    return true;
  };

  const fetchSupplierLinksByProphet = (data) => {
    const prophetData = data;
    const prophets = prophetData.map((item) => {
      return item[0];
    });
    fetch(`${apiBase}/api/suppliers/get-link-by-prophets/${prophets[0]}`, {
      method: "GET",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      credentials: "include", // Use session authentication
    })
      .then((res) => {
        if (res.status === 200) {
          return res.json();
        }
      })
      .then((json) => {
        const res = json;
        if (res?.length > 0) {
          const roleMap = new Map();
          res?.forEach((item) => {
            if (!roleMap.has(item?.name)) {
              roleMap.set(item?.name, { id: item?.id, role_id: [], roles: [] });
            }
            roleMap.get(item?.name)?.roles.push(item?.role_names);
            roleMap.get(item?.name)?.role_id.push(item?.role_id);
          });

          const result = [...roleMap.entries()].map(
            ([name, { id, role_id, roles }]) => ({
              id,
              name,
              role_id,
              role_names: roles.join(" , "),
            })
          );

          const formattedData = result?.map((row) => {
            return {
              value: row?.id,
              label: row?.name + " ( " + row?.role_names.trim() + ")",
              role_id: row?.role_id,
            };
          });
          setLinkListByRole(
            formattedData.filter((item) => item?.value != supplierId)
          );
        } else {
          setLinkListByRole([]);
        }
      })
      .catch((error) => {
        return error.message;
      });
  };

  const fetchSendacGroupByProphet = (data) => {
    const prophetData = data;

    const prophets = prophetData?.map((item) => {
      return item[0];
    });

    fetch(`${apiBase}/api/suppliers/get-sendac-group-by-prophets`, {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      credentials: "include", // Use session authentication
      body: JSON.stringify(prophets),
    })
      .then((res) => {
        if (res.status === 200) {
          return res.json();
        }
      })
      .then((json) => {
        const res = json;
        if (res?.length > 0) {
          const formattedGroupList = res?.map((item) => {
            return {
              value: item?.id,
              group_id: item?.value,
              is_active: item?.is_active,
              label:
                (item?.value ? item?.value + " - " : "TBC - ") + item?.label,
            };
          });
          const formattedActiveGroupList = res
            ?.filter((item) => item?.is_active === true)
            .map((item) => {
              return {
                value: item?.id,
                group_id: item?.value,
                is_active: item?.is_active,
                label:
                  (item?.value ? item?.value + " - " : "TBC - ") + item?.label,
              };
            });

          setGroupList(formattedActiveGroupList);
          setGroupAllList(formattedGroupList);
        } else {
          setGroupList([]);
          setGroupAllList([]);
        }
      })
      .catch((error) => {
        return error.message;
      });
  };

  // const handleRoleChange = (value, checked) => {
  //   if (checked) {
  //     if (value == 2 || value == 3) {
  //       setProduceSupplier(true);
  //       setProdSupDisabled(true);
  //     }

  //     setSupplierRoles([...supplierRoles, parseInt(value)]);
  //     setValidRoles(true);
  //   } else {
  //     if (supplierRoles?.includes(2) || supplierRoles?.includes(3)) {
  //       setProduceSupplier(true);
  //     } else {
  //       setProduceSupplier(false);
  //     }

  //     setSupplierRoles(
  //       supplierRoles.filter((item) => item !== parseInt(value))
  //     );
  //   }
  // };

  const handleRoleChange = (value, checked) => {
    // Role to supplier type ID mapping
    const roleToSupplierType = {
      1: 2, // Supplier account -> GOODS
      2: 3, // Producer -> GROWE
      3: 3, // Grower -> GROWE
      4: 4, // Packhouse -> PACKH
      5: 1, // Haulier -> CHARG
      6: 1, // Expense -> CHARG
    };

    let updatedRoles;
    if (checked) {
      if (value == 2 || value == 3) {
        setProduceSupplier(true);
        setProdSupDisabled(true);
      }
      updatedRoles = [...supplierRoles, parseInt(value)];
      setSupplierRoles(updatedRoles);
      setValidRoles(true);
    } else {
      updatedRoles = supplierRoles.filter((item) => item !== parseInt(value));
      if (updatedRoles.some((role) => role === 2 || role === 3)) {
        setProduceSupplier(true);
      } else {
        setProduceSupplier(false);
      }
      setSupplierRoles(updatedRoles);
    }

    // Determine supplier type based on precedence
    const precedenceOrder = [2, 3, 4, 1]; // Precedence order based on supplier type IDs
    const highestPrecedenceRole = updatedRoles.sort(
      (a, b) =>
        precedenceOrder.indexOf(roleToSupplierType[a]) -
        precedenceOrder.indexOf(roleToSupplierType[b])
    )[0];

    // Set supplier type based on highest precedence role
    if (highestPrecedenceRole) {
      const supplierTypeId = roleToSupplierType[highestPrecedenceRole];
      const supplierTypeValueLabel = supplierType.find(
        (type) => type.value === supplierTypeId
      );
      setSelectedSupplierType(supplierTypeValueLabel);
    } else {
      setSelectedSupplierType([]);
    }
  };

  const moveLinks = (operation, newEnteredSendacGroup) => {
    setLoading(true);
    let saveData;

    if (operation == "removeSendacOperation") {
      saveData = {
        selectedSendacGroup: [newSendacGroupSelected],
        links: supplierLinks,
        sectionName: operation,
        flag: "dontaddnew",
        oldSendac: sendacGroups,
      };
      setSendacGroups([
        {
          value: newSendacGroupSelected.value,
          label: newSendacGroupSelected.label,
        },
      ]);

      setIsAddingNewSendac(false);
      //addSandacGroup();
      setSupplierLinks([]);
    } else if (operation == "sendacOperation") {
      saveData = {
        selectedSendacGroup: [newSendacGroupSelected],
        links: supplierLinks,
        sectionName: operation,
        flag: "",
        oldSendac: sendacGroups,
      };

      setSendacGroups([
        {
          value: newSendacGroupSelected.value,
          label: newSendacGroupSelected.label,
        },
      ]);
    } else if (operation == "moveSendacOperation") {
      saveData = {
        selectedSendacGroup: [newEnteredSendacGroup],
        links: supplierLinks,
        sectionName: operation,
        flag: "",
        oldSendac: sendacGroups,
      };

      setSendacGroups([
        {
          value: newEnteredSendacGroup.value,
          label: newEnteredSendacGroup.label,
        },
      ]);
    }
    setSaveDataMoveOrRemoveLinks(saveData);
    setIsOpen(false);

    setLoading(false);

    //setValidSandacGroup(true);
    handleValidationChange();
  };

  const openPopup = (data) => {
    setNewSendacGroupSelected(data);
    setIsOpen(true);
  };

  const fetchDataBySendac = (data) => {
    fetch(`${apiBase}/api/suppliers/get-link-by-sendac/${data.value}`, {
      method: "GET",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      credentials: "include", // Use session authentication
    })
      .then((res) => {
        if (res.status === 400 || res.status === 401) {
          setLoading(false);
          setCommonError("Invalid Token");
        }
        if (res.status === 200) {
          return res.json();
        }
        return Promise.reject(res);
      })
      .then((res) => {
        if (res?.length > 0) {
          const roleMap = new Map();
          res.forEach((item) => {
            if (!roleMap.has(item?.name)) {
              roleMap.set(item?.name, { id: item?.id, role_id: [], roles: [] });
            }
            roleMap.get(item?.name)?.roles.push(item?.role_names);
            roleMap.get(item?.name)?.role_id.push(item?.role_id);
          });

          const result = [...roleMap.entries()].map(
            ([name, { id, role_id, roles }]) => ({
              id,
              name,
              role_id,
              role_names: roles.join(" , "),
            })
          );

          const formattedLinks = result.map((row) => ({
            value: row?.id,
            label: `${row.name} ( ${row?.role_names?.trim()} )`,
            role_id: row.role_id,
          }));

          setLinkListByRole(
            formattedLinks?.filter((item) => item?.value != supplierId)
          );
        }
      })
      .catch((error) => {
        toast.error(error.message, {
          position: "top-right",
        });
      });
  };

  const handleSendacGroup = (data) => {
    setFormChange(true);
    setIsVisibleSendacGroup(false);
    if (data) {
      setValidSandacGroup(true);
    } else {
      setValidSandacGroup(false);
    }

    if (supplierLinks?.length === 0 || sendacGroups?.length === 0) {
      if (data) {
        setSendacGroups([{ value: data.value, label: data.label }]);
        setValidSandacGroup(true);
      } else {
        setSendacGroups([]);
      }
    } else if (data && sendacGroups) {
      if (supplierRoles?.includes(1) && supplierLinks?.length > 0) {
        openPopup(data);
      } else {
        setSendacGroups([{ value: data.value, label: data.label }]);
      }
    } else {
      setSendacGroups([]);
      setSupplierLinks([]);
    }
    setIsNewSendacGroup(false);
    //handleValidationChange();
  };
  const handleSupplierLinks = (currentSelected) => {
    setFormChange(true);
    const removedLinks = supplierLinks.filter(
      (oldOption) =>
        !currentSelected.some(
          (newOption) => newOption.value === oldOption.value
        )
    );
    setClearedLinks([...clearedLinks, ...removedLinks]);

    setLinkListByRole((prevLinks) => {
      const newLinks = removedLinks.filter(
        (removedLink) =>
          !prevLinks.some((prevLink) => prevLink.value === removedLink.value)
      );
      return [...prevLinks, ...newLinks];
    });
    setSupplierLinks(currentSelected);
  };
  const [selectedSupplierType, setSelectedSupplierType] = useState([]);
  const handleSupplierType = (selectedType) => {
    setFormChange(true);
    setSelectedSupplierType(selectedType);
  };
  const handleSubmitSupplier = (e) => {
    const domain = userData?.company || "";
    e.preventDefault();
    if (handleValidationChange() == false) {
      return false;
    }
    let prophetsArr = prophets?.map((ele) => ele[0]);
    // Store prophets in localStorage instead of cookies
    setLoading(true);

    const saveData = {
      name: supplierName.trim(),
      prophets: prophetChange || supplierNameChange ? newProphets : prophets,
      requestor_name: userData?.name,
      requestor_email: userData?.email,
      emergency_request: urgentRequest,
      gdpr_compliant: gdprCompliant,
      product_supplier: produceSupplier,
      sendac_groups: sendacGroups,
      roles: supplierRoles,
      supplier_links: supplierLinks,
      created_date: new Date().toISOString(),
      updated_date: new Date().toISOString(),
      newSendacGroup: isNewSendacGroup,
      status: 2,
      company: domain,
      isInactiveGroup: isInactiveGroup,
      prophetCodeNum: prophets[0][0],
      isCodeSystemGenerated: isCodeSystemGenerated,
      supplier_type: selectedSupplierType.value,
    };

    fetch(`${apiBase}/api/suppliers/add-supplier`, {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      credentials: "include", // Use session authentication
      body: JSON.stringify(saveData),
    })
      .then((res) => {
        if (res.status === 400) {
          console.log("Something went wrong");
        } else if (res.status === 401) {
          toast.error(`Token expired`, {
            position: "top-right",
          });
        }
        if (res.status === 201) {
          return res.json();
        }
        return Promise.reject(res);
      })
      .then((json) => {
        const res = json;

        if (res?.data?.length > 0) {
          toast.success("Supplier created successfully", {
            position: "top-right",
          });
          localStorage.setItem("supplier_id", res.data[0].id);
          if (editing) {
            router.back();
          } else {
            router.push({
              pathname: `/supplier/${res.data[0].id}/edit/forms`,
            });
          }
        } else {
          setLoading(false);
          toast.error(`Supplier not added successfully. Please try again`, {
            position: "top-right",
          });
        }
      })
      .catch((error) => {
        console.log("error", error);
        setLoading(false);
        toast.error(`Something went wrong. Please try again`, {
          position: "top-right",
        });
      });
  };

  useEffect(() => {
    if (isCodeSystemGenerated && prophets.length > 0) {
      let generatePhophetCode = "";
      let prophetId = prophets.length > 0 && prophets[0][0];
      if (urgentRequest && prophets[0][1] == "") {
        const supplierNameWithoutSpaces = supplierName
          .replace(/\s/g, "")
          .toUpperCase();
        const supplierPrefix = supplierNameWithoutSpaces?.substring(0, 4);

        if (supplierRoles?.includes(1) || supplierRoles?.includes(6)) {
          if (prophetId == 1) {
            generatePhophetCode = supplierPrefix;
            generatePhophetCode = generatePhophetCode
              .slice(-6)
              .padStart(6, "X");
            setProphetCodesDPS(generatePhophetCode);
          } else if (prophetId == 2) {
            generatePhophetCode =
              supplierPrefix.slice(0, 4).padStart(4, "X") + "9";
            generatePhophetCode = generatePhophetCode.padEnd(6, "X");
            setProphetCodesDPSMS(generatePhophetCode);
          } else if (prophetId == 3) {
            generatePhophetCode =
              supplierPrefix.slice(0, 4).padStart(4, "X") + "3";
            generatePhophetCode = generatePhophetCode.padEnd(6, "X");
            setProphetCodesEFC(generatePhophetCode);
          } else if (prophetId == 4) {
            generatePhophetCode =
              supplierPrefix.slice(0, 4).padStart(4, "X") + "2";
            generatePhophetCode = generatePhophetCode.padEnd(6, "X");

            setProphetCodesFPP(generatePhophetCode);
          }
        } else if (
          supplierRoles?.length > 0 &&
          (!supplierRoles?.includes(1) || !supplierRoles?.includes(6))
        ) {
          generatePhophetCode = Math.floor(Math.random() * 1000000).toString();
          generatePhophetCode = generatePhophetCode.slice(-6).padStart(6, "X");
          if (prophetId == 1) {
            setProphetCodesDPS(generatePhophetCode);
          } else if (prophetId == 2) {
            setProphetCodesDPSMS(generatePhophetCode);
          } else if (prophetId == 3) {
            setProphetCodesEFC(generatePhophetCode);
          } else if (prophetId == 4) {
            setProphetCodesFPP(generatePhophetCode);
          }
        }
        setFormChange(true);
        setProphetChange(true);

        let supplierIdToPass = supplierId ? supplierId : "";

        setNewProphets([
          [prophetId, generatePhophetCode?.trim(), supplierIdToPass],
        ]);
      } else if (
        prophets[0][1] == "" &&
        supplierRoles.length == 1 &&
        (supplierRoles?.includes(5) || supplierRoles?.includes(4))
      ) {
        generatePhophetCode = Math.floor(Math.random() * 1000000).toString();
        generatePhophetCode = generatePhophetCode.slice(-6).padStart(6, "X");
        if (prophetId == 1) {
          setProphetCodesDPS(generatePhophetCode);
        } else if (prophetId == 2) {
          setProphetCodesDPSMS(generatePhophetCode);
        } else if (prophetId == 3) {
          setProphetCodesEFC(generatePhophetCode);
        } else if (prophetId == 4) {
          setProphetCodesFPP(generatePhophetCode);
        }

        setFormChange(true);
        setProphetChange(true);

        let supplierIdToPass = supplierId ? supplierId : "";
        setNewProphets([
          [prophetId, generatePhophetCode?.trim(), supplierIdToPass],
        ]);
      }
    }
  }, [urgentRequest, prophets, supplierId, supplierRoles]);

  const handleUpdateSupplier = (e) => {
    e.preventDefault();
    if (handleValidationChange() == false) {
      return false;
    }

    setLoading(true);

    let deleteFlags = {
      technical: false,
      compliance: false,
      procurement: false,
      financial: false,
    };

    supplierRoles.forEach((role) => {
      switch (role) {
        case 1:
          deleteFlags.technical = true;
          deleteFlags.compliance = true;
          deleteFlags.financial = true;
          deleteFlags.procurement = true;
          break;
        case 2:
        case 3:
        case 4:
          deleteFlags.technical = true;
          deleteFlags.compliance = true;
          break;
        case 5:
          deleteFlags.technical = true;
          deleteFlags.procurement = true;
          break;
        case 6:
          deleteFlags.technical = true;
          deleteFlags.financial = true;
          break;
        default:
          break;
      }
    });

    const saveData = {
      name: supplierName.trim(),
      prophets: prophetChange || supplierNameChange ? newProphets : prophets,
      emergency_request: urgentRequest,
      gdpr_compliant: gdprCompliant,
      product_supplier:
        supplierRoles?.includes(2) || supplierRoles?.includes(3) ? true : false,
      sendac_groups: sendacGroups,
      roles: supplierRoles,
      supplier_links: supplierLinks,
      updated_date: new Date().toISOString(),
      status: 4,
      sectionName: "updateSupplier",
      user: userData,
      newSendacGroup: isNewSendacGroup,
      deleteFlags: deleteFlags,
      moveOrRemoveLinks: saveDataMoveOrRemoveLinks,
      supplierLinksExisted: initiallySupplierLinksExist,
      removedLinks: clearedLinks,
      isInactiveGroup: isInactiveGroup,
      prophetCodeNum: prophetCodeNum,
      isCodeSystemGenerated: isCodeSystemGenerated,
      supplier_type: selectedSupplierType.value,
    };

    if (formChange) {
      fetch(`${apiBase}/api/suppliers/update-supplier/${supplierId}`, {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        credentials: "include", // Use session authentication
        body: JSON.stringify(saveData),
      })
        .then(async (res) => {
          if (res.status === 400 || res.status === 401) {
            await handleSessionError();
            return;
          }
          if (res.status === 200) {
            return res.json();
          }
          return Promise.reject(res);
        })
        .then((json) => {
          localStorage.setItem("isFormNew", false);
          if (json.status == 200) {
            if (editing) {
              localStorage.removeItem("isEdit");
              router.back();
            } else {
              router.push({
                pathname: `/supplier/${supplierId}/edit/forms`,
              });
              toast.success("Supplier updated successfully", {
                position: "top-right",
              });
            }
          }
        })
        .catch((error) => {
          setLoading(false);
          toast.error("Supplier not updated successfully. Please try again", {
            position: "top-right",
          });
        });
    } else {
      if (initialFormValues.status == 3) {
        localStorage.setItem("isFormNew", true);
      } else {
        localStorage.setItem("isFormNew", false);
      }

      if (editing) {
        localStorage.removeItem("isEdit");
        router.back();
      } else {
        router.push({
          pathname: `/supplier/${supplierId}/edit/forms`,
        });
      }
    }
  };

  const checkLinks = (e) => {
    e.preventDefault();
    if (supplierLinks?.length > 0 && sendacGroup?.trim()) {
      addSandacGroup(e);
      setIsAddingNewSendac(true);

      if (!duplicateFound) {
        setIsOpen(true);
        return;
      }
      if (duplicateFound) {
        setIsOpen(false);
        return;
      }

      setIsOpen(true);
    } else if (sendacGroup.trim()) {
      addSandacGroup(e);
    } else {
      setIsValidGroup(false);
    }
  };

  const handleAddSendacGroup = (e) => {
    e.preventDefault();
    setIsVisibleSendacGroup(!isVisibleSendacGroup);
    setSendacGroup("");
    // setSendacGroups("");
  };
  const addSandacGroup = (e, op = "none") => {
    e.preventDefault();

    if (sendacGroup) {
      // Filter out any existing groups that match the input
      const found = sendacAllGroupList?.filter((obj) => {
        // Remove any prefix followed by " - " and then compare in uppercase
        const labelWithoutPrefix = obj.label.substring(
          obj.label.indexOf(" - ") + 3
        );
        return labelWithoutPrefix === sendacGroup.trim().toUpperCase();
      });

      // Check if a duplicate active group is found
      const duplicateActiveGroup = found?.find((obj) => obj.is_active === 1);

      if (duplicateActiveGroup) {
        // If an active group is found, show an error
        toast.error("Sendac group already exists.", {
          position: "top-right",
        });
        return;
      }

      // Check if an inactive group is found
      const inactiveGroup = found?.find((obj) => obj.is_active === 0);

      if (inactiveGroup) {
        setIsInactiveGroup(true);
        // Use the existing inactive group's label and value
        setSendacGroups([
          {
            value: inactiveGroup.value,
            label: inactiveGroup.label,
          },
        ]);
        setIsNewSendacGroup(false);

        setNewSendacGroupSelected({
          value: inactiveGroup.value,
          label: inactiveGroup.label,
        });
      } else {
        // If no inactive group is found, proceed with creating a new one
        setIsNewSendacGroup(true);
        setSendacGroups([
          {
            value: null,
            label: sendacGroup.trim(),
          },
        ]);
        setNewSendacGroupSelected({ value: null, label: sendacGroup.trim() });
      }

      setIsOpen(false);

      if (supplierLinks?.length > 0 && op === "none") {
        return;
      }

      const formattedData = sendacGroupList?.map((row) => {
        return { value: row.value, label: row.label };
      });

      setValidSandacGroup(true);

      const selected_group = formattedData.filter(
        (item) => item.label === sendacGroup.trim()
      );

      setGroupList(formattedData);
      setIsVisibleSendacGroup(false);

      // Handle different operations
      if (op === "moving") {
        moveLinks("moveSendacOperation", {
          value: inactiveGroup ? inactiveGroup.value : null,
          label: inactiveGroup ? inactiveGroup.label : sendacGroup.trim(),
        });
      } else if (op === "removing") {
        moveLinks("removeSendacOperation", {
          value: inactiveGroup ? inactiveGroup.value : null,
          label: inactiveGroup ? inactiveGroup.label : sendacGroup.trim(),
        });
      }
    } else {
      setIsValidGroup(false);
    }
  };

  const handleCancel = (e) => {
    e.preventDefault();
    router.push("/suppliers");
    // router.back();
  };

  const supplierNameChangeHandler = (value) => {
    setSupplierName(value);
  };
  useEffect(() => {
    if (!formChange) {
      if (selectedSupplierType?.value === null) {
        const roleToSupplierType = {
          1: 2, // Supplier account -> GOODS
          2: 3, // Producer -> GROWE
          3: 3, // Grower -> GROWE
          4: 4, // Packhouse -> PACKH
          5: 1, // Haulier -> CHARG
          6: 1, // Expense -> CHARG
        };

        const precedenceOrder = [2, 3, 4, 1]; // Precedence order based on supplier type IDs
        const highestPrecedenceRole = supplierRoles.sort(
          (a, b) =>
            precedenceOrder.indexOf(roleToSupplierType[a]) -
            precedenceOrder.indexOf(roleToSupplierType[b])
        )[0];
        if (highestPrecedenceRole) {
          const supplierTypeId = roleToSupplierType[highestPrecedenceRole];
          const supplierTypeValueLabel = supplierType.find(
            (type) => type.value === supplierTypeId
          );
          setSelectedSupplierType(supplierTypeValueLabel);
        } else {
          setSelectedSupplierType([]);
        }
      }
    }
  }, [supplierType, selectedSupplierType]);
  return (
    <>
      <ToastContainer />
      <Layout userData={userData}>
        {loading ? (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: "calc(100vh - 100px)",
              width: "calc(100vw - 125px)",
            }}
          >
            <ThreeCircles
              color="#002D73"
              height={50}
              width={50}
              visible={true}
              ariaLabel="oval-loading"
              secondaryColor="#0066FF"
              strokeWidth={2}
              strokeWidthSecondary={2}
            />
          </div>
        ) : (
          <div className="relative panel-container contentsectionbg rounded-lg w-[95%] 2xl:w-[calc(100%-70px)] p-4 pb-0 shadow-lg">
            <div className="m-3 mb-0">
              {/* <!-- <div>
								<h4 className="text-lg xl:text-lg 2xl:text-2xl font-bold pb-2 border-b ">Supplier details</h4>
							</div> --> */}
              <div className="flex flex-col">
                <div className="flex flex-col xl:flex-row max-lg:gap-10">
                  <div className="flex flex-col w-full xl:w-[65%] 2xl:w-[55%] xl:border-e ">
                    <div className="flex flex-col w-full xl:w-3/5 mb-4">
                      <label className="labels mb-2 ">
                        Supplier Name <span className="text-red-500">*</span>
                      </label>

                      <DebouncedAutocomplete
                        onChange={supplierNameChangeHandler}
                        isCodeSystemGenerated={isCodeSystemGenerated}
                        setSupplierNameChange={setSupplierNameChange}
                        supplierName={supplierName}
                        isValidSupplierName={isValidName}
                        setFormChange={setFormChange}
                        setSupplierValidName={setValidName}
                        setProphetChange={setProphetChange}
                        urgentRequest={urgentRequest}
                        setNewProphets={setNewProphets}
                        prophets={prophets[0]}
                        supplierRoles={supplierRoles}
                        symbol={symbol}
                        handleSessionError={handleSessionError}
                      />
                    </div>
                    <div className="mr-2">
                      <div className="grid grid-cols-8 w-full">
                        <label className="labels mb-2 col-span-2">
                          Prophet System<span className="text-red-500">*</span>{" "}
                        </label>
                        <label className="labels mb-2 col-span-2">
                          Supplier Code<span className="text-red-500">*</span>{" "}
                        </label>
                        <label className="labels mb-2 col-span-2 col-start-6">
                          Supplier Type<span className="text-red-500">*</span>{" "}
                        </label>
                      </div>
                      <div className="grid w-full grid-cols-8">
                        <label className="flex items-center labels col-span-2">
                          {ADCompanyName}
                        </label>
                        <ProphetCode
                          setIsProphetCodeUnique={setIsProphetCodeUnique}
                          setIsCodeSystemGenerated={setIsCodeSystemGenerated}
                          setProphetCodeChange={setProphetChange}
                          handleSessionError={handleSessionError}
                          prophetCodes={
                            prophetCodesDPS
                              ? prophetCodesDPS
                              : prophetCodesDPSMS
                              ? prophetCodesDPSMS
                              : prophetCodesEFC
                              ? prophetCodesEFC
                              : prophetCodesFPP
                              ? prophetCodesFPP
                              : ""
                          }
                          fetchedProphetCodes={
                            fetchedProphetCodesDPS
                              ? fetchedProphetCodesDPS
                              : fetchedProphetCodesDPSMS
                              ? fetchedProphetCodesDPSMS
                              : fetchedProphetCodesEFC
                              ? fetchedProphetCodesEFC
                              : fetchedProphetCodesFPP
                              ? fetchedProphetCodesFPP
                              : ""
                          }
                          setProphetCodes={
                            ADCompanyName == "DPS"
                              ? setProphetCodesDPS
                              : ADCompanyName == "DPS MS"
                              ? setProphetCodesDPSMS
                              : ADCompanyName == "EFC"
                              ? setProphetCodesEFC
                              : ADCompanyName == "FPP"
                              ? setProphetCodesFPP
                              : ""
                          }
                          prophets={prophets[0]}
                          setNewProphets={setNewProphets}
                          setFormChange={setFormChange}
                          prophetChange={prophetChange}
                          forField={ADCompanyName}
                          supplierRoles={supplierRoles}
                        />{" "}
                        <div className="col-span-2 col-start-6">
                          <Select
                            options={supplierType}
                            placeholder="Select..."
                            value={selectedSupplierType}
                            onChange={handleSupplierType}
                            isSearchable={true}
                            instanceId="selectbox"
                            //onBlur={handleValidationChange}
                          />
                        </div>
                      </div>
                    </div>
                    <div className="flex w-[50%]">
                      <span className="w-[50%]"></span>
                      <span className="text-red-500 text-[10px] w-[50%]">
                        {ADCompanyName == "DPS"
                          ? prophetCodesDPS && !isDPSProphetCodeValid
                            ? prophetCodeValidationMessage
                            : !prophetDPSUnique
                            ? "Supplier code is not unique"
                            : ""
                          : ADCompanyName == "DPS MS"
                          ? prophetCodesDPSMS && !isDPSMSProphetCodeValid
                            ? prophetCodeValidationMessage
                            : !prophetDPSMSUnique
                            ? "Supplier code is not unique"
                            : ""
                          : ADCompanyName == "EFC"
                          ? prophetCodesEFC && !isEFCProphetCodeValid
                            ? prophetCodeValidationMessage
                            : !prophetEFCUnique
                            ? "Supplier code is not unique"
                            : ""
                          : ADCompanyName == "FPP"
                          ? prophetCodesFPP && !isFPPProphetCodeValid
                            ? prophetCodeValidationMessage
                            : !prophetFPPUnique
                            ? "Supplier code is not unique"
                            : ""
                          : ""}
                      </span>
                    </div>
                    <div className="flex flex-row w-[96%] py-5">
                      <div className="flex flex-col w-full">
                        <div className="flex flex-row items-center w-1/2">
                          <label className="labels  p-0">Urgent Request </label>
                          <div className="ml-5">
                            <label className="relative inline-flex items-center cursor-pointer py-[5px]">
                              <input
                                type="checkbox"
                                value=""
                                className="sr-only peer"
                                checked={urgentRequest}
                                onChange={(e) => {
                                  setFormChange(true);
                                  setUrgentRequest(!urgentRequest);
                                }}
                              />
                              <div className="w-11 h-6 checkboxbg rounded-full peer after:translate-x-0 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-2 after:left-[1px] peer-checked:after:left-[3px] after:bg-white after: after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-skin-primary"></div>
                            </label>
                          </div>
                        </div>
                        {isCurrencyMandatory && (
                          <span className="text-red-500 mt-1">
                            Currency in finance section is mandatory to generate
                            valid code and export the supplier
                          </span>
                        )}
                      </div>
                      <div className="flex flex-col w-full">
                        <div className="flex flex-row justify-center items-center w-1/2">
                          <label className="labels p-0 ">GDPR Compliant</label>
                          <div className="ml-5">
                            <label className="relative inline-flex items-center cursor-pointer py-[5px]">
                              <input
                                type="checkbox"
                                checked={gdprCompliant}
                                onChange={(e) => {
                                  setFormChange(true);
                                  setGdprCompliant(!gdprCompliant);
                                }}
                                value=""
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 checkboxbg rounded-full peer after:translate-x-0 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-2 after:left-[1px] peer-checked:after:left-[3px] after:bg-white after: after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-skin-primary"></div>
                            </label>
                          </div>
                        </div>
                        {urgentRequest && (
                          <span className="text-red-500 mt-1"></span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col w-full xl:w-[40%] 2xl:w-[50%] xl:mt-20 lg:pl-0 xl:pl-5">
                    <div className="flex flex-col w-full md:mb-6 lg:mb-6 xl:mb-6">
                      <label className="labels mb-3">
                        Supplier Roles <span className="text-red-500">*</span>
                      </label>
                      <div className="grid grid-cols-3 gap-5 xl:gap-6 w-full">
                        {roles &&
                          roles?.length > 0 &&
                          roles?.map((role, index) => {
                            return (
                              <div className="flex items-center" key={index}>
                                <input
                                  type="checkbox"
                                  id={`role-${index}`} // Add unique id for each checkbox
                                  checked={
                                    supplierRoles &&
                                    supplierRoles?.includes(role.id)
                                      ? true
                                      : false
                                  }
                                  value={role.id}
                                  className="w-5 h-5 text-theme-blue2 bg-white rounded accent-skin-primary"
                                  onChange={(e) => {
                                    setFormChange(true);
                                    handleRoleChange(
                                      e.target.value,
                                      e.target.checked
                                    );
                                    //handleValidationChange(e)
                                  }}
                                  // onBlur={handleValidationChange}
                                />
                                <label
                                  htmlFor={`role-${index}`} // Use htmlFor to associate label with checkbox
                                  className="p-0 ml-3 inputs whitespace-nowrap"
                                >
                                  {titleCase(role.role_name)}
                                </label>
                              </div>
                            );
                          })}
                      </div>

                      {!isValidRoles && (
                        <span className="text-red-500">
                          Please select role(s){" "}
                        </span>
                      )}
                    </div>
                    {supplierRoles &&
                      (supplierRoles?.includes(1) ||
                        supplierRoles?.includes(2) ||
                        supplierRoles?.includes(3) ||
                        supplierRoles?.includes(4) ||
                        supplierRoles?.includes(5) ||
                        supplierRoles?.includes(6)) && (
                        <div className="flex flex-row lg:mt-0 w-full">
                          <div className="flex flex-col w-full">
                            <div className="flex flex-row justify-between">
                              <label className="labels mb-2 ">
                                SENDAC Group{" "}
                                {(mandatorySendacDPS ||
                                  mandatorySendacDPSMS ||
                                  mandatorySendacFPP ||
                                  mandatorySendacEFC) && (
                                  <span className="text-red-500">*</span>
                                )}
                              </label>
                              {supplierRoles && supplierRoles?.includes(1) && (
                                <button
                                  onClick={(e) => handleAddSendacGroup(e)}
                                  title="Create New Sendac Group"
                                >
                                  <span className="px-2 py-1  b2xl:px-3.5 2xl:py-1 border border-skin-primary rounded-md text-skin-primary mb-1 flex items-center cursor-pointer">
                                    <FontAwesomeIcon icon={faPlus} />
                                  </span>
                                </button>
                              )}
                            </div>

                            <div className="flex flex-col w-full">
                              <Select
                                options={sendacGroupList}
                                placeholder="Select..."
                                value={sendacGroups}
                                onChange={handleSendacGroup}
                                isSearchable={true}
                                isClearable={true}
                                instanceId="selectbox"
                                //onBlur={handleValidationChange}
                              />
                            </div>
                            {!isValidSandacGroup && (
                              <span className="text-red-500 mt-1">
                                Please select any of one sendac group{" "}
                              </span>
                            )}
                          </div>
                        </div>
                      )}
                    {isVisibleSendacGroup && (
                      <div className="flex flex-col w-full mb-5 mt-4 xl:mt-6">
                        <div className="flex w-full flex-col">
                          <label className="labels mb-2 ">
                            Add New Sendac Group Name.
                          </label>
                          <div className="flex flex-row w-full justify-between">
                            <input
                              type="text"
                              name="sendac_group_name"
                              className="border searchbar rounded-md px-2 2xl:px-3 mr-4 w-full"
                              value={sendacGroup}
                              onChange={(e) => {
                                setFormChange(true);
                                setSendacGroup(e.target.value.toUpperCase());
                              }}
                              onBlur={handleValidationChange}
                              maxLength={50}
                            />
                            <div className="flex flex-row gap-3 mb-0 2xl:mb-0.5">
                              <button onClick={(e) => handleAddSendacGroup(e)}>
                                <span className="px-2.5 py-2 2xl:p-3 border rounded-md text-skin-a11y bg-red-500 mb-0 flex items-center cursor-pointer">
                                  {/* <i
                                    className="fa fa-times"
                                    aria-hidden="true"
                                  ></i> */}
                                  {/* <FontAwesomeIcon icon={faClose} className="w-5"/> */}
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 384 512"
                                    className="w-5 h-5 fill-white"
                                  >
                                    <path d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z" />
                                  </svg>
                                </span>
                              </button>
                              <button onClick={(e) => checkLinks(e)}>
                                <span className="px-2.5 py-2 2xl:p-3 border border-skin-primary bg-skin-primary rounded-md text-skin-a11y mb-0 flex items-center cursor-pointer">
                                  {/* <FontAwesomeIcon icon={faFloppyDisk} /> */}
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 448 512"
                                    className="w-5 h-5 fill-white"
                                  >
                                    <path d="M222.2 319.2c.5 .5 1.1 .8 1.8 .8s1.4-.3 1.8-.8L350.2 187.3c1.2-1.2 1.8-2.9 1.8-4.6c0-3.7-3-6.7-6.7-6.7L288 176c-8.8 0-16-7.2-16-16l0-120c0-4.4-3.6-8-8-8l-80 0c-4.4 0-8 3.6-8 8l0 120c0 8.8-7.2 16-16 16l-57.3 0c-3.7 0-6.7 3-6.7 6.7c0 1.7 .7 3.3 1.8 4.6L222.2 319.2zM224 352c-9.5 0-18.6-3.9-25.1-10.8L74.5 209.2C67.8 202 64 192.5 64 182.7c0-21.4 17.3-38.7 38.7-38.7l41.3 0 0-104c0-22.1 17.9-40 40-40l80 0c22.1 0 40 17.9 40 40l0 104 41.3 0c21.4 0 38.7 17.3 38.7 38.7c0 9.9-3.8 19.3-10.5 26.5L249.1 341.2c-6.5 6.9-15.6 10.8-25.1 10.8zM32 336l0 96c0 26.5 21.5 48 48 48l288 0c26.5 0 48-21.5 48-48l0-96c0-8.8 7.2-16 16-16s16 7.2 16 16l0 96c0 44.2-35.8 80-80 80L80 512c-44.2 0-80-35.8-80-80l0-96c0-8.8 7.2-16 16-16s16 7.2 16 16z" />
                                  </svg>
                                </span>
                              </button>
                            </div>
                          </div>
                          {!isValidGroup && (
                            <span className="text-red-500 mt-1">
                              Please enter sendac group name
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                    {supplierRoles && supplierRoles?.includes(1) && (
                      <div className="flex flex-col mb-5 mt-4 xl:mt-4 w-full">
                        <div className="flex flex-col">
                          <label className="labels mb-2 ">
                            Link to Existing Producer, Grower, Packhouse
                          </label>
                          <Select
                            options={linkListByRole}
                            placeholder="Select..."
                            value={supplierLinks}
                            onChange={handleSupplierLinks}
                            isSearchable={true}
                            isMulti
                            instanceId="selectbox"
                            //onBlur={handleValidationChange}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex justify-between contentsectionbg py-5">
                <button
                  onClick={(e) => handleCancel(e)}
                  className="border border-skin-primary text-skin-primary button px-8 rounded-md"
                >
                  Cancel
                </button>
                <button
                  onClick={(e) => {
                    isValidName && isValidProphets && isValidRoles == true
                      ? !!isEdit
                        ? handleUpdateSupplier(e)
                        : handleSubmitSupplier(e)
                      : false;
                  }}
                  className="border border-skin-primary text-white bg-skin-primary rounded-md  button px-8 cursor-pointer"
                >
                  Save & Proceed
                </button>
              </div>
            </div>
          </div>
        )}
        <Transition appear show={isOpen} as={Fragment}>
          <Dialog as="div" className="relative z-10" onClose={closeModal}>
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
            </Transition.Child>

            <div className="fixed inset-0 overflow-y-auto">
              <div className="flex items-center justify-center min-h-full p-4 text-center">
                <Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 scale-95"
                  enterTo="opacity-100 scale-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 scale-100"
                  leaveTo="opacity-0 scale-95"
                >
                  <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                    {/* <!-- Modal content --> */}
                    <div className="relative bg-white rounded-lg shadow">
                      {/* <!-- Modal header --> */}
                      <div className="flex items-start justify-between p-8 rounded-t">
                        <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                          <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                            <FontAwesomeIcon icon={faInfo} />{" "}
                          </span>{" "}
                          Warning
                        </h3>
                        <button
                          onClick={(e) => closeModal(e)}
                          type="button"
                          className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                          data-modal-hide="default-modal"
                        >
                          <FontAwesomeIcon
                            icon={faXmark}
                            className="text-skin-primary"
                          />{" "}
                        </button>
                      </div>

                      {/* <!-- Modal footer --> */}
                      <div className="flex items-end p-6 space-x-2 justify-end">
                        <button
                          onClick={(e) => {
                            if (isAddingNewSendac) {
                              addSandacGroup(e, "moving");
                            } else {
                              moveLinks("sendacOperation");
                            }
                          }}
                          data-modal-hide="default-modal"
                          type="button"
                          className="text-skin-a11y bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                        >
                          Move linked Suppliers to new Sendac Group
                        </button>

                        <button
                          onClick={(e) => {
                            if (isAddingNewSendac) {
                              addSandacGroup(e, "removing");
                            } else {
                              moveLinks("removeSendacOperation");
                            }
                          }}
                          data-modal-hide="default-modal"
                          type="button"
                          className="text-skin-a11y bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                        >
                          Remove Linked Suppliers from Sendac Group
                        </button>
                        <button
                          onClick={(e) => closeModal(e)}
                          data-modal-hide="default-modal"
                          type="button"
                          className="text-skin-a11y border border-skin-primary textc-dark-gray focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  </Dialog.Panel>
                </Transition.Child>
              </div>
            </div>
          </Dialog>
        </Transition>
      </Layout>
    </>
  );
};

export default SupplierForm;

export const getServerSideProps = async (context) => {
  try {
    // Use secure session validation
    const sessionId = context.req.cookies.thl_session;

    if (!sessionId) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(
            context.resolvedUrl
          )}`,
          permanent: false,
        },
      };
    }

    // Validate session with our backend API
    const apiBase =
      process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8081";

    try {
      const response = await fetch(`${apiBase}/api/auth/me`, {
        method: "GET",
        headers: {
          Cookie: `thl_session=${sessionId}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        // Session invalid or expired - redirect to login
        return {
          redirect: {
            destination: `/login?redirect=${encodeURIComponent(
              context.resolvedUrl
            )}`,
            permanent: false,
          },
        };
      }

      const { user } = await response.json();

      // Check if user has permission to access supplier form
      // Typically all authenticated users can access supplier forms, but you can add role restrictions here if needed

      return {
        props: {
          userData: user,
        },
      };
    } catch (fetchError) {
      console.error("Session validation failed:", fetchError);
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(
            context.resolvedUrl
          )}`,
          permanent: false,
        },
      };
    }
  } catch (error) {
    console.error("Authentication error:", error);
    return {
      redirect: {
        destination: `/login?redirect=${encodeURIComponent(
          context.resolvedUrl
        )}`,
        permanent: false,
      },
    };
  }
};
