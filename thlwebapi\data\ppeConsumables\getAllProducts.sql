SELECT 
    -- Product Core Information
    p.[id] AS ProductId,
    p.[name] AS ProductName,
    p.[product_code] AS ProductCode,
    p.[quantity] AS PackageQuantity, -- e.g., "10 pairs", "1 unit"
    p.[comments] AS ProductComments,
    p.[is_active] AS IsProductActive,

    -- Product Type Information (from lookup table)
    tp.[id] AS TypeId,
    tp.[name] AS ProductType, -- e.g., 'Gloves', 'Footwear', 'Head Protection'
    tp.[is_active] AS IsTypeActive,

    -- Size Information (if applicable)
    p.[size_required] AS IsSizeRequired,
    p.[size_label] AS SizeLabel, -- e.g., 'Shoe Size', 'Clothing Size'

    -- Optional: List of available sizes for the product
    -- This uses STRING_AGG to create a comma-separated list of available sizes
    -- Comment this section out if you don't need it or it slows down the query
    ISIZE.AvailableSizes,

    -- Timestamps
    p.[size_required] -- This seems to be the only other column, displaying it for clarity

FROM 
    [Iss_ppe_consumables_portal].[dbo].[products] p
    -- Left Join to get the product type name (will show NULL if type_id is invalid or doesn't exist)
    LEFT JOIN [Iss_ppe_consumables_portal].[dbo].[type_of_product] tp 
        ON p.[type_id] = tp.[id] 
        AND tp.[is_active] = 1

    -- Use a subquery with STRING_AGG to get a list of all available sizes for each product
    -- This is a more advanced but very useful technique
    LEFT JOIN (
        SELECT 
            pas.[product_id],
            STRING_AGG(ps.size_label, ', ') WITHIN GROUP (ORDER BY ps.size_label) AS AvailableSizes
        FROM 
            [Iss_ppe_consumables_portal].[dbo].[product_available_sizes] pas
            INNER JOIN [Iss_ppe_consumables_portal].[dbo].[product_sizes] ps 
                ON pas.[size_id] = ps.[id]
        WHERE 
            pas.[is_active] = 1 
            AND ps.[is_active] = 1
        GROUP BY 
            pas.[product_id]
    ) ISIZE ON p.[id] = ISIZE.[product_id]

-- Optional: Add a WHERE clause to filter results
-- WHERE p.[is_active] = 1 -- Uncomment to show only active products
-- WHERE p.[type_id] = 5   -- Uncomment to show products of a specific type

ORDER BY 
    p.[name]; -- Order by product name alphabetically
    -- Other good options: p.[product_code], tp.[name], p.[id]