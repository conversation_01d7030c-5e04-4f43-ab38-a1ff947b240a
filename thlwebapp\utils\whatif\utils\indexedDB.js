import { openDB } from "idb";
import {
  WHATIF_TYPE_PROMO_ID,
  WHATIF_TYPE_RR_VOLUME_ID,
  WHATIF_TYPE_SUPPLIR_ISSUE_ID,
} from "./constants";

const DB_NAME = "WhatifProducts";
const STORE_NAME = "allProducts";
const CALENDER_STORE_NAME = "efcCalender";
const STD_CALENDER_STORE_NAME = "stdCalender";
const CUSTOMER_TOTALS = "totals";
const VERSION = 1;

let dbPromise;

const getDBPromise = () => {
  if (typeof window === "undefined") {
    return null; // Return null if it's not running in the browser
  }

  if (!dbPromise) {
    dbPromise = openDB(DB_NAME, VERSION, {
      upgrade(db) {
        if (!db.objectStoreNames.contains(STORE_NAME)) {
          const store = db.createObjectStore(STORE_NAME, {
            keyPath: "pkey",
          });
          store.createIndex("Master_code", "Master_code");
          store.createIndex("Business_unit", "Business_unit");
          store.createIndex("product_desc", "product_desc");
          store.createIndex("isLockedBy", "isLockedBy");
        }
        if (!db.objectStoreNames.contains(CALENDER_STORE_NAME)) {
          const efcCalenderStore = db.createObjectStore(CALENDER_STORE_NAME, {
            keyPath: "startweek",
          });
        }
        if (!db.objectStoreNames.contains(STD_CALENDER_STORE_NAME)) {
          const stdCalenderStore = db.createObjectStore(
            STD_CALENDER_STORE_NAME,
            {
              keyPath: "fiscalweek",
            }
          );
        }

        if (!db.objectStoreNames.contains(CUSTOMER_TOTALS)) {
          db.createObjectStore(CUSTOMER_TOTALS, {
            keyPath: "id",
            autoIncrement: true,
          });
        }
      },
    });
  }

  return dbPromise;
};

// Clear the idb
export const clearIDB = async () => {
  const db = await getDBPromise();
  if (!db) return;

  // Get all existing entries for the given customer_code
  const txDelete = db.transaction(STORE_NAME, "readwrite");
  const storeDelete = txDelete.objectStore(STORE_NAME);
  storeDelete.clear();
  await txDelete.done;

  // Get all existing entries for the efc calender
  const txDeleteEfcCalender = db.transaction(CALENDER_STORE_NAME, "readwrite");
  const storeEfcDelete = txDeleteEfcCalender.objectStore(CALENDER_STORE_NAME);
  storeEfcDelete.clear();
  await txDeleteEfcCalender.done;

  // Get all existing entries for the std calender
  const txDeleteStdCalender = db.transaction(
    STD_CALENDER_STORE_NAME,
    "readwrite"
  );
  const storeStdDelete = txDeleteStdCalender.objectStore(
    STD_CALENDER_STORE_NAME
  );
  storeStdDelete.clear();
  await txDeleteStdCalender.done;

  // Get all existing entries for the efc calender
  const txDeleteTotals = db.transaction(CUSTOMER_TOTALS, "readwrite");
  const storeTotalsDelete = txDeleteTotals.objectStore(CUSTOMER_TOTALS);
  storeTotalsDelete.clear();
  await txDeleteTotals.done;
};

// Add data to the database
export const addProductsToIdb = async (products) => {
  const db = await getDBPromise();
  if (!db) return;

  const tx = db.transaction(STORE_NAME, "readwrite");
  const store = tx.objectStore(STORE_NAME);
  await Promise.all(products.map((product) => store.put(product)));
  await tx.done;
};

// on change of selected customer, replace the data with the new one
export const replaceDataWithTheNew = async (newProducts) => {
  const db = await getDBPromise();
  if (!db) return;

  if (newProducts?.length === 0) return;

  const customer_code = newProducts[0].customer_code;

  // Get all existing entries for the given customer_code
  const txDelete = db.transaction(STORE_NAME, "readwrite");
  const storeDelete = txDelete.objectStore(STORE_NAME);
  storeDelete.clear();
  await txDelete.done;

  // Add new data
  const txAdd = db.transaction(STORE_NAME, "readwrite");
  const storeAdd = txAdd.objectStore(STORE_NAME);
  await Promise.all(newProducts.map((product) => storeAdd.put(product)));
  await txAdd.done;
};

// Get data by pkey
export const getProductFromIdbByPkey = async (pkey) => {
  const db = getDBPromise();
  if (!db) return null;

  return await db.get(STORE_NAME, pkey);
};

// Get multiple products by pkeys
export const getProductsFromIdbByPkeys = async (pkeys) => {
  const db = await getDBPromise();
  if (!db) return [];

  const transaction = db.transaction(STORE_NAME, "readonly");
  const store = transaction.objectStore(STORE_NAME);

  const promises = pkeys.map((pkey) => store.get(pkey));
  let results = await Promise.all(promises);
  results = results.filter((product) => product !== undefined); // Filter out any undefined results
  results = sortProductsByDescription(results);

  return results;
};

// Function to query data by master_code and optionally filter by business_unit
export const getProductsFromIdbByMasterCodeAndBusinessUnit = async (
  master_code = null,
  business_unit = null,
  getAll = false,
  productType,
  selectWhereTotalIsZero,
  selectWhereTotalIsNotZero,
  selectedQuarters
) => {
  const db = await getDBPromise();
  if (!db) return [];

  let result = [];

  if (master_code && business_unit) {
    // Query by master_code and filter by business_unit
    const index = db.transaction(STORE_NAME).store.index("Master_code");
    const allMasterCodeRecords = await index.getAll(master_code);
    result = allMasterCodeRecords.filter(
      (record) => record.Business_unit === business_unit
    );
  } else if (master_code) {
    // Query by master_code only
    const index = db.transaction(STORE_NAME).store.index("Master_code");
    result = await index.getAll(master_code);
  } else if (business_unit) {
    // Query by business_unit only
    const index = db.transaction(STORE_NAME).store.index("Business_unit");
    result = await index.getAll(business_unit);
  } else {
    // If both are null, return all records
    const store = db.transaction(STORE_NAME).store;

    result = await store.getAll();
  }

  if (productType) {
    //TODO: Filter by product type
  }

  if (!selectWhereTotalIsZero || !selectWhereTotalIsNotZero) {
    result = filtersBasedOnZeros(
      result,
      selectWhereTotalIsZero,
      selectWhereTotalIsNotZero,
      selectedQuarters
    );
  }

  result = sortProductsByDescription(result);

  if (!getAll) {
    return result;
  }
  return result;
};

function sortProductsByDescription(products) {
  return products.sort((a, b) => {
    if (a.product_desc < b.product_desc) {
      return -1;
    }
    if (a.product_desc > b.product_desc) {
      return 1;
    }
    return 0;
  });
}

export const updatePromotionByPkey = async (pkey, updatedPromotion) => {
  const db = await getDBPromise();
  if (!db) return false;

  const tx = db.transaction(STORE_NAME, "readwrite");
  const store = tx.objectStore(STORE_NAME);

  // Retrieve the object containing the promotions array
  const objectToUpdate = await store.get(pkey);
  if (!objectToUpdate) return false;

  // Update the relevant promotion in the promotions array
  const promotions = objectToUpdate.promotions;
  const promotionIndex = promotions.findIndex(
    (promotion) =>
      promotion.promo_start_week === updatedPromotion.promo_start_week
  );

  if (promotionIndex !== -1) {
    // Update the existing promotion
    promotions[promotionIndex] = {
      ...promotions[promotionIndex],
      ...updatedPromotion,
    };
  } else {
    // Push the new promotion
    promotions.push(updatedPromotion);
  }

  objectToUpdate.promotions = promotions;

  // Save the updated object back into IndexedDB
  await store.put(objectToUpdate);

  await tx.done;
  return true;
};

export const removePromotionByPkeyAndWhatIfId = async (pkey, whatIfId) => {
  const db = await getDBPromise();
  if (!db) return false;

  const tx = db.transaction(STORE_NAME, "readwrite");
  const store = tx.objectStore(STORE_NAME);

  // Retrieve the object containing the promotions array
  const objectToUpdate = await store.get(pkey);
  if (!objectToUpdate) return false;

  // Filter out the promotion with the given what_if_id
  const promotions = objectToUpdate.promotions.filter(
    (promotion) => promotion.what_if_id !== whatIfId
  );

  // If the length of promotions is unchanged, the promotion was not found
  if (promotions.length === objectToUpdate.promotions.length) {
    return false;
  }

  // Update the promotions array
  objectToUpdate.promotions = promotions;

  // Save the updated object back into IndexedDB
  await store.put(objectToUpdate);

  await tx.done;
  return true;
};

export const addLockByPkey = async (pkey, user) => {
  const db = await getDBPromise();
  if (!db) return false;

  const tx = db.transaction(STORE_NAME, "readwrite");
  const store = tx.objectStore(STORE_NAME);

  // Retrieve the object to update
  const objectToUpdate = await store.get(pkey);
  if (!objectToUpdate) return false;

  // Update the relevant lock in products
  objectToUpdate.isLockedBy = user;

  // Save the updated object back into IndexedDB
  await store.put(objectToUpdate);

  await tx.done;
  return true;
};

export const removeLocksByUser = async (user) => {
  const db = await getDBPromise();
  if (!db) return false;

  const tx = db.transaction(STORE_NAME, "readwrite");
  const store = tx.objectStore(STORE_NAME);

  // Retrieve all objects where isLockedBy is equal to the passed user
  const index = store.index("isLockedBy");
  const allProductsLockedByUser = await index.getAll(user);

  if (!allProductsLockedByUser || allProductsLockedByUser.length === 0) return false;

  // Update the isLockedBy field to be an empty string
  allProductsLockedByUser.forEach(async (product) => {
    product.isLockedBy = "";
    await store.put(product);
  });

  await tx.done;
  return true;
};


export const addNextQuarterByPkey = async (pkey, quarter, data) => {
  const db = await getDBPromise();
  if (!db) return false;

  const tx = db.transaction(STORE_NAME, "readwrite");
  const store = tx.objectStore(STORE_NAME);

  // Retrieve the object to update
  const objectToUpdate = await store.get(pkey);
  if (!objectToUpdate) return false;

  // Update the relevant lock in products
  objectToUpdate.quarters[quarter] = data;

  // Save the updated object back into IndexedDB
  await store.put(objectToUpdate);

  await tx.done;
  return true;
};

// on change of quarter, replace the calender data with the new one
export const replaceEfcCalenderWithTheNew = async (newData) => {
  const db = await getDBPromise();
  if (!db) return;

  if (newData.length === 0) return;

  // Get all existing entries for the given customer_code
  const txDelete = db.transaction(CALENDER_STORE_NAME, "readwrite");
  const storeDelete = txDelete.objectStore(CALENDER_STORE_NAME);
  storeDelete.clear();
  await txDelete.done;

  // Add new data
  const txAdd = db.transaction(CALENDER_STORE_NAME, "readwrite");
  const storeAdd = txAdd.objectStore(CALENDER_STORE_NAME);
  await Promise.all(newData.map((data) => storeAdd.put(data)));
  await txAdd.done;
};

// on change of quarter, replace the std calender data with the new one
export const replaceStdCalenderWithTheNew = async (newData) => {
  
  const db = await getDBPromise();
  if (!db) return;

  if (newData.length === 0) return;
  // Get all existing entries for the given customer_code
  const txDelete = db.transaction(STD_CALENDER_STORE_NAME, "readwrite");
  const storeDelete = txDelete.objectStore(STD_CALENDER_STORE_NAME);
  storeDelete.clear();
  await txDelete.done;

  // Add new data
  const txAdd = db.transaction(STD_CALENDER_STORE_NAME, "readwrite");
  const storeAdd = txAdd.objectStore(STD_CALENDER_STORE_NAME);
  await Promise.all(newData.map((data) => storeAdd.put(data)));
  await txAdd.done;
};

// on change of customer, replace the customerTotals with the new one
export const replaceCustomerTotalsWithTheNew = async (newData) => {
  const db = await getDBPromise();
  if (!db) return;

  if (newData.length === 0) return;

  // Get all existing entries for the given customer_code
  const txDelete = db.transaction(CUSTOMER_TOTALS, "readwrite");
  const storeDelete = txDelete.objectStore(CUSTOMER_TOTALS);
  storeDelete.clear();
  await txDelete.done;

  // Add new data
  const txAdd = db.transaction(CUSTOMER_TOTALS, "readwrite");
  const storeAdd = txAdd.objectStore(CUSTOMER_TOTALS);
  await storeAdd.put(newData);
  await txAdd.done;
};

const filterByQuarters = (data, quarters) => {
  const result = data.reduce(
    (acc, item, index) => {
      if (quarters.includes(item.fiscalquarter)) {
        acc.selectedItems.push(item);
      }
      return acc;
    },
    { selectedItems: [] }
  );

  return result;
};

const getAllDataFromStore = async (db, storeName) => {
  return await db.getAll(storeName);
};

// Function to query data by quarters and filter accordingly
export const getCalendersByQuarters = async (quarters) => {
  const db = await getDBPromise();
  if (!db) return [];

  if (!quarters) return [];

  // Fetch all data from both stores
  const allCalendersWeeks = await getAllDataFromStore(db, CALENDER_STORE_NAME);
  const allStdCalenderWeeks = await getAllDataFromStore(
    db,
    STD_CALENDER_STORE_NAME
  );

  // Filter the calendar data by quarters
  const filteredData = filterByQuarters(allCalendersWeeks, quarters);

  // Filter the standard calendar based on the indexes of the filtered calendar data
  const filteredStdCalender = filterByQuarters(allStdCalenderWeeks, quarters);

  return {
    efCCalender: filteredData.selectedItems,
    stdCalender: filteredStdCalender.selectedItems.sort(
      (a, b) => a.fiscalquarter - b.fiscalquarter
    ),
  };
};

export const getFiltredProductsByNameFromIdb = async (
  searchTerm,
  selectedBusinessUnit,
  selectedMasterCode,
  productType,
  selectWhereTotalIsZero,
  selectWhereTotalIsNotZero,
  selectedQuarters
) => {
  const db = await getDBPromise();
  if (!db) return [];

  const transaction = db.transaction(STORE_NAME, "readonly");
  const store = transaction.objectStore(STORE_NAME);

  const allRecords = await store.getAll(); // Get all records

  // Filter records where product_desc contains the search term
  let filteredResults = allRecords.filter(
    (record) =>
      record.product_desc.toLowerCase().includes(searchTerm.toLowerCase()) &&
      (selectedBusinessUnit
        ? record.Business_unit === selectedBusinessUnit
        : true) &&
      (selectedMasterCode ? record.Master_code === selectedMasterCode : true)
  );

  if (!selectWhereTotalIsZero || !selectWhereTotalIsNotZero) {
    filteredResults = filtersBasedOnZeros(
      filteredResults,
      selectWhereTotalIsZero,
      selectWhereTotalIsNotZero,
      selectedQuarters
    );
  }

  filteredResults = sortProductsByDescription(filteredResults);

  return filteredResults;
};

function getTotalVolume(data) {
  return data
    .map((week) => {
      const volumeEntry = week.data.find((entry) => entry.name === "volume");
      return volumeEntry ? volumeEntry.value : 0;
    })
    .reduce((total, volume) => total + volume, 0);
}

const getTotalPromoVolume = (promotions) => {
  return promotions.reduce((totalVolume, promotion) => {
    const weeks = Object.values(promotion.weekData);
    const weekVolume = weeks.reduce(
      (sum, week) => sum + week.current.volume,
      0
    );
    return totalVolume + weekVolume;
  }, 0);
};

export const filtersBasedOnZeros = (
  records,
  selectWhereTotalIsZero,
  selectWhereTotalIsNotZero,
  selectedQuarters,
  selectedMasterCode,
  selectedBusinessUnit
) => {
  const result = records.filter((prod) => {
    let combinedQuarter = [];
    Object.keys(prod.quarters).forEach((quarter) => {
      if (selectedQuarters.includes(quarter.toLowerCase())) {
        combinedQuarter = [...combinedQuarter, ...prod.quarters[quarter]];
      }
    });

    if (!!selectedMasterCode && prod.Master_code !== selectedMasterCode) {
      return false;
    }

    if (!!selectedBusinessUnit && prod.Business_unit !== selectedBusinessUnit) {
      return false;
    }

    let volumeTotal = getTotalVolume(combinedQuarter);

    if (prod.promotions?.length > 0) {
      const volumePoromotions = prod.promotions.filter(
        (promo) =>
          promo.type_id === WHATIF_TYPE_PROMO_ID ||
          promo.type_id === WHATIF_TYPE_RR_VOLUME_ID ||
          promo.type_id === WHATIF_TYPE_SUPPLIR_ISSUE_ID
      );

      volumeTotal += getTotalPromoVolume(volumePoromotions);
    }

    if (
      !selectWhereTotalIsZero &&
      selectWhereTotalIsNotZero &&
      volumeTotal > 0
    ) {
      return true;
    } else if (
      selectWhereTotalIsZero &&
      !selectWhereTotalIsNotZero &&
      volumeTotal <= 0
    ) {
      return true;
    }

    return false;
  });

  return result;
};
