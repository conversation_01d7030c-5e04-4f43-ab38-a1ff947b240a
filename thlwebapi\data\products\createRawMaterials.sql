INSERT INTO
  [dbo].[products] (
    [type],
    [brand],
    [product_type],
    [reason],
    [originator],
    [originator_email],
    [product_description],
    [suppliers_description],
    [product_code],
    [group_id],
    [mark_variaty],
    [count_or_size],
    [units_in_outer],
    [cases_per_pallet],
    [outer_net_weight],
    [outer_gross_weight],
    [sub_product_code],
    [temperature_grade],
    [class_required],
    [intrastat_commodity_code],
    [organic_certificate],
    [is_classified_allergic_fsa14],
    [coo],
    [caliber_size],
    [end_customer],
    [variety],
    [company_name],
    [status],
    [delivery_date],
    [requestor],
    [created_date],
    [request_no],
    [submitted_to_iss],
    [email_comment]
  )
  OUTPUT INSERTED.id, INSERTED.request_no
VALUES
  (
    @type,
    @brand,
    @product_type,
    @reason,
    @originator,
    @originator_email,
    @product_description,
    @suppliers_description,
    @product_code,
    @group_id,
    @mark_variaty,
    @count_or_size,
    @units_in_outer,
    @cases_per_pallet,
    @outer_net_weight,
    @outer_gross_weight,
    @sub_product_code,
    @temperature_grade,
    @class_required,
    @intrastat_commodity_code,
    @organic_certificate,
    @is_classified_allergic_fsa14,
    @coo,
    @caliber_size,
    @end_customer,
    @variety,
    @company_name,
    @status,
    @delivery_date,
    @requestor,
    GETDATE(),
    @request_no,
    @submitted_to_iss,
    @email_comment
  );