-- This query only updates the status for a packaging request
-- Used for action ID 3 (Setup Completed) where we don't touch the product_packaging_request table

-- First, update all previous statuses to not be the latest
UPDATE product_packaging_status
SET is_latest = 0
WHERE packaging_request_id = @packaging_request_id;

-- Then insert the new status
INSERT INTO product_packaging_status (
    packaging_request_id,
    action_id,
    actioned_by_email,
    actioned_by_name,
    comment,
    is_latest
) 
VALUES (
    @packaging_request_id,
    @actionId,
    @actionedByEmail,
    @actionedByName,
    COALESCE(@comment, NULL),
    1
);

-- Return the request number for reference
SELECT request_no 
FROM product_packaging_request 
WHERE id = @packaging_request_id;