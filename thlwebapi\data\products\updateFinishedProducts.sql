UPDATE
    [dbo].[products]
SET
    [reason] = @reason,
    [delivery_date] = @delivery_date,
    [originator] = @originator,
    [product_code] = @product_code,
    [group_id] = @group_id,
    [product_description] = @product_description,
    [mark_variaty] = @mark_variaty,
    [units_in_outer] = @units_in_outer,
    [cases_per_pallet] = @cases_per_pallet,
    [outer_net_weight] = @outer_net_weight,
    [outer_gross_weight] = @outer_gross_weight,
    [sub_product_code] = @sub_product_code,
    [temperature_grade] = @temperature_grade,
    [class_required] = @class_required,
    [organic_certificate] = @organic_certificate,
    [is_classified_allergic_fsa14] = @is_classified_allergic_fsa14,
    [brand] = @brand,
    [end_customer] = @end_customer,
    [customer_description] = @customer_description,
    [finished_pack_size] = @finished_pack_size,
    [is_box_type_colours] = @is_box_type_colours,
    [box_type_other_comments] = @box_type_other_comments,
    [packaging_types] = @packaging_types,
    [description_net_film] = @description_net_film,
    [punnet_tray_type] = @punnet_tray_type,
    [machine_format] = @machine_format,
    [pack_label_type] = @pack_label_type,
    [end_label_description] = @end_label_description,
    [promotional_label_desc] = @promotional_label_desc,
    [label_scan_grade] = @label_scan_grade,
    [occ_box_end] = @occ_box_end,
    [ean_pack_label] = @ean_pack_label,
    [tpnd_trading_unit] = @tpnd_trading_unit,
    [tpnb_base_unit] = @tpnb_base_unit,
    [display_until] = @display_until,
    [best_before] = @best_before,
    [is_other_date_code_type] = @is_other_date_code_type,
    [f_code] = @f_code,
    [supplier_site_code] = @supplier_site_code,
    [update_date] = GETDATE(),
    [submitted_to_iss] = @submitted_to_iss
WHERE
    [id] = @id;