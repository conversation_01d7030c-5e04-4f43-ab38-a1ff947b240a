BEGIN TRANSACTION;

	BEGIN TRY
		UPDATE product_nvariety_status
		SET is_latest = 0
		WHERE variety_id = @varietyId;

	INSERT INTO
			product_nvariety_status (
				variety_id,
				action_id,
				actioned_by_email,
				actioned_by_name,
				comment
			) 
		VALUES
			(
				@varietyId,
				@actionId,
				@actionedByEmail,
				@actionedByName,
				@comment
			);
    COMMIT TRANSACTION;
    PRINT 'Transaction committed.';
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;

    PRINT 'Transaction rolled back.';
    PRINT ERROR_MESSAGE();
END CATCH;

