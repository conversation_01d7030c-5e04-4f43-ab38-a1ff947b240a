import "@/styles/globals.css";
import { useState, useEffect, Fragment } from "react";
import Msal<PERSON>uth<PERSON>rovider from "@/utils/auth/msalProvider";
import { LoadingProvider } from "@/utils/loaders/loadingContext";
import OverlaySpinner from "@/utils/loaders/overlaySpinner";
import { SecureThemeProvider } from "@/utils/secureThemeContext";
import { PermissionsProvider } from "@/utils/rolePermissionsContext";
import { ServiceCustomerProvider } from '@/utils/serviceCustomerContext';

export default function App({ Component, pageProps }) {
  const [isOpen, setIsOpen] = useState(true);
  const isOnline = useNetwork();

  function useNetwork(){
    if (typeof window !== "undefined") {
      // Client-side-only code
      const [isOnline, setNetwork] = useState(window.navigator.onLine);
      useEffect(() => {
        window.addEventListener("offline", 
          () => setNetwork(window.navigator.onLine)
        );
        window.addEventListener("online", 
          () => setNetwork(window.navigator.onLine)
        );
      });
      return isOnline;
    }
  };

  const closeModal = () => {
      setIsOpen(false);
  }

  useEffect(()=>{
    if(isOnline){
      <div classNam="no-connection">
        <p>No Internet Connection</p>
        <noConnectionAlertBox isOpen={isOpen} closeModal={closeModal} />
      </div>
    }
  },[])

  return (
    <MsalAuthProvider>
      <SecureThemeProvider initialTheme={pageProps.userData?.theme}>
        <PermissionsProvider>
          <LoadingProvider>
            <ServiceCustomerProvider>
              <OverlaySpinner />
              <Component {...pageProps} />
            </ServiceCustomerProvider>
          </LoadingProvider>
        </PermissionsProvider>
      </SecureThemeProvider>
    </MsalAuthProvider>
  );
}
