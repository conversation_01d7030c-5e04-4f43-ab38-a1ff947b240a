import { createContext, useContext, useState, useEffect } from 'react';
import { getCurrentUser, isAuthenticated as checkAuth } from './secureStorage';

const SecureUserContext = createContext();

export const SecureUserProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Initialize user data on app load
  useEffect(() => {
    initializeUser();
  }, []);

  const initializeUser = async () => {
    try {
      setIsLoading(true);
      const currentUser = await getCurrentUser();
      
      if (currentUser) {
        setUser(currentUser);
        setIsAuthenticated(true);
      } else {
        setUser(null);
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error('Failed to initialize user:', error);
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Update user data (replaces updateToken)
  const updateUser = (newUserData) => {
    setUser(newUserData);
    setIsAuthenticated(!!newUserData);
  };

  // Clear user data (logout)
  const clearUser = () => {
    setUser(null);
    setIsAuthenticated(false);
  };

  // Get user data securely (replaces getCookieData("user"))
  const getUserData = () => {
    return user;
  };

  // Get specific user properties (maintains backward compatibility)
  const getUserProperty = (property) => {
    if (!user) return null;
    
    // Map old property names to new structure if needed
    const propertyMap = {
      'user_id': 'id',
      'role_id': 'role', 
      'email': 'email',
      'name': 'name'
    };
    
    const mappedProperty = propertyMap[property] || property;
    return user[mappedProperty];
  };

  // Temporary: Get data from localStorage (for migration period)
  const getTemporaryData = (key) => {
    if (typeof window === 'undefined') return null;
    
    try {
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : data;
    } catch (error) {
      return localStorage.getItem(key);
    }
  };

  // Check if user has specific role
  const hasRole = (roleId) => {
    if (!user || !user.roles) return false;
    return user.roles.includes(roleId) || user.role === roleId;
  };

  // Check if user is super user
  const isSuperUser = () => {
    // You may need to adjust this based on your role system
    return user?.role === 1 || hasRole('admin') || hasRole('superuser');
  };

  const contextValue = {
    // New secure methods
    user,
    isLoading,
    isAuthenticated,
    updateUser,
    clearUser,
    getUserData,
    getUserProperty,
    hasRole,
    isSuperUser,
    initializeUser,
    
    // Temporary backward compatibility (remove after migration)
    getTemporaryData,
    
    // Legacy compatibility (gradually replace these)
    userDetails: user, // Maps to old userDetails
    updateToken: updateUser // Maps to old updateToken
  };

  return (
    <SecureUserContext.Provider value={contextValue}>
      {children}
    </SecureUserContext.Provider>
  );
};

export const useSecureUser = () => {
  const context = useContext(SecureUserContext);
  if (!context) {
    throw new Error('useSecureUser must be used within a SecureUserProvider');
  }
  return context;
};