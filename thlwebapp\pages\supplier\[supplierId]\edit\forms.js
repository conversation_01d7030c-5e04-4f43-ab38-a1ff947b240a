import ComplianceSection from "@/components/ComplianceSection";
import FinancialsSection from "@/components/FinancialsSection";
import GeneralSection from "@/components/GeneralSection";
import ProcurementSection from "@/components/ProcurementSection";
import { useRouter } from "next/router";
import React, { useState, useEffect, useCallback, useMemo } from "react";
import Steps from "@/components/Steps";
import Layout from "@/components/Layout";
import { ThreeCircles } from "react-loader-spinner";
import { usePermissions } from "@/utils/rolePermissionsContext";
import { logout } from "@/utils/secureStorage";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useLoading } from "@/utils/loaders/loadingContext";

export default function SupplierForms({ userData }) {
  const router = useRouter();
  const { permissions, updatePermissions } = usePermissions();
  const [supplierId, setSupplierId] = useState(router.query?.supplierId);
  const [data, setData] = useState(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [roleIds, setRoleIds] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isProducerSupplier, setIsProducerSupplier] = useState(false);
  const [allowedSections, setAllowedSections] = useState(false);
  const [dropdowns, setDropdowns] = useState([]);
  const { setIsLoading } = useLoading();
  const [navType, setNavType] = useState("");
  const [company, setCompany] = useState("");
  const [isCommonError, setCommonError] = useState("");
  const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8081';

  // Helper function to handle session errors and logout
  const handleSessionError = async () => {
    toast.error("Your session has expired. Please log in again.");
    setTimeout(async () => {
      await logout();
      router.push('/login');
    }, 3000);
  };

  const rolePermissions = {
    ...permissions,
  };

  useEffect(() => {
    const sectionFromQuery = router.query.section;

    if (sectionFromQuery) {
      if (typeof window !== "undefined") {
        localStorage.setItem("current", parseInt(sectionFromQuery));
      }

      setCurrentStep(parseInt(sectionFromQuery));
    }
    setLoading(false);
  }, [router.query.section]);

  const producerOrGrowerRoleIds = [2, 3];
  const hasProducerOrGrowerRole =
    roleIds.length === 1 && producerOrGrowerRoleIds?.includes(roleIds[0]);

  useEffect(() => {
    let allowedSections;
    // Check for role ID 1 first
    if (roleIds?.includes(1)) {
      allowedSections = roleIds?.map((roleId) => rolePermissions[roleId]);
    } else if (hasProducerOrGrowerRole && isProducerSupplier) {
      // Only allow sections related to the selected Grower or Producer role
      allowedSections = rolePermissions[roleIds[0]];
    } else if (hasProducerOrGrowerRole && !isProducerSupplier) {
      localStorage.setItem("allowedSections", []);
      router.push({
        pathname: `/supplier/${supplierId}/confirm`,
      });
      return; // Exit the useEffect to avoid setting allowedSections again
    } else {
      allowedSections = roleIds?.map((roleId) => rolePermissions[roleId]);
    }

    setAllowedSections(allowedSections);
    setLoading(false);
    setIsLoading(false);
  }, [roleIds, isProducerSupplier]); // Include isProducerSupplier in the dependency array

  const fetchData = async () => {
    try {
      let curr = "";
      if (typeof window !== "undefined") {
        curr = localStorage.getItem("curr");
      }
      
      const response = await fetch(
        `${apiBase}/api/suppliers/get-supplier-by-id/${supplierId}`,
        {
          method: "GET",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          credentials: 'include',
        }
      );

      if (response.status === 400) {
        toast.error("There was an error with your request. Please check your data and try again.");
      } else if (response.status === 401) {
        await handleSessionError();
        return;
      }

      if (!response.ok) {
        throw new Error(`Request failed with status ${response.status}`);
      }

      const result = await response.json();
      const parsedRoles = JSON.parse(result[0]?.role_ids);
      const formattedRoles = parsedRoles?.map((row) => row.role_id);

      const roleIds = formattedRoles;
      setRoleIds(roleIds);
      setIsProducerSupplier(result[0].product_supplier);
      setData(result);

      const hasProducerOrGrowerRole = roleIds.some((roleId) => {
        return producerOrGrowerRoleIds?.includes(roleId);
      });

      if (!roleIds?.includes(1) && !roleIds?.includes(6) && curr != 3) {
        if (hasProducerOrGrowerRole && result[0].product_supplier) {
          if (typeof window !== "undefined") {
            localStorage.setItem("current", 3);
          }

          setCurrentStep(3);
        }
      }

      setLoading(false);
    } catch (error) {
      console.log("error", error);
      setLoading(false);
      toast.error("Error fetching data in supplier forms file:", error.message, {
        position: "top-right",
      });
    }
  };

  const getData = useCallback(async (prophets) => {
    const allDropDowns = [
      "country",
      "currency",
      "country_code",
      "haulier",
      "delivery_term",
      "transport",
      "type_contact",
      "payment_type",
      "agreed_term",
      "brand",
      "end_customer",
    ];

    const requestBody = {
      allDropDowns,
      prophets,
    };

    try {
      const res = await fetch(`${apiBase}/api/suppliers/get-all-dropdown-list`, {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        credentials: 'include', // Use session authentication
        body: JSON.stringify(requestBody),
      });

      if (res.status === 400) {
        toast.error(
          "There was an error with your request. Please check your data and try again."
        );
      } else if (res.status === 401) {
        await handleSessionError();
        return;
      }
      if (res.status === 200) {
        return res.json();
      }
      throw new Error("Failed to fetch data");
    } catch (error) {
      console.error("Error fetching dropdown data:", error);
      setCommonError(error.message);
    }
  }, [apiBase, handleSessionError]);

  // Get prophets from localStorage instead of cookies
  const prophetsIdFromLocalStorage = useMemo(() => {
    if (typeof window !== "undefined") {
      const storedProphets = localStorage.getItem("prophet");
      return storedProphets ? JSON.parse(storedProphets) : "";
    }
    return "";
  }, []);

  useEffect(() => {
    if (prophetsIdFromLocalStorage) {
      getData(prophetsIdFromLocalStorage).then((data) => {
        if (data) {
          setDropdowns(data);
        }
      });
    }
  }, [prophetsIdFromLocalStorage]);

  const fetchRolePermissions = async () => {
    try {
      const response = await fetch(
        `${apiBase}/api/suppliers/get-role-permissions`,
        {
          method: "GET",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          credentials: 'include', // Use session authentication
        }
      );

      if (response.status === 400 || response.status === 401) {
        await handleSessionError();
        return;
      }

      if (!response.ok) {
        throw new Error(`Request failed with status ${response.status}`);
      }

      const result = await response.json();
      const rolePermissions = {};
      for (const row of result) {
        rolePermissions[row.role_id] = row.sections.split(","); // Split sections string into an array
      }

      updatePermissions(rolePermissions);
    } catch (error) {
      console.error("Error fetching role permissions:", error);
    }
  };

  useEffect(() => {
    // Get company from session data instead of cookies
    const company = userData?.company || "";
    setCompany(company);
  }, [userData]);

  useEffect(() => {
    fetchRolePermissions();

    const { supplierId } = router.query;
    setSupplierId(supplierId);

    if (typeof window !== "undefined") {
      setIsEdit(localStorage.getItem("isEdit"));
    }
    
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          `${apiBase}/api/suppliers/get-supplier-by-id/${supplierId}`,
          {
            method: "GET",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
            },
            credentials: 'include', // Use session authentication
          }
        );

        if (response.status === 400 || response.status === 401) {
          await handleSessionError();
          return;
        }

        if (!response.ok) {
          throw new Error(`Request failed with status ${response.status}`);
        }

        const result = await response.json();

        const parsedRoles = JSON.parse(result[0].role_ids);
        const formattedRoles = parsedRoles?.map((row) => row.role_id);

        const roleIds = formattedRoles;
        setRoleIds(roleIds);
        setIsProducerSupplier(result[0].product_supplier);
        setData(result);

        const hasProducerOrGrowerRole = roleIds.some((roleId) => {
          return producerOrGrowerRoleIds?.includes(roleId);
        });

        if (
          !roleIds?.includes(1) &&
          !roleIds?.includes(4) &&
          !roleIds?.includes(5) &&
          !roleIds?.includes(6) &&
          !router.query.section
        ) {
          if (hasProducerOrGrowerRole && result[0].product_supplier) {
            if (typeof window !== "undefined") {
              localStorage.setItem("current", 3);
            }

            setCurrentStep(1);
          }
        }

        setLoading(false);
      } catch (error) {
        setLoading(false);
        console.error("Error fetching supplier data:", error);
        toast.error(
          "Error fetching data in supplier forms file",
          {
            position: "top-right",
          }
        );
      }
    };

    if (supplierId) {
      fetchData();
    }
  }, [router.query, userData]);

  useEffect(() => {
    if (isCommonError) {
      toast.error("Connection lost or session expired", {
        position: "top-right",
      });
      setTimeout(async () => {
        await handleSessionError();
      }, 3000);
    }
  }, [isCommonError]);

  if (!data) {
    return null;
  }

  const sections = ["General", "Financials", "Compliance", "Procurement"];
  function getStepSectionName(step) {
    const sectionNames = {
      1: "General",
      2: "Financials",
      3: "Compliance",
      4: "Procurement",
    };

    return sectionNames[step] || null;
  }
  const uniqueAllowedSections = sections.filter((section) =>
    allowedSections?.flat().includes(section)
  );
  localStorage.setItem("allowedSections", uniqueAllowedSections);

  const handleSubmit = (step) => {
    fetchData();

    let nextStep = step + 1;
    let nextSectionName = getStepSectionName(nextStep);

    while (
      !uniqueAllowedSections.includes(nextSectionName) &&
      nextStep <= sections.length
    ) {
      nextStep++;
      nextSectionName = getStepSectionName(nextStep);
    }

    if (uniqueAllowedSections.includes(nextSectionName)) {
      if (typeof window !== "undefined") {
        localStorage.setItem("current", nextStep);
      }

      setCurrentStep(nextStep);
    } else {
      router.push({
        pathname: `/supplier/${supplierId}/confirm`,
      });
    }
  };

  const handlePrevious = (step) => {
    let previousAllowedStep = step - 1;

    while (previousAllowedStep >= 1) {
      const previousSectionName = getStepSectionName(previousAllowedStep);

      if (uniqueAllowedSections.includes(previousSectionName)) {
        if (typeof window !== "undefined") {
          localStorage.setItem("current", previousAllowedStep);
        }
        setCurrentStep(previousAllowedStep);
        return;
      }

      previousAllowedStep--;
    }
    router.back();
  };

  if (!supplierId) {
    return <></>;
  }

  return (
    <>
      <ToastContainer />
      <Layout userData={userData}>
        {loading ? (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: "100vh",
            }}
          >
            <ThreeCircles
              color="#002D73"
              height={50}
              width={50}
              visible={true}
              ariaLabel="oval-loading"
              secondaryColor="#0066FF"
              strokeWidth={2}
              strokeWidthSecondary={2}
            />
          </div>
        ) : (
          <div>
            <Steps
              activeStep={currentStep}
              uniqueAllowedSections={uniqueAllowedSections}
              allowedSectionsInOrder={sections}
            />
            {currentStep === 1 &&
              allowedSections?.flat().includes("General") && (
                <GeneralSection
                  data={data}
                  dropdowns={dropdowns}
                  onSubmit={() => handleSubmit(1)}
                  isEdit={isEdit}
                  navType={navType}
                  setNavType={setNavType}
                  userData={userData}
                />
              )}
            {currentStep === 2 &&
              allowedSections?.flat().includes("Financials") && (
                <FinancialsSection
                  data={data}
                  setNavType={setNavType}
                  onSubmit={() => handleSubmit(2)}
                  onPrev={() => handlePrevious(2)}
                  isEdit={isEdit}
                  userData={userData}
                  dropdowns={dropdowns}
                  navType={navType}
                  company={company}
                />
              )}
            {currentStep === 3 &&
              allowedSections?.flat().includes("Compliance") && (
                <ComplianceSection
                  data={data}
                  setNavType={setNavType}
                  onSubmit={() => handleSubmit(3)}
                  onPrev={() => handlePrevious(3)}
                  isEdit={isEdit}
                  navType={navType}
                  userData={userData}
                />
              )}
            {currentStep === 4 &&
              allowedSections?.flat().includes("Procurement") && (
                <ProcurementSection
                  data={data}
                  uniqueAllowedSections={uniqueAllowedSections}
                  onPrev={() => handlePrevious(4)}
                  isEdit={isEdit}
                  dropdowns={dropdowns}
                />
              )}
          </div>
        )}
      </Layout>
    </>
  );
}

export const getServerSideProps = async (context) => {
  try {
    // Use secure session validation
    const sessionId = context.req.cookies.thl_session;
    
    if (!sessionId) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
          permanent: false,
        },
      };
    }

    // Validate session with our backend API
    const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8081';
    
    try {
      const response = await fetch(`${apiBase}/api/auth/me`, {
        method: 'GET',
        headers: {
          'Cookie': `thl_session=${sessionId}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        // Session invalid or expired - redirect to login
        return {
          redirect: {
            destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
            permanent: false,
          },
        };
      }

      const { user } = await response.json();
      // Check if user has permission to access supplier forms
      // Typically all authenticated users can access supplier forms, but you can add role restrictions here if needed
      
      return {
        props: {
          userData: user,
        },
      };

    } catch (fetchError) {
      console.error('Session validation failed:', fetchError);
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
          permanent: false,
        },
      };
    }

  } catch (error) {
    console.error('Authentication error:', error);
    return {
      redirect: {
        destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
        permanent: false,
      },
    };
  }
};
