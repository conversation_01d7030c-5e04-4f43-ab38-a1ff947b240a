INSERT INTO
    [dbo].[products] (
        [reason],
        [delivery_date],
        [originator],
        [type],
        [product_code],
        [group_id],
        [product_description],
        [mark_variaty],
        [units_in_outer],
        [cases_per_pallet],
        [outer_net_weight],
        [outer_gross_weight],
        [sub_product_code],
        [temperature_grade],
        [class_required],
        [organic_certificate],
        [is_classified_allergic_fsa14],
        [brand],
        [end_customer],
        [customer_description],
        [finished_pack_size],
        [is_box_type_colours],
        [box_type_other_comments],
        [packaging_types],
        [description_net_film],
        [punnet_tray_type],
        [machine_format],
        [pack_label_type],
        [end_label_description],
        [promotional_label_desc],
        [label_scan_grade],
        [occ_box_end],
        [ean_pack_label],
        [tpnd_trading_unit],
        [tpnb_base_unit],
        [display_until],
        [best_before],
        [is_other_date_code_type],
        [f_code],
        [supplier_site_code],
        [company_name],
        [status],
        [requestor],
        [created_date],
        [request_no],
        [submitted_to_iss]
    )
    OUTPUT INSERTED.id, INSERTED.request_no
VALUES
    (
        @reason,
        @delivery_date,
        @originator,
        @type,
        @product_code,
        @group_id,
        @product_description,
        @mark_variaty,
        @units_in_outer,
        @cases_per_pallet,
        @outer_net_weight,
        @outer_gross_weight,
        @sub_product_code,
        @temperature_grade,
        @class_required,
        @organic_certificate,
        @is_classified_allergic_fsa14,
        @brand,
        @end_customer,
        @customer_description,
        @finished_pack_size,
        @is_box_type_colours,
        @box_type_other_comments,
        @packaging_types,
        @description_net_film,
        @punnet_tray_type,
        @machine_format,
        @pack_label_type,
        @end_label_description,
        @promotional_label_desc,
        @label_scan_grade,
        @occ_box_end,
        @ean_pack_label,
        @tpnd_trading_unit,
        @tpnb_base_unit,
        @display_until,
        @best_before,
        @is_other_date_code_type,
        @f_code,
        @supplier_site_code,
        @company_name,
        @status,
        @requestor,
        GETDATE(),
        @request_no,
        @submitted_to_iss
    )