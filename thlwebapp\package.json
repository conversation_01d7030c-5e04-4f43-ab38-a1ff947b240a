{"name": "th<PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@azure/msal-browser": "^3.6.0", "@azure/msal-react": "^2.0.8", "@fluentui/react-components": "^9.54.2", "@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-regular-svg-icons": "^6.4.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/react-fontawesome": "^0.2.0", "@headlessui/react": "^1.7.17", "@types/react-select": "^5.0.1", "add": "^2.0.6", "ag-grid-community": "^30.2.1", "ag-grid-enterprise": "^31.0.3", "ag-grid-react": "^30.2.1", "date-fns": "^3.3.1", "dirty-form": "^0.4.0", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "idb": "^8.0.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.30.1", "multiselect-react-dropdown": "^2.0.25", "next": "13.5.2", "react": "^18", "react-date-range": "^2.0.1", "react-dom": "^18", "react-fontawesome": "^1.7.1", "react-loader-spinner": "^6.1.1-0", "react-microsoft-login": "^2.0.1", "react-responsive": "^9.0.2", "react-select": "^5.8.0", "react-tiny-popover": "^8.0.4", "react-toastify": "^9.1.3", "socket.io-client": "^4.7.5", "styled-components": "^6.1.11", "xlsx": "^0.18.5"}, "devDependencies": {"autoprefixer": "^10", "postcss": "^8", "tailwindcss": "^3"}}