import { useState, useMemo } from "react";
import { usePermissions } from "@/utils/rolePermissionsContext";
import { Bars } from "react-loader-spinner";

import { useRouter } from "next/router";
import Cookies from "js-cookie";
const colorMap = {
  Compliance: {
    Complete: "#3EAB58",
    Incomplete: "#FF6C09",
    default: "#9A9A9A",
  },
  Procurement: {
    Complete: "#3EAB58",
    Incomplete: "#FF6C09",
    "Not Entered": "#FF6C09",
    default: "#9A9A9A",
  },
  Financials: {
    Complete: "#3EAB58",
    Incomplete: "#FF6C09",
    Verified: "#FF6C09",
    Rejected: "#B31312",
    default: "#9A9A9A",
    "N/A": "#9A9A9A",
  },
  General: {
    Complete: "#3EAB58",
    Incomplete: "#FF6C09",
    default: "#9A9A9A",
  },
  status: {
    Completed: "#fff",
    New: "#fff",
    Updated: "#fff",
    "In Progress": "#fff",
    Exported: "fff",
    default: "#fff",
    Cancelled: "#fff",
  },
};

const statusRenderer = (params) => {
  const router = useRouter();
  const { permissions } = usePermissions();
  const [loading, setLoading] = useState(false);

  const rolePermissions = {
    ...permissions,
  };

  const roles = params.data?.roleId;
  const supplierId = params.data?.id;
  const status = params.data?.status;
  const roleIds = roles?.map((role) => role?.role_id);

  const hasSupplierAccountRole = roleIds?.includes(1);

  const isColumnAllowed = roleIds?.some((roleId) => {
    const allowedSections = rolePermissions[roleId];
    return allowedSections?.includes(params.colDef.field);
  });

  const valueToRender =
    params.colDef.field === "status"
      ? params.value
      : hasSupplierAccountRole
      ? params.data[params.colDef.field]
        ? params.data[params.colDef.field]
        : "Not Entered"
      : isColumnAllowed
      ? params.data[params.colDef.field]
        ? params.data[params.colDef.field]
        : "Not Entered"
      : "N/A";
  const statusColumnStyles = {
    Completed: {
      backgroundColor: "#3EAB58",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
    },
    New: {
      backgroundColor: "#54C5ED",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
    },
    "In Progress": {
      backgroundColor: "#FF6C09",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
    },
    Updated: {
      backgroundColor: "#0066FF",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
    },
    Exported: {
      backgroundColor: "#FFAE00",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
    },
    Cancelled: {
      backgroundColor: "#ff2929",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
    },
  };

  const color = useMemo(
    () => colorMap[params.colDef.field][params.value] || "statusGray",
    [params.colDef.field, params.value]
  );

  const spanStyle = {
    width: "80px",
    textAlign: "center",
    display: "inline-block",
    verticalAlign: "middle",
    lineHeight: "24px",
    height: "32px",
    color: valueToRender == "Not Entered" ? "#B31312" : color,
    ...(params.colDef.field === "status" && statusColumnStyles[params.value]),
  };
  function routePage(pageToRoute) {
    if (pageToRoute == "status") {
      return;
    }
    if (status !== "Exported" && valueToRender !== "N/A" && status!=="Cancelled") {
      Cookies.set("prophets", params.data.prophets[0].prophet_id);
      setLoading(true);

      setTimeout(() => {
        if (pageToRoute == "General") {
          router.push({
            pathname: `/supplier/${supplierId}/edit/forms`,
            query: { section: 1 },
          });
        } else if (pageToRoute == "Financials") {
          router.push({
            pathname: `/supplier/${supplierId}/edit/forms`,
            query: { section: 2 },
          });
        } else if (pageToRoute == "Compliance") {
          router.push({
            pathname: `/supplier/${supplierId}/edit/forms`,
            query: { section: 3 },
          });
        } else if (pageToRoute == "Procurement") {
          router.push({
            pathname: `/supplier/${supplierId}/edit/forms`,
            query: { section: 4 },
          });
        }
      }, 1000);
    }
  }

  return (
    <>
      {loading ? (
        <div
          style={{
            width: "80px",
            textAlign: "center",
            display: "inline-block",
            verticalAlign: "middle",
            lineHeight: "24px",
            height: "32px",
            paddingLeft: "20px",
          }}
        >
          {" "}
          <Bars
            color="#002D73"
            height={30}
            width={30}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      ) : (
        //!make sure the status column is not clickable
        <span
          style={spanStyle}
          className={`${
            valueToRender !== "N/A" || valueToRender !== "Exported"
              ? "cursor-pointer"
              : ""
          }`}
          onClick={() => routePage(params.colDef.field)}
        >
          {valueToRender}
        </span>
      )}
    </>
  );
};

export default statusRenderer;
