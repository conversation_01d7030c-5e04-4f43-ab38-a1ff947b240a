import React, { useState, useEffect, useRef } from "react";
import {
  makeStyles,
  Popover,
  PopoverSurface,
  PopoverTrigger,
} from "@fluentui/react-components";

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    rowGap: "10px",
    paddingTop: "10px",
    paddingBottom: "10px",
    fontSize: "small",
    paddingLeft: "15px",
    paddingRight: "15px",
  },
  productList: {
    padding: "10px",
    cursor: "pointer",
    height: "200px",
    overflowX: "auto",
  },
});

const ProductDropDown = ({
  currentProducts,
  selectedProducts,
  setSelectedProducts,
  slFilters,
}) => {
  const styles = useStyles();
  const products = currentProducts;
  const [isProductDropDownOpen, setIsProductDropDownOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProductsString, setSelectedProductsString] = useState("");
  const [selectedCharacterRange, setSelectedCharacterRange] = useState("all");

  const popoverRef = useRef(null);

  const organizeProductsByCharRange = (products) => {
    return {
      "a-c": products.filter((product) => /^[a-c]/i.test(product.label)),
      "d-f": products.filter((product) => /^[d-f]/i.test(product.label)),
      "g-i": products.filter((product) => /^[g-i]/i.test(product.label)),
      "j-l": products.filter((product) => /^[j-l]/i.test(product.label)),
      "m-o": products.filter((product) => /^[m-o]/i.test(product.label)),
      "p-r": products.filter((product) => /^[p-r]/i.test(product.label)),
      "s-u": products.filter((product) => /^[s-u]/i.test(product.label)),
      "v-x": products.filter((product) => /^[v-x]/i.test(product.label)),
      "y-z": products.filter((product) => /^[y-z]/i.test(product.label)),
      all: products,
    };
  };

  const characterwiseProducts = organizeProductsByCharRange(products);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target)) {
        setIsProductDropDownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  const [productsToggleTriggered, setProductsToggleTrigger] = useState(false);
  const toggleProductSelection = (product) => {
    try {
      const isSelected = selectedProducts.some(
        (p) => p.value === product.value
      );
      const updatedProducts = isSelected
        ? selectedProducts.filter((p) => p.value !== product.value)
        : [...selectedProducts, product];
      setSelectedProducts(updatedProducts);
      setSelectedProductsString(updatedProducts.map((p) => p.label).join(", "));
      setSelectedProducts(updatedProducts);
      setProductsToggleTrigger(true);
    } catch (error) {
      console.error("Error in toggleProductSelection:", error);
    }
  };
 
  useEffect(() => {

    if (slFilters && !productsToggleTriggered) {
      try {
        const filter = slFilters.selectedProducts || [];

        let productsString = "";
        if (filter.length > 0) {
          productsString = filter
            .map((product) => product.productDescription || product.label)
            .slice(0, 2)
            .join(", ")
            .slice(0, 35);
          if (filter.length > 2) {
            productsString += "..."; // Add ellipsis if there are more than 2 products
          }
        }
        setSelectedProductsString(productsString);
        if (!productsToggleTriggered) {
          setSelectedProducts(filter);
        }

      } catch (error) {
        console.error("Error parsing slfilter cookie:", error);
      }
    } else {
      let productsString = "";

      if (selectedProducts.length > 0) {
        productsString = selectedProducts
          .map((product) => product.productDescription || product.label)
          .slice(0, 2)
          .join(", ")
          .slice(0, 35);
        if (selectedProducts.length > 2) {
          productsString += "..."; 
        }
      }
      setSelectedProductsString(productsString);
    }
  }, [selectedProducts, slFilters]);

  const productsByCharRange = characterwiseProducts[selectedCharacterRange].filter((product) =>
    product.label.toLowerCase().includes(searchTerm.toLowerCase())
  );
 
  const selectedFilteredProducts = productsByCharRange.filter((product) =>
    selectedProducts.some((p) => p.value === product.value)
  );
 
  const unselectedFilteredProducts = productsByCharRange.filter((product) =>
    !selectedProducts.some((p) => p.value === product.value)
  );
 
  const filteredProducts = [...selectedFilteredProducts, ...unselectedFilteredProducts];

  const searchChangeHandler = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    setSelectedCharacterRange("all");
  };

  const clearFilterHandlerSL = () => {
    setSelectedProducts([]);
    setSelectedProductsString("");
  };

  return (
    <div className="flex-1">
      <Popover
        open={isProductDropDownOpen}
        onOpenChange={(_, data) => setIsProductDropDownOpen(data.open)}
      >
        <PopoverTrigger disableButtonEnhancement>
          <div
            onClick={() => setIsProductDropDownOpen((prev) => !prev)}
            className="px-2 2xl:px-3 border border-[#cccccc] rounded-md w-full flex justify-between h-[36px] items-center"
            style={{ fontFamily: "poppinsregular" }}
          >
            <span
              className={
                selectedProductsString ? "text-black" : "placeholdertext"
              }
            >
              {selectedProductsString || "Select Products..."}
            </span>
            <span className="inline-flex items-center rounded-full bg-[#00E0D5] px-2 py-1 text-xs font-bold text-white m-3 px-4">
              {selectedProducts.length}
            </span>
          </div>
        </PopoverTrigger>
        <PopoverSurface
          ref={popoverRef} // Assign ref here to check for outside clicks
          tabIndex={-1}
          className="w-[45%] !p-0 !mt-2"
          style={{ fontFamily: "poppinsregular" }}
        >
          <div>
            <div className="px-4 pt-4">
              <input
                type="text"
                className="px-2 2xl:px-3 border rounded-md w-full text-sm"
                placeholder="Search..."
                value={searchTerm}
                onChange={searchChangeHandler}
              />
              <div className="flex flex-row justify-between ">
                <span className="text-xs text-gray-400 pl-1 py-1"></span>
                <span
                  className="text-xs py-1 cursor-pointer text-skin-primary"
                  style={{ fontFamily: "poppinsregular" }}
                  onClick={clearFilterHandlerSL}
                >
                  Clear Filters
                </span>
              </div>
            </div>

            <hr className="border-b border-gray-200 w-full" />

            <div className="flex flex-row gap-2 bg-gray-100 text-sm justify-between px-4 font-semibold">
              {Object.keys(characterwiseProducts).map((range) => (
                <span
                  key={range}
                  className={`${
                    selectedCharacterRange === range
                      ? "text-skin-primary border-b-2 border-skin-primary"
                      : ""
                  } cursor-pointer py-2 px-4`}
                  onClick={() => setSelectedCharacterRange(range)}
                >
                  {range === "all" ? "All" : range.toUpperCase()}
                </span>
              ))}
            </div>

            <div className={styles.productList}>
              {filteredProducts.map((product,index) => (
                <div
                  className="flex gap-4 items-center py-1"
                  key={index}
                  onClick={() => toggleProductSelection(product)}
                >
                  <input
                    type="checkbox"
                    checked={selectedProducts.some(
                      (p) => (p.value || p.altFillId) === product.value
                    )}
                    onChange={() => toggleProductSelection(product)}
                  />
                  <span>{product.label}</span>
                </div>
              ))}
            </div>

            <hr className="border-b border-gray-200 w-full" />

            <div className="px-4 py-3 flex flex-row justify-end">
              <button
                className="text-skin-primary px-4 py-1 rounded-md border border-skin-primary"
                onClick={() => setIsProductDropDownOpen(false)}
              >
                Close
              </button>
            </div>
          </div>
        </PopoverSurface>
      </Popover>
    </div>
  );
};

export default ProductDropDown;
