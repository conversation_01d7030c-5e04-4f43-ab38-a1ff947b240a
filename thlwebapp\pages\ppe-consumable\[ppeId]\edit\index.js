import Layout from "@/components/Layout";
import { apiConfig } from "@/services/apiConfig";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { ThreeCircles } from "react-loader-spinner";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import PpeConsumable from "@/components/PpeConsumable";
import { extractCompanyFromEmail } from "@/utils/extractCompanyFromEmail";
import { logout } from "@/utils/secureStorage";

const PpeConsumableEdit = ({ userData, initialData, dropdowns }) => {
  const router = useRouter();

  return (
    <Layout userData={userData}>
      <ToastContainer limit={1} />
      {!dropdowns || !initialData ? (
        <div style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: "calc(100vh - 100px)",
        }}>
          <ThreeCircles
            color="#002D73"
            height={50}
            width={50}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      ) : (
        <PpeConsumable
          dropdowns={dropdowns}
          userData={userData}
          pageType="edit"
          initialData={initialData}
        />
      )}
    </Layout>
  );
};

export default PpeConsumableEdit;

export const getServerSideProps = async (context) => {
  try {
    const { req, resolvedUrl, params } = context;
    const sessionId = req.cookies.thl_session;
    const ppeId = params.ppeId;

    if (!sessionId) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(resolvedUrl)}`,
          permanent: false,
        },
      };
    }

    const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8081";
    // Fetch user
    const userRes = await fetch(`${apiBase}/api/auth/me`, {
      method: "GET",
      headers: {
        Cookie: `thl_session=${sessionId}`,
        "Content-Type": "application/json",
      },
    });
    if (!userRes.ok) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(resolvedUrl)}`,
          permanent: false,
        },
      };
    }
    const { user } = await userRes.json();

    // Fetch dropdowns (reuse your add logic)
    const company = user?.company || extractCompanyFromEmail(user?.email);
    let prophetId = 1;
    if (company === "dpsltd") prophetId = 1;
    else if (company === "efcltd") prophetId = 3;
    else if (company === "fpp-ltd") prophetId = 4;
    else if (company === "iss" || company === "issproduce") prophetId = 5;

    const dropdownsRes = await fetch(
      `${apiBase}/ppe-consumables/get-ppe-dropdowns-list?prophetId=${prophetId}`,
      {
        method: "POST",
        headers: {
          Cookie: `thl_session=${sessionId}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify([
          "ppeType",
          "ppeCategory",
          "ppeSite",
          "ppeStatus",
        ]),
      }
    );
    const dropdowns = dropdownsRes.ok ? await dropdownsRes.json() : null;

    // Fetch the PPE record to edit
    const ppeRes = await fetch(
      `${apiBase}/ppe-consumables/${ppeId}`,
      {
        method: "GET",
        headers: {
          Cookie: `thl_session=${sessionId}`,
          "Content-Type": "application/json",
        },
      }
    );
    const initialData = ppeRes.ok ? await ppeRes.json() : null;

    return {
      props: {
        userData: user,
        dropdowns,
        initialData,
      },
    };
  } catch (error) {
    console.error("Error in getServerSideProps:", error);
    return {
      redirect: {
        destination: "/login",
        permanent: false,
      },
    };
  }
};