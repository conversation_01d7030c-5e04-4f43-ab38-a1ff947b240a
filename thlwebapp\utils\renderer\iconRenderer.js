import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {faEye,faTrash
} from "@fortawesome/free-solid-svg-icons";

const IconsRenderer = (props) => {
    const handleDelete = () => {

		const updatedData = [...yourData];
		const index = updatedData.indexOf(rowData);
		updatedData.splice(index, 1);
		setYourData(updatedData);
	  
		// Update the grid with the new data
		gridApi.current.setRowData(updatedData);
    };


	return (
		<div className="flex flex-row gap-3 justify-center text-blue-500">
			{/* <button ><FontAwesomeIcon icon={faPenToSquare} /></button> */}
			<button ><FontAwesomeIcon icon={faEye} /></button>
			<button onClick={handleDelete} className="text-red-500"><FontAwesomeIcon icon={faTrash} /></button>
		</div>
	)
};

export default IconsRenderer;
