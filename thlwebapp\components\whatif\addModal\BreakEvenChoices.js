import { apiConfig } from "@/services/apiConfig";
import { getCookieData } from "@/utils/getCookieData";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useRouter } from "next/router";
import { logout } from "@/utils/secureStorage";

export default function BreakEvenChoices({ beStatus = 1, setBeStatus, isRemoveConfirmationShown }) {
  const serverAddress = apiConfig.serverAddress;
  const router = useRouter();
  const [statuses, setStatuses] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetch(`${serverAddress}whatif/get-breakeven-statuses`, {
      method: "GET",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      credentials: "include",      
    })
      .then((res) => {
        if(res.status === 401){
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async() => {
            await logout();
            router.push("/login");
          }, 3000);
        }
        if (res.status === 200) {
          return res.json();
        }
        return Promise.reject(res);
      })
      .then((json) => {
        if (json) {
          setStatuses(json);
        }
        return false;
      })
      .catch((error) => {
        console.log("error", error);
      }).finally(()=>{
        setLoading(false)
      });
  }, []);

  if (loading) {
    return <></>;
  }

  return (
    <div className="flex flex-col">
      <label className="labels">Breakeven</label>
      <div className="flex justify-start">
        {statuses.map((status, i) => {
          return (
            <div key={status.id}>
              <input
                className="hidden"
                type="radio"
                id={`bestatus-${status.id}`}
                name={`bestatus-${status.id}`}
                readOnly
                checked={status.id === beStatus}
                value={status.id}
                onClick={(e) => {
                  if(isRemoveConfirmationShown)return;
                  setBeStatus(+e.target.value);
                }}
              />
              <label
                htmlFor={`bestatus-${status.id}`}
                className={`pointer-cursor  labelcheck border border-gray-300 px-8 py-2 flex items-center ${
                  i === 0 ? "rounded-tl-lg rounded-bl-lg" : ""
                } ${i === 3 ? "rounded-tr-lg rounded-br-lg" : ""}`}
              >
                {status.name}
              </label>
            </div>
          );
        })}
      </div>
    </div>
  );
}
