import { createContext, useContext, useState } from 'react';

const ServiceCustomersContext = createContext();

export const ServiceCustomerProvider = ({ children }) => {
  const [serviceCustomers, setServiceCustomers] = useState({
      value:"All",
      label:"All Service Customers"
    });

  const updateServiceCustomersList = (newServiceCustomersList) => {
    setServiceCustomers(newServiceCustomersList);
  };

  return (
    <ServiceCustomersContext.Provider value={{ serviceCustomers, updateServiceCustomersList }}>
      {children}
    </ServiceCustomersContext.Provider>
  );
};

export const useServiceCustomers = () => {
  return useContext(ServiceCustomersContext);
};
