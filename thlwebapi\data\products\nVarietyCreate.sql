-- DECLARE @company VARCHAR(50) = 'flrs'
-- DECLAR<PERSON> @varietyName VARCHAR(50) = 'Variety12'
-- DECLARE @code VARCHAR(50) = 'CVariety12'
-- DECLARE @description VARCHAR(50) = 'testing22'
-- DECLAR<PERSON> @masterProductCode INT = 12
-- DECLAR<PERSON> @actionId INT = 2
-- <PERSON><PERSON>AR<PERSON> @actionedByEmail VARCHAR(100) = '<EMAIL>'
-- <PERSON><PERSON>AR<PERSON> @actionedByName VARCHAR(100) = 'test test'
-- DECLARE @comment VARCHAR(100) = ''
BEGIN TRANSACTION;

BEGIN TRY DECLARE @last_request_number INT = 0;

SELECT
	@last_request_number = COALESCE(request_no, 0)
FROM
	product_request_no
WHERE
	 product_type = 3;

DECLAR<PERSON> @padded_request_number NVARCHAR(6);

SET
	@padded_request_number = RIGHT(
		'000000' + CAST(@last_request_number + 1 AS VARCHAR),
		6
	);

		INSERT INTO product_request_no (
			[company],
			[request_no],
			[product_type]
		)
		OUTPUT INSERTED.request_no, INSERTED.company, INSERTED.id 
		VALUES (
			@company,
			@padded_request_number ,
			3
		);

DECLARE @generatedRequestNo INT = SCOPE_IDENTITY();

INSERT INTO
	product_nvariety (
		request_no,
		[type],
		code,
		[description],
		master_product_code,
		prophet_id
	)
VALUES
	(
		@generatedRequestNo,
		3,
		@code,
		@description,
		@masterProductCode,
		@prophetId
	);

DECLARE @varietyId INT = SCOPE_IDENTITY();

DECLARE @departmentId INT = 0;
DECLARE @roleId INT = 0;

SELECT @departmentId = department_id FROM users WHERE email = @actionedByEmail

IF (@departmentId != 5)
	BEGIN
		INSERT INTO
			product_nvariety_status (
				variety_id,
				action_id,
				actioned_by_email,
				actioned_by_name,
				comment
			) 
		VALUES
			(
				@varietyId,
				@actionId,
				@actionedByEmail,
				@actionedByName,
				@comment
			);
	END
ELSE IF(@departmentId=5 AND @actionID=1)
	BEGIN
		INSERT INTO
			product_nvariety_status (
				variety_id,
				action_id,
				actioned_by_email,
				actioned_by_name,
				comment
			) 
		VALUES
			(
				@varietyId,
				@actionId,
				@actionedByEmail,
				@actionedByName,
				@comment
			);
	END

IF (@actionId=2 AND @departmentId = 5)
	BEGIN

		INSERT INTO
			product_nvariety_status (
				variety_id,
				action_id,
				actioned_by_email,
				actioned_by_name,
				comment,
				is_latest
			) 
		VALUES
			(
				@varietyId,
				@actionId,
				@actionedByEmail,
				@actionedByName,
				@comment,
				0
			);

		INSERT INTO
		product_nvariety_status (
			variety_id,
			action_id,
			actioned_by_email,
			actioned_by_name,
			comment
		) 
	VALUES
		(
			@varietyId,
			3,
			@actionedByEmail,
			@actionedByName,
			'Approved by default as the application is created by a technical person'
		);
	END

COMMIT TRANSACTION;

PRINT 'Transaction committed.';

END TRY BEGIN CATCH ROLLBACK TRANSACTION;

PRINT 'Transaction rolled back.';

PRINT ERROR_MESSAGE();

END CATCH;