import { useState, useEffect, Fragment } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faInfo,
    faXmark
} from "@fortawesome/free-solid-svg-icons";

const noConnectionAlertBox = ({ isOpen, closeModal }) => {
  
    <Transition appear={isOpen} show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeModal}>
            <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
            >
                <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
            </Transition.Child>

            <div className="fixed inset-0 overflow-y-auto">
                <div className="flex items-center justify-center min-h-full p-4 text-center">
                    <Transition.Child
                        as={Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0 scale-95"
                        enterTo="opacity-100 scale-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100 scale-100"
                        leaveTo="opacity-0 scale-95"
                    >
                        <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">

                            <div className="relative bg-white rounded-lg shadow">

                                <div className="flex items-start justify-between p-8 rounded-t">
                                    <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                                        <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                                            <FontAwesomeIcon
                                                icon={faInfo}
                                            />{" "}
                                        </span>{" "}
                                        Alert
                                    </h3>
                                    <button
                                        onClick={closeModal}
                                        type="button"
                                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                                        data-modal-hide="default-modal"
                                    >
                                        <FontAwesomeIcon
                                            icon={faXmark}
                                            className="text-skin-primary"
                                        />{" "}
                                    </button>
                                </div>

                                <div className="p-8 py-0 space-y-6">
                                    <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                                        Internet connection lost. please try again.
                                    </p>
                                </div>

                                <div className="flex items-end p-6 space-x-2 justify-center">
                                    <button
                                        onClick={closeModal}
                                        data-modal-hide="default-modal"
                                        type="button"
                                        className="text-skin-a11y bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                                    >
                                        OK
                                    </button>
                                </div>
                            </div>
                        </Dialog.Panel>
                    </Transition.Child>
                </div>
            </div>
        </Dialog>
    </Transition>
}

export default noConnectionAlertBox;