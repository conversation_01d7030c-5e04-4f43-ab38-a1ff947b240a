import React, { useCallback, useRef } from "react";
import {
  makeSty<PERSON>,
  Button,
  Popover,
  PopoverSurface,
  PopoverTrigger,
} from "@fluentui/react-components";

const useStyles = makeStyles({
  contentHeader: {
    marginTop: "0",
  },
  popoverSurface: {
    height: "auto",
    width: "450px",
    padding: "0",
    borderRadius: "10px",
  },
});

const PromoPopover = ({ positioningRef, popoverData, currency }) => {
  const styles = useStyles();
  let promoWeekData;
  let productWeekData;
  let promoData;

  if (popoverData?.length > 0) {
    promoData = popoverData[0];
    productWeekData = popoverData[1];
    let weekNo = productWeekData.week;
    promoWeekData = promoData.weekData[weekNo]?.current;
  }

  return (
    <div>
      <Popover positioning={{ positioningRef }}>
        <PopoverTrigger disableButtonEnhancement>
          <Button className="w-5 h-5 !p-0 !border-0 !bg-transparent !min-w-0">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 512 512"
              className="w-4 h-4"
              fill="#000"
            >
              <path d="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416zm0 464A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM216 336c-13.3 0-24 10.7-24 24s10.7 24 24 24h80c13.3 0 24-10.7 24-24s-10.7-24-24-24h-8V248c0-13.3-10.7-24-24-24H216c-13.3 0-24 10.7-24 24s10.7 24 24 24h24v64H216zm40-144a32 32 0 1 0 0-64 32 32 0 1 0 0 64z" />
            </svg>
          </Button>
        </PopoverTrigger>

        <PopoverSurface className={styles.popoverSurface} tabIndex={-1}>
          <div className="w-full">
            <div className="rounded-sm p-3 bg-theme-blue text-white rounded-tl-xl rounded-tr-xl">
              <div className="flex flex-row justify-between items-center">
                {promoData?.created_by &&
                  ` Promotion created by : ${promoData?.created_by
                    ?.split(" ")
                    ?.map(
                      (word) => word?.charAt(0)?.toUpperCase() + word?.slice(1)
                    )
                    ?.join(" ")}`}

                <span></span>
              </div>
            </div>
            <div className="p-3">
              <h3 className="text-Base text-gray-400 font-semibold">
                Description
              </h3>
              <hr className="border border-gray-200"></hr>
              <div className="py-2">{promoData?.description}</div>
              <h3 className="text-Base text-gray-400 pt-3 font-semibold">
                Overview
              </h3>
              <hr className="border border-gray-200"></hr>
              <div className="flex flex-row border-b border-gray-300 justify-between p-2">
                <div className="flex flex-col border-r border-gray-300 w-1/2 ">
                  <div className="text-gray-400 ">Volume</div>
                  <div className="text-skin-primary font-bold text-2xl">
                    {promoWeekData?.volume}/
                    <span className="text-base text-gray-800">
                      {productWeekData?.data[0].value}
                    </span>
                  </div>
                </div>
                <div className="flex flex-col w-1/2 pl-4">
                  <div className="text-gray-400 ">BE</div>
                  <div className="text-skin-primary font-bold text-2xl">
                    {" "}
                    {promoWeekData?.be < 0
                      ? `-${currency}${Math.abs(promoWeekData?.be)?.toFixed(2)}`
                      : `${currency}${promoWeekData?.be?.toFixed(2)}`}
                    /
                    <span className="text-base text-gray-800">
                      {productWeekData?.data[2].value < 0
                        ? `-${currency}${Math.abs(
                            productWeekData?.data[2].value
                          )?.toFixed(2)}`
                        : `${currency}${productWeekData?.data[2].value?.toFixed(
                            2
                          )}`}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex flex-row border-b border-gray-300 justify-between p-2">
                <div className="flex flex-col border-r border-gray-300 w-1/2">
                  <div className="text-gray-400 ">Price</div>
                  <div className="text-skin-primary font-bold text-2xl">
                    {" "}
                    {promoWeekData?.price < 0
                      ? `-${currency}${Math.abs(promoWeekData?.price)?.toFixed(
                          2
                        )}`
                      : `${currency}${promoWeekData?.price?.toFixed(2)}`}
                    /
                    <span className="text-base text-gray-800">
                      {productWeekData?.data[3].value < 0
                        ? `-${currency}${Math.abs(
                            productWeekData?.data[3].value
                          )?.toFixed(2)}`
                        : `${currency}${productWeekData?.data[3].value?.toFixed(
                            2
                          )}`}
                    </span>
                  </div>
                </div>
                <div className="flex flex-col w-1/2 pl-4">
                  <div className="text-gray-400 ">GP</div>
                  <div className="text-skin-primary font-bold text-2xl">
                    {promoWeekData?.gp < 0
                      ? `-${currency}${Math.abs(promoWeekData?.gp)?.toFixed(2)}`
                      : `${currency}${promoWeekData?.gp?.toFixed(2)}`}
                    /
                    <span className="text-base text-gray-800">
                      {productWeekData?.data[5].value < 0
                        ? `-${currency}${Math.abs(
                            productWeekData?.data[5].value
                          )?.toFixed(2)}`
                        : `${currency}${productWeekData?.data[5].value?.toFixed(
                            2
                          )}`}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex flex-row justify-between p-2">
                <div className="flex flex-col border-r border-gray-300 w-1/2">
                  <div className="text-gray-400 ">Sales</div>
                  <div className="text-skin-primary font-bold text-2xl">
                    {promoWeekData?.sales < 0
                      ? `-${currency}${Math.abs(promoWeekData?.sales)?.toFixed(
                          2
                        )}`
                      : `${currency}${promoWeekData?.sales?.toFixed(2)}`}
                    /
                    <span className="text-base text-gray-800">
                      {productWeekData?.data[7].value < 0
                        ? `-${currency}${Math.abs(
                            productWeekData?.data[7]?.value
                          )?.toFixed(2)}`
                        : `${currency}${productWeekData?.data[7]?.value?.toFixed(
                            2
                          )}`}
                    </span>
                  </div>
                </div>
                <div className="flex flex-col w-1/2 pl-4">
                  <div className="text-gray-400 ">GP%</div>
                  <div className="text-skin-primary font-bold text-2xl">
                  {promoWeekData?.gp_percent?.toFixed(2)}%/
                    <span className="text-base text-gray-800">
                      {productWeekData?.data[6].value?.toFixed(2)}%
                    </span>
                  </div>
                </div>
              </div>
              {/* <div className="flex flex-row justify-end p-2 pb-0">
                <button className="bg-gray-200 rounded-lg px-8 py-2 font-semibold" >
                    Close
                </button>
              </div> */}
            </div>
          </div>
        </PopoverSurface>
      </Popover>
    </div>
  );
};

export default PromoPopover;
