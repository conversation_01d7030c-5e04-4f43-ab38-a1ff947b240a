-- 1. DECLARE VARIABLES to hold the IDs we need to use and generate
DECLARE @NewRequestID INT;
DECLARE @NewRequestItemID INT;
DECLARE @SubmitterUserID INT = 1; -- REPLACE: The ID of the user creating the request
DECLARE @TargetSiteID INT = 1;    -- REPLACE: The ID of the site the request is for
DECLAR<PERSON> @ProductID INT = 1;       -- REPLACE: The ID of the product being requested
DECLARE @InitialStatusActionID INT = 1; -- REPLACE: The status_action ID for 'Submitted'

-- 2. BEGIN TRANSACTION to ensure all inserts succeed or fail together
BEGIN TRANSACTION;

BEGIN TRY
    -- 3. INSERT INTO order_requests
    -- Let the IDENTITY column auto-generate the primary key [id]
    -- For this schema, we can set [request_id] to be the same as the auto-generated [id] later
    INSERT INTO [dbo].[order_requests] (
        [request_id], -- We will update this in a moment
        [user_id],
        [site_id],
        [created_at],
        [required_date],
        [is_active]
    )
    VALUES (
        NULL, -- Insert NULL initially, update it after we know the ID
        @SubmitterUserID,
        @TargetSiteID,
        GETDATE(), -- Current date/time for creation
        DATEADD(day, 14, GETDATE()), -- Example: required date 2 weeks from now
        1 -- Set as active
    );

    -- 4. CAPTURE the primary key of the new request
    SET @NewRequestID = SCOPE_IDENTITY();

    -- 5. UPDATE the order_requests table to set request_id to match the id
    -- This is a common pattern when you want a human-friendly ID that matches the PK
    UPDATE [dbo].[order_requests]
    SET [request_id] = @NewRequestID
    WHERE [id] = @NewRequestID;

    -- 6. INSERT INTO order_request_items for the first product
    INSERT INTO [dbo].[order_request_items] (
        [request_id],
        [product_id],
        [size],
        [quantity],
        [name_for_printing],
        [comments],
        [is_active]
    )
    VALUES (
        @NewRequestID, -- Use the FK from the request we just made
        @ProductID,
        'Large',       -- REPLACE with appropriate size
        25,            -- REPLACE with desired quantity
        NULL,          -- Can be filled in or left NULL
        'Initial request for new project', -- REPLACE with comments
        1 -- Set as active
    );

    -- 7. CAPTURE the primary key of the new item
    SET @NewRequestItemID = SCOPE_IDENTITY();

    -- 8. INSERT INTO status for the new item
    -- This establishes the initial status (e.g., 'Submitted')
    INSERT INTO [dbo].[status] (
        [request_item_id],
        [action_id],
        [actioned_by],
        [actioned_by_email],
        [actioned_at],
        [comment],
        [is_active],
        [is_latest],
        [test] -- Consider omitting or providing a value if needed
    )
    VALUES (
        @NewRequestItemID, -- FK to the item we just inserted
        @InitialStatusActionID, -- FK to status_action (e.g., 1 for Submitted)
        @SubmitterUserID,  -- FK to the user who is actioning this (same as submitter)
        (SELECT email FROM users WHERE id = @SubmitterUserID), -- Get email from users table
        GETDATE(), -- Current date/time for the action
        'Request submitted by user.', -- Status comment
        1, -- Set as active
        1, -- This is the latest status for this item
        NULL -- Assuming 'test' column can be NULL, else provide a value
    );

    -- 9. If everything worked, COMMIT the transaction
    COMMIT TRANSACTION;

    -- 10. OUTPUT the results
    PRINT 'Transaction committed successfully.';
    PRINT 'New Request ID: ' + CAST(@NewRequestID AS VARCHAR);
    PRINT 'New Request Item ID: ' + CAST(@NewRequestItemID AS VARCHAR);
    SELECT @NewRequestID AS NewRequestID, @NewRequestItemID AS NewRequestItemID;

END TRY
BEGIN CATCH
    -- 11. If anything went wrong, ROLLBACK the entire transaction
    ROLLBACK TRANSACTION;
    PRINT 'Transaction rolled back due to error.';

    -- 12. Display the error details
    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();
    DECLARE @ErrorLine INT = ERROR_LINE();
    DECLARE @ErrorProc NVARCHAR(126) = ERROR_PROCEDURE();

    PRINT 'Error Procedure: ' + ISNULL(@ErrorProc, 'Not within a procedure');
    PRINT 'Error Line: ' + CAST(@ErrorLine AS VARCHAR(10));
    PRINT 'Error Message: ' + @ErrorMessage;
    RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH;