-- DECLARE @variety_id INT = 6

	;WITH LatestActionDraft AS (
		SELECT TOP 1
			id AS latest_id,
			variety_id,
			action_id AS action_id,
			actioned_by_name AS actioned_by_name,
			actioned_by_email AS actioned_by_email,
			actioned_at AS actioned_at,
			Comment AS comment
		FROM 
			product_nvariety_status
		WHERE 
			action_id = 1 AND variety_id = @variety_id AND is_active = 1
		ORDER BY 
			id DESC
	),
	LatestActionSubmit AS (
		SELECT TOP 1
			id AS latest_id,
			variety_id,
			action_id AS action_id,
			actioned_by_name AS actioned_by_name,
			actioned_by_email AS actioned_by_email,
			actioned_at AS actioned_at,
			Comment AS comment
		FROM 
			product_nvariety_status
		WHERE 
			action_id = 2 AND variety_id = @variety_id AND is_active = 1
		ORDER BY 
			id DESC
	),
	LatestActionReview AS (
		SELECT TOP 1
			id AS latest_id,
			variety_id,
			action_id AS action_id,
			actioned_by_name AS actioned_by_name,
			actioned_by_email AS actioned_by_email,
			actioned_at AS actioned_at,
			Comment AS comment
		FROM 
			product_nvariety_status
		WHERE 
			(action_id = 3 OR action_id = 4) AND variety_id = @variety_id AND is_active = 1
		ORDER BY 
			id DESC
	),
	wasRejected AS (
    SELECT CASE 
        WHEN EXISTS (
            SELECT 1 
            FROM product_nvariety_status 
            WHERE action_id = 4 
            AND variety_id = @variety_id 
            AND is_active = 1
        ) THEN 1 
        ELSE 0 
    END AS is_rejected
	),
	LatestActionIss AS (
		SELECT TOP 1
			id AS latest_id,
			variety_id,
			action_id AS action_id,
			actioned_by_name AS actioned_by_name,
			actioned_by_email AS actioned_by_email,
			actioned_at AS actioned_at,
			Comment AS comment
		FROM 
			product_nvariety_status
		WHERE 
			action_id = 5 AND variety_id = @variety_id AND is_active = 1
		ORDER BY 
			id DESC
	)
	SELECT 
		 PNV.id
		 ,CONCAT('NV', PRN.request_no) AS request_no
		 ,wasRejected.is_rejected AS was_rejected
		,PNV.[description] AS description
		,PNV.[code] AS variety_code
		,MP.id AS master_product_id
		,MP.name AS master_product_name
		,MP.code AS master_product_code
		,PNV.[description] as product_description
		,S.[label] AS [status]
		,COALESCE(LASubmit.actioned_by_name, LADraft.actioned_by_name) AS originator_name
		,COALESCE(LASubmit.actioned_by_email, LADraft.actioned_by_email) AS originator_email
		,COALESCE(LASubmit.comment, LADraft.comment) AS originator_comment
		,LAReview.actioned_by_name AS reviewer_name
		,LAReview.actioned_by_email AS reviewer_email
		,LAReview.comment AS reviewer_comment
		,LAIss.actioned_by_name AS completed_by_name
		,LAIss.actioned_by_email AS completed_by_email
		,LAIss.comment AS completed_by_comment
		,PRN.company AS company_name
		,PRN.id AS request_id
		FROM product_nvariety PNV
		JOIN product_request_type PRT ON PRT.id = PNV.type
		JOIN master_products MP ON MP.code = PNV.master_product_code AND MP.prophet_id = PNV.prophet_id
		JOIN product_request_no PRN ON PRN.id = PNV.request_no
		LEFT JOIN LatestActionDraft LADraft ON LADraft.variety_id = PNV.id
		LEFT JOIN LatestActionSubmit LASubmit ON LASubmit.variety_id = PNV.id
		LEFT JOIN LatestActionReview LAReview ON LAReview.variety_id = PNV.id
		LEFT JOIN LatestActionIss LAIss ON LAIss.variety_id = PNV.id
		JOIN product_nvariety_status PNVS ON PNVS.variety_id = PNV.id AND PNVS.is_latest = 1
		JOIN product_nvariety_actions PNVA ON PNVA.id = PNVS.action_id
		JOIN [status] S ON S.id = PNVA.status_id
		CROSS JOIN wasRejected
		WHERE PNV.is_active = 1
		AND MP.is_active = 1
		AND PNVA.is_active = 1
		AND PNV.id = @variety_id