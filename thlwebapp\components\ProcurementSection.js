import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
} from "react";
import { useRouter } from "next/router";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faFloppyDisk,
  faPenToSquare,
  faTrash,
  faSearch,
} from "@fortawesome/free-solid-svg-icons";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles//ag-grid.css";
import "ag-grid-community/styles//ag-theme-alpine.css";
import { apiConfig } from "@/services/apiConfig";
import { ThreeCircles } from "react-loader-spinner";
import Cookies from "js-cookie";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useMsal } from "@azure/msal-react";
import productNumberRenderer from "@/utils/renderer/productNumberRenderer";
import { parse } from "date-fns";
import { useLoading } from "@/utils/loaders/loadingContext";
import { logout } from "@/utils/secureStorage";

const ProcurementSection = ({
  data,
  uniqueAllowedSections,
  onPrev,
  isEdit,
  dropdowns,
}) => {
  const gridRef = useRef(null);
  const productGridRef = useRef(null);
  const router = useRouter();
  const { supplierId } = router.query;
  const [dtay_data_json, setDtay_data_json] = useState([]);
  const [proudct_data_json, setProduct_data_json] = useState([]);
  const [product_number, setProductNumber] = useState();
  const [description, setDescription] = useState("");
  const [brand, setBrand] = useState("");
  const [end_customer, setEndCustomer] = useState("");
  const [agreed_terms, setAgreedTerms] = useState("");
  const [agreed_terms_name, setAgreedTermsName] = useState("");
  const [pricing, setPricing] = useState("");
  const [yields, setYields] = useState("");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [isValidEndDate, setIsValidEndDate] = useState(true);
  const [isValidStartDate, setIsValidStartDate] = useState(true);
  const [startDateErrorMsg, setStartDateErrorMsg] = useState("");
  const [endDateErrorMsg, setEndDateErrorMsg] = useState("");
  const [loading, setLoading] = useState(false);
  const [isCommonError, setCommonError] = useState("");
  const { instance, accounts } = useMsal();
  const [products, setProducts] = useState([]);
  const [agreedTermsValid, setAgreedTermValidate] = useState("");
  const [pricingValid, setPricingValidate] = useState("");
  const [yieldValid, setYieldValidate] = useState("");
  const [dateValid, setDateValidate] = useState("");
  const [pageSize, setPageSize] = useState(15);
  const [isRadioChecked, setRadioChecked] = useState(false);
  const [selectedProductData, setSelectedProductData] = useState(null);
  const [rowDeleted, setRowDeleted] = useState(false);
  const [formChange, setFormChange] = useState(false);
  const [status, setStatus] = useState("");
  const { setIsLoading } = useLoading();

  const IconsRenderer = (props) => {
    let updatedData;

    const handleDelete = (event) => {
      event.preventDefault();
      setRowDeleted(true);
      const selectedRow = props.data;

      updatedData = [...dtay_data_json];

      const index = updatedData?.indexOf(selectedRow);
      updatedData.splice(index, 1);

      props.api.applyTransaction({ remove: updatedData });

      setDtay_data_json(updatedData);
    };

    const handleEdit = (event) => {
      const selectedRow = props.data;
      updatedData = [...dtay_data_json];
      const index = updatedData?.indexOf(selectedRow);
      updatedData?.splice(index, 1);
      props.api.applyTransaction({ remove: updatedData });
      setDtay_data_json(updatedData);
      setProduct_data_json(updatedData);
      setProductNumber(selectedRow?.product_number);
      setDescription(selectedRow?.description);
      setYields(selectedRow?.yields);
      setEndCustomer(selectedRow?.end_customer);
      setAgreedTerms(selectedRow?.agreed_terms);
      setPricing(selectedRow?.pricing);
      setBrand(selectedRow?.brand);
      const [startString, endString] = selectedRow?.date?.split(" to ");
      const start = new Date(startString);
      const end = new Date(endString);

      setStartDate(start);
      setEndDate(end);
    };

    return (
      <div className="flex flex-row gap-4 justify-center text-skin-primary">
        {/* <button onClick={handleEdit}>
          <FontAwesomeIcon icon={faPenToSquare} />
        </button> */}
        <button onClick={handleDelete} className="text-red-500">
          <FontAwesomeIcon icon={faTrash} />
        </button>
      </div>
    );
  };
  const gridOptions = {
    responsive: true,
  };

  const productListColumnDefs = [
    {
      headerName: "Product Number",
      field: "product_number",
      flex: "1%",
      //headerClass: "header-with-border",
      checkboxSelection: true,
      maxWidth: 120,
    },
    {
      headerName: "Description",
      field: "description",
      flex: "1%",
      //headerClass: "header-with-border",
      maxWidth: 400,
    },
    {
      headerName: "Brand",
      field: "brand",
      flex: "1%",
      //headerClass: "header-with-border",
    },
    {
      headerName: "End Customer",
      field: "end_customer",
      flex: "1%",
      //headerClass: "header-with-border",
    },
  ];

  function onSelectionChanged() {
    const selectedNodes = productGridRef.current.api.getSelectedNodes();
    const selectedRows = selectedNodes?.map((node) => node?.data);
    if (selectedRows?.length > 0) {
      const saveProductNumber = [
        {
          product_number: selectedRows[0]?.product_number,
          description: selectedRows[0]?.description,
          brand: selectedRows[0]?.brand,
          end_customer: selectedRows[0]?.end_customer,
        },
      ];
      setProduct_data_json(saveProductNumber);
    } else {
      setProduct_data_json([]);
    }
  }

  const defaultProductColDef = useMemo(() => ({
    sortable: true,
    filter: false,
    resizable: true,
    flex: 1,
    suppressMenu: false,
  }));
  const CustomCellRenderer = (params) => {
    const truncatedText =
      params?.value && params?.value?.length > 12
        ? params?.value?.substring(0, 12) + "..."
        : params?.value;

    return <span title={params?.value}>{truncatedText}</span>;
  };

  const CustomTooltipComponent = ({ value }) => (
    <div title={value}>{value}</div>
  );

  const columnDefs = [
    {
      headerName: "Product Number",
      field: "product_number",
      headerClass: "header-with-border",
      cellRenderer: CustomCellRenderer,
      tooltipComponent: CustomTooltipComponent,
      flex: "2%",
      //maxWidth: 150
    },
    {
      headerName: "Description",
      field: "description",
      headerClass: "header-with-border",
      cellRenderer: CustomCellRenderer,
      tooltipComponent: CustomTooltipComponent,
      flex: "2%",
    },
    {
      headerName: "Brand",
      field: "brand",
      headerClass: "header-with-border",
      cellRenderer: CustomCellRenderer,
      tooltipComponent: CustomTooltipComponent,
      flex: "2%",
    },
    {
      headerName: "End Customer",
      field: "end_customer",
      headerClass: "header-with-border",
      cellRenderer: CustomCellRenderer,
      tooltipComponent: CustomTooltipComponent,
      flex: "2%",
    },
    {
      headerName: "Agreed Terms",
      field: "agreed_terms_name",
      headerClass: "header-with-border",
      cellRenderer: CustomCellRenderer,
      tooltipComponent: CustomTooltipComponent,
      flex: "2%",
    },
    {
      headerName: "Pricing",
      field: "pricing",
      headerClass: "header-with-border",
      cellRenderer: CustomCellRenderer,
      tooltipComponent: CustomTooltipComponent,
      flex: "2%",
    },
    {
      headerName: "Yields",
      field: "yields",
      headerClass: "header-with-border",
      cellRenderer: CustomCellRenderer,
      tooltipComponent: CustomTooltipComponent,
      flex: "2%",
    },
    {
      headerName: "Start Date",
      field: "startDate",
      headerClass: "header-with-border",
      cellRenderer: CustomCellRenderer,
      tooltipComponent: CustomTooltipComponent,
      flex: "2%",
      //maxWidth: 400
    },
    {
      headerName: "End Date",
      field: "endDate",
      headerClass: "header-with-border",
      cellRenderer: CustomCellRenderer,
      tooltipComponent: CustomTooltipComponent,
      flex: "1%",
      //maxWidth: 400
    },
    {
      headerName: "",
      cellRenderer: IconsRenderer,
      headerClass: "header-with-border",
      flex: "1%",
      cellStyle: { justifyContent: "end", paddingRight: "10px" },
    },
  ];

  if (typeof window !== "undefined") {
    localStorage.removeItem("curr");
  }

  const saveData = (e) => {
    setFormChange(true);
    e.preventDefault();
    let count = 0;
    if (!agreed_terms_name) {
      count++;
      setAgreedTermValidate("Select agreed terms");
    } else {
      setAgreedTermValidate("");
    }
    if (!pricing) {
      count++;

      setPricingValidate("Enter product price");
    } else if (!pricing.match(/^(\d*\.{0,1}\d{0,2}$)/)) {
      setPricingValidate("Enter valid price");
    } else {
      setPricingValidate("");
    }
    if (!yields) {
      count++;

      setYieldValidate("Enter product yields");
    } else {
      setYieldValidate("");
    }
    if (!startDate) {
      count++;
      setIsValidStartDate(false);
      setStartDateErrorMsg("Please enter a valid start date");
    } else {
      setIsValidStartDate(true);
      setStartDateErrorMsg("");
    }

    if (!endDate) {
      count++;
      setIsValidEndDate(false);
      setEndDateErrorMsg("Please enter a valid end date");
    } else if (startDate && endDate < startDate) {
      count++;
      setIsValidEndDate(false);
      setEndDateErrorMsg("End date should be after the start date");
    } else {
      setIsValidEndDate(true);
      setEndDateErrorMsg("");
    }
    if (count > 0) {
      return;
    }

    const startDateString = startDate
      ? startDate?.toISOString()?.split("T")[0]
      : null;
    const endDateString = endDate
      ? endDate?.toISOString()?.split("T")[0]
      : null;
    if (
      agreed_terms_name &&
      pricing &&
      yields &&
      startDate &&
      endDate &&
      proudct_data_json.length > 0
    ) {
      const newItem = {
        product_number: parseInt(proudct_data_json[0]?.product_number, 10),
        description: proudct_data_json[0]?.description,
        brand: proudct_data_json[0]?.brand,
        end_customer: proudct_data_json[0]?.end_customer,
        agreed_terms_name,
        agreed_terms,
        pricing,
        yields,
        startDate: startDateString,
        endDate: endDateString,
      };

      if (Object.values(newItem)?.some((value) => value === "")) {
        return;
      }
      setDtay_data_json([...dtay_data_json, newItem]);
      setRadioChecked(false);
      productGridRef.current.api.deselectAll();
    } else {
      toast.error("Select a product", {
        position: "top-right",
      });
      return;
    }

    setProductNumber("");
    setDescription("");
    setBrand("");
    setEndCustomer("");
    setAgreedTerms("");
    setPricing("");
    setYields("");
    setStartDate(null);
    setEndDate(null);
  };
  // const handleAgreedTermsChange = (e) => {
  //   setAgreedTerms(e.target.value);
  //   const selectedAgreedTerms = e.target.options[e.target.selectedIndex].text;
  //   setAgreedTermsName(selectedAgreedTerms);
  //   if (agreedTermsValid) {
  //     setAgreedTermValidate("");
  //   }
  // };

  const handleAgreedTermsChange = (e) => {
    setFormChange(true);
    e.preventDefault(); // This will prevent the default form submission behavior
    setAgreedTerms(e.target.value);
    const selectedAgreedTerms = e.target.options[e.target.selectedIndex].text;
    setAgreedTermsName(selectedAgreedTerms);
    if (agreedTermsValid) {
      setAgreedTermValidate("");
    }
  };

  // const handlePricingChange = (e) => {
  //   setPricing(e.target.value);
  //   console.log(pricingValid);
  //   if (pricingValid && pricingValid.match(/^(\d*\.{0,1}\d{0,2}$)/)) {
  //     setPricingValidate("");
  //   } else if(pricingValid && pricingValid?.length > 10){
  //     setPricingValidate("Pricing must be 10 digits or less.");
  //   } else if(pricingValid.match(/^(\d*\.{0,1}\d{0,2}$)/)){
  //     setPricingValidate("");
  //   } else {
  //     setPricingValidate("Enter valid pricing.");
  //   }
  // };

  const handlePricingChange = (e) => {
    setFormChange(true);
    const newPricing = e.target.value.trim();
    setPricing(newPricing);
    if (newPricing.match(/^(\d*\.{0,1}\d{0,2})$/)) {
      setPricingValidate("");
    } else if (newPricing.length > 10) {
      setPricingValidate("Pricing must be 10 digits or less.");
    } else {
      setPricingValidate("Enter valid pricing.");
    }
  };

  const handleYiedlsChange = (e) => {
    setFormChange(true);
    const trimmedValue = e.target.value.trim();
    setYields(trimmedValue);
    if (yieldValid) {
      setYieldValidate("");
    } else if (pricingValid?.length > 10) {
      setYieldValidate("Yield must be 50 characters or less.");
    }
  };

  useEffect(() => {
    const parsed = JSON.parse(data[0]?.dtay_data_json ?? "[]");
    const prophet_code = data[0]?.prophets_id_code;
    setStatus(data[0]?.status ?? "");

    if (prophet_code) {
      const parseProphetCode = JSON.parse(prophet_code ?? "[]");
      const prophet_id_code = parseProphetCode[0]?.prophet_id;
      try {
        let serverAddress = apiConfig.serverAddress;
        fetch(
          `${serverAddress}suppliers/get-product-by-prophet/${prophet_id_code}`,
          {
            method: "GET",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
            },
            credentials: "include",
          }
        )
          .then((res) => {
            if (res.status === 400) {
              toast.error(
                "There was an error with your request. Please check your data and try again."
              );
            } else if (res.status === 401) {
              toast.error("Your session has expired. Please log in again.");
              setTimeout(async () => {
                await logout();
                router.push("/login");
              }, 3000);
            }
            if (res.status === 200) {
              return res.json();
            }
            throw new Error("Failed to fetch data");
          })
          .then((data) => {
            const formattedProductData = data?.map((row) => ({
              id: row?.id,
              product_number: row?.product_number,
              description: row?.description,
              brand: row?.brand ? row?.brand : "-",
              end_customer: row?.end_customer,
              agreed_terms: row?.agreed_terms,
              agreed_terms_name: row?.agreed_terms_name,
              pricing: row?.pricing,
              yields: row?.yields,
              startDate: row?.start_date,
              endDate: row?.end_date,
            }));
            setProducts(formattedProductData);
          })
          .catch((error) => {
            toast.error("Error fetching product data:", error.message, {
              position: "top-right",
            });
          });
      } catch (error) {
        toast.error("Error fetching product data:", error.message, {
          position: "top-right",
        });
      }
    }

    if (parsed) {
      const formattedData = parsed?.map((row) => ({
        id: row?.id,
        product_number: row?.product_number,
        description: row?.description,
        brand: row?.brand ? row?.brand : "-",
        end_customer: row?.end_customer,
        agreed_terms: row?.agreed_terms,
        agreed_terms_name: row?.agreed_terms_name,
        pricing: row?.pricing,
        yields: row?.yields,
        startDate: row?.start_date,
        endDate: row?.end_date,
      }));
      setDtay_data_json(formattedData);
    }
    setIsLoading(false)

  }, [supplierId]);

  function validate() {
    if (dtay_data_json?.length === 0) {
      setStatus("Not Entered");
      return "Not Entered";
    } else {
      setStatus("Complete");
      return "Complete";
    }
  }

  function handleSubmit(step) {
    const ValidStatus = validate();
    let serverAddress = apiConfig.serverAddress;
    if (ValidStatus) {
      setLoading(true);
      if (rowDeleted || formChange) {
        let currentStatus;
        if (status == 3 || status == 4 || status == 1) {
          currentStatus = 4;
        } else if (status == 2) {
          currentStatus = 2;
        } else {
          currentStatus = 3;
        }
        localStorage.setItem("isFormNew", false);
        fetch(`${serverAddress}suppliers/update-supplier/${supplierId}`, {
          method: "PUT",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({
            dtay_data_json: dtay_data_json,
            sectionName: "procurementSection",
            updated_date: new Date().toISOString(),
            procurement: ValidStatus,
            status: currentStatus,
          }),
        })
          .then((res) => {
            if (res.status === 400) {
              toast.error(
                "There was an error with your request. Please check your data and try again."
              );
            } else if (res.status === 401) {
              toast.error("Your session has expired. Please log in again.");
              setTimeout(async () => {
                await logout();
                router.push("/login");
              }, 3000);
            }
            if (res.status === 200) {
              return res.json();
            }
            return Promise.reject(res);
          })
          .then((json) => {
            if (step == "sap") {
              if (isEdit) {
                localStorage.removeItem("isEdit");
                router.back();
              } else {
                router.push({ pathname: `/supplier/${supplierId}/confirm` });
              }
            }
          })
          .catch((err) => {
            setLoading(false);
            toast.error(
              "Error saving data in procurement forms file:",
              err.message,
              {
                position: "top-right",
              }
            );
            return err;
          });
      } else {
        if (step == "sap") {
          if (isEdit) {
            localStorage.removeItem("isEdit");
            router.back();
          } else {
            router.push({ pathname: `/supplier/${supplierId}/confirm` });
          }
        }else {
          router.push({ pathname: "/suppliers" });
        }
      }
    }
  }

  // const handleStartDateChange = (event) => {
  //   try {
  //     const newDate = new Date(event.target.value);

  //     if (!isNaN(newDate.getTime())) {
  //       setStartDate(newDate);
  //       setIsValidStartDate(true);
  //     } else {
  //       setIsValidStartDate(false);
  //       console.error("Invalid date entered. Please enter a valid date.");
  //     }
  //   } catch (error) {
  //     console.error("Error parsing date:", error);
  //   }
  // };

  // const handleEndDateChange = (event) => {
  //   try {
  //     const newDate = new Date(event.target.value);

  //     if (!isNaN(newDate.getTime())) {
  //       setEndDate(newDate);
  //       setIsValidEndDate(true);
  //     } else {
  //       setIsValidEndDate(false);
  //       console.error("Invalid date entered. Please enter a valid date.");
  //     }
  //   } catch (error) {
  //     console.error("Error parsing date:", error);
  //   }
  // };

  const handleStartDateChange = (event) => {
    try {
      const newDate = new Date(event.target.value);

      if (!isNaN(newDate.getTime())) {
        setStartDate(newDate);
        setIsValidStartDate(true);

        // Ensure end date is not before new start date
        if (endDate && endDate < newDate) {
          setEndDate(newDate); // Set end date to new start date
          setIsValidEndDate(true); // Update validity flag
        }
      } else {
        setIsValidStartDate(false);
        setStartDateErrorMsg("PLease enter valid start date");
        console.error("Invalid date entered. Please enter a valid date.");
      }
    } catch (error) {
      console.error("Error parsing date:", error);
    }
  };

  // const handleEndDateChange = (event) => {
  //   try {
  //     const newDate = new Date(event.target.value);

  //     if (!isNaN(newDate.getTime())) {
  //       console.log("inside if")
  //       setEndDate(newDate);
  //       setIsValidEndDate(true);

  //       // Ensure end date is not before start date
  //       if (startDate && newDate < startDate) {
  //         setEndDate(startDate); // Set end date to start date
  //         setIsValidEndDate(false); // Update validity flag (optional)
  //         setEndDateErrorMsg("End date cannot be before start date");
  //       }
  //     } else {
  //       console.log("inside if")
  //       setIsValidEndDate(false);
  //       setEndDateErrorMsg("Please enter valid end date");
  //       console.error("Invalid date entered. Please enter a valid date.");
  //     }
  //   } catch (error) {
  //     console.error("Error parsing date:", error);
  //   }
  // };

  const handleEndDateChange = (event) => {
    try {
      const newDate = new Date(event.target.value);

      if (!isNaN(newDate.getTime())) {
        if (startDate && newDate < startDate) {
          setEndDate(newDate);
          setIsValidEndDate(false);
          setEndDateErrorMsg("End date cannot be before the start date.");
        } else {
          setEndDate(newDate);
          setIsValidEndDate(true);
          setEndDateErrorMsg("");
        }
      } else {
        setIsValidEndDate(false);
        setEndDateErrorMsg(
          "Invalid date entered. Please enter a valid date in the format YYYY-MM-DD."
        );
      }
    } catch (error) {
      console.error("Error parsing date:", error);
      setEndDateErrorMsg("An error occurred while parsing the date.");
    }
  };

  const onFilterTextBoxChanged = useCallback(() => {
    productGridRef.current.api.setQuickFilter(
      document.getElementById("filter-text-box").value
    );
  }, []);

  const handleGridReady = (params) => {
    params.api.setColumnDefs(columnDefs);
  };

  const handlePageSizeChange = (event) => {
    const newPageSize = parseInt(event.target.value, 15);
    setPageSize(newPageSize);
    gridRef.current.api.paginationSetPageSize(newPageSize);
  };

  const formatDate = (dateString) => {
    const dateObject = new Date(dateString);

    const year = dateObject?.getFullYear();
    const month = String(dateObject?.getMonth() + 1)?.padStart(2, "0"); // Months are zero-based
    const day = String(dateObject?.getDate())?.padStart(2, "0");

    return `${year}-${month}-${day}`;
  };

  const formattedDate = formatDate(startDate);

  if (isCommonError) {
    toast.error("Connection lost or Invalid Token", {
      position: "top-right",
    });
    setTimeout(async function () {
      await logout();
      router.push("/login");
    }, 3000);
  }

  return (
    <>
      <ToastContainer limit={1} />
      {loading ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "100vh",
          }}
        >
          <ThreeCircles
            color="#002D73"
            height={50}
            width={50}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      ) : (
        <div className="relative panel-container bg-white rounded-lg w-[95%] 2xl:w-[calc(100%-70px)] p-4 pb-0">
          <div className="m-3 mb-0">
            <div className="mb-6">
              <div className="mb-3">
                <h4 className="formtitle pb-1 border-b border-light-gray">
                  Default Terms & Agreed Yield
                </h4>
              </div>
              <div className="flex w-full justify-end">
                <label className="relative block w-[47vh] text-gray-400 mt-0 pt-0 mb-2">
                  <span className="absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black">
                    <FontAwesomeIcon icon={faSearch} className="fw-bold" />
                  </span>
                  <input
                    type="text"
                    id="filter-text-box"
                    placeholder="Search"
                    onInput={onFilterTextBoxChanged}
                    className="block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none"
                  />
                </label>
              </div>
              <div
                className="product_link_def ag-theme-alpine"
                style={{ height: 250, width: "100%" }}
              >
                <AgGridReact
                  rowData={products}
                  ref={productGridRef}
                  suppressRowClickSelection
                  defaultColDef={defaultProductColDef}
                  columnDefs={productListColumnDefs}
                  pagination={true}
                  paginationPageSize={pageSize}
                  onPageSizeChanged={handlePageSizeChange}
                  onGridReady={handleGridReady}
                  gridOptions={gridOptions}
                  rowSelection="single"
                  onSelectionChanged={onSelectionChanged}

                  //rowHeight={33}
                ></AgGridReact>
              </div>
              <div className="flex flex-row my-4 w-full">
                <div className="flex flex-row my-4 w-full gap-4">
                  {/* <div className="flex flex-col">
                    <label className="labels mb-2">Product number</label>
                    <input
                      type="text"
                      name="product_number"
                      value={product_number}
                      onChange={(e) => setProductNumber(e.target.value)}
                      className="w- border border-light-gray px-2 2xl:px-3 rounded-md inputs"
                    />
                  </div>
                  <div className="flex flex-col">
                    <label className="labels mb-2">Description</label>
                    <input
                      type="text"
                      name="product_description"
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      className="w-full px-2 2xl:px-3 border border-light-gray rounded-md inputs"
                    />
                  </div>

                  <div className="flex flex-col">
                    <label className="labels mb-2">Brand</label>
                    <select
                      type="text"
                      name="brand"
                      value={brand}
                      onChange={(e) => setBrand(e.target.value)}
                      className="w-full px-2 border border-light-gray rounded-md inputs"
                    >
                      <option value="" disabled></option>
                      {dropdowns.brands &&
                        dropdowns.brands.map((brand, key) => {
                          return (
                            <option key={key} value={brand.id}>{brand.brand}</option>
                          );
                        })}
                    </select>
                  </div>

                  <div className="flex flex-col">
                    <label className="labels mb-2">End customer</label>
                    <select
                      type="text"
                      name="end_customer"
                      value={end_customer}
                      onChange={(e) => setEndCustomer(e.target.value)}
                      className="w-full px-2 2xl:px-3 border border-light-gray rounded-md inputs"
                    >
                      <option value="" disabled></option>
                      {dropdowns.end_customers &&
                        dropdowns.end_customers.map((end_customer, key) => {
                          return (
                            <option key={key} value={end_customer.id}>{end_customer.name}</option>
                          );
                        })}
                    </select>
                  </div> */}

                  <div className="flex flex-col w-2/5">
                    <label className="labels mb-2">Agreed Terms</label>
                    <select
                      type="text"
                      name="agreed_terms"
                      value={agreed_terms}
                      onChange={handleAgreedTermsChange}
                      className="w-full px-2 2xl:px-3 border border-light-gray rounded-md inputs"
                    >
                      <option value="" disabled>Select...</option>
                      {dropdowns.agreed_terms &&
                        dropdowns.agreed_terms.map((term, key) => {
                          return (
                            <option key={key} value={term.id}>
                              {term.terms}
                            </option>
                          );
                        })}
                    </select>
                    {agreedTermsValid && (
                      <span className="text-red-500">{agreedTermsValid}</span>
                    )}
                  </div>

                  <div className="flex flex-col w-2/5">
                    <label className="labels mb-2">Pricing</label>
                    <input
                      type="text"
                      name="pricing"
                      value={pricing}
                      onChange={handlePricingChange}
                      maxLength={10}
                      className="w-full px-2 2xl:px-3 border border-light-gray rounded-md inputs"
                    />
                    {pricingValid && (
                      <span className="text-red-500">{pricingValid}</span>
                    )}
                  </div>

                  <div className="flex flex-col w-2/5">
                    <label className="labels mb-2">Yields</label>
                    <input
                      type="text"
                      name="yields"
                      value={yields}
                      onChange={handleYiedlsChange}
                      maxLength={20}
                      className="w-full px-2 2xl:px-3 border border-light-gray rounded-md inputs"
                    />
                    {yieldValid && (
                      <span className="text-red-500">{yieldValid}</span>
                    )}
                  </div>
                  <div className="flex flex-col w-full">
                    <div className="flex flex-row gap-4">
                      <div className="flex flex-col w-[45%]">
                        <label className="labels mb-2">Start Date</label>
                        <input
                          type="date"
                          name="startDate"
                          value={
                            startDate
                              ? startDate.toISOString().split("T")[0]
                              : ""
                          }
                          onChange={handleStartDateChange}
                          min={new Date().toISOString().split("T")[0]}
                          className="w-full px-2 2xl:px-3 border border-light-gray rounded-md inputs"
                        />
                        {!isValidStartDate && (
                          <span className="text-red-600">
                            {startDateErrorMsg}
                          </span>
                        )}
                      </div>

                      <div className="flex flex-col w-[45%]">
                        <label className="labels mb-2">End Date</label>
                        <input
                          type="date"
                          name="endDate"
                          value={
                            endDate ? endDate.toISOString().split("T")[0] : ""
                          }
                          onChange={handleEndDateChange}
                          min={formattedDate}
                          className="w-full px-2 2xl:px-3 border border-light-gray rounded-md inputs"
                        />
                        {!isValidEndDate && (
                          <span className="text-red-600">
                            {endDateErrorMsg}
                          </span>
                        )}
                      </div>
                      <div
                        className={`flex justify-end ${
                          isValidEndDate && isValidStartDate
                            ? "items-end"
                            : "items-center mb-1"
                        }`}
                        onClick={saveData}
                      >
                        <span className="mb-[2px] px-2 py-1 2xl:px-3.5 border border-skin-primary rounded-md text-skin-primary cursor-pointer">
                          <FontAwesomeIcon icon={faFloppyDisk} />
                        </span>
                      </div>
                    </div>
                    <div>
                      {dateValid && (
                        <span className="text-red-500">{dateValid}</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div
                className="product_data_def"
                style={{ height: 150, width: "100%" }}
              >
                <AgGridReact
                  ref={gridRef}
                  columnDefs={columnDefs}
                  rowData={dtay_data_json}
                  rowHeight={33}
                ></AgGridReact>
              </div>
            </div>
            <div className="flex justify-between border-t border-gray-300 py-5 bg-white">
              <button
                className="border border-skin-primary text-skin-primary rounded-md px-8"
                onClick={onPrev}
              >
                Previous
              </button>
              <div>
                <button
                  className="border border-skin-primary text-skin-primary me-10 py-1 px-8 font-medium rounded-md"
                  onClick={() => handleSubmit("sae")}
                >
                  Save & Exit
                </button>
                <button
                  onClick={() => handleSubmit("sap")}
                  className="border border-skin-primary bg-skin-primary text-white rounded-md py-1 px-8 font-medium"
                >
                  Save & Proceed
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ProcurementSection;
