"use strict";

const utils = require("../utils");
const config = require("../../config");
const sql = require("mssql");

const getLogs = async () => {
    try {
        let pool = await sql.connect(config.sql);
        const sqlQueries = await utils.loadSqlQueries("logs");
        const list = await pool.request().query(sqlQueries.getLogs);
        return list.recordset;
    } catch (error) {
        return error.message;
    }
};
const getModules = async () => {
    try {
        let pool = await sql.connect(config.sql);
        const sqlQueries = await utils.loadSqlQueries("logs");
        const list = await pool.request().query(sqlQueries.getModules);
        return list.recordset;
    } catch (error) {
        return error.message;
    }
};

module.exports = {
    getLogs,getModules
}