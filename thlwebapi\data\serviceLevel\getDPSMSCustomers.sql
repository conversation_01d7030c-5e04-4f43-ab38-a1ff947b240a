select
    distinct cust_customer.hocustcode as [value],
    cust_customer.hocustcode as [label]
from
    [FLR_DEV_TEST_dps_BI_lookup].[dbo].sales_dm_stage_dim_sales sales
    join [FLR_DEV_TEST_dps_BI_lookup].[dbo].sales_dm_stage_customer cust_customer on cust_customer.custcode = sales.custcode
    join [FLR_DEV_TEST_dps_BI_lookup].[dbo].sales_dm_stage_customer delcust_customer on sales.delcustcode = delcust_customer.custcode
WHERE
    case
        when cust_customer.custcode <>'DPS' then cust_customer.hocustcode
        else delcust_customer.hocustcode
    end NOT IN ('PPACK', 'REJISS', 'SMOVE')
    AND altfilid > 0
    AND delivery_date >= @start_date
    AND delivery_date <= @end_date
    AND cust_customer.category_no = 1
    AND deptcode = @deptCode
order by
    cust_customer.hocustcode