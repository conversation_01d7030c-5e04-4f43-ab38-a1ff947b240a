import React, { useRef } from "react";

const TableGrossProfitTd = ({
  quarterData,
  previousSunday,
  currentWeek,
  calendarWeeks,
  row,
  currency,
  fiscalyear,currentFiscalYear
}) => {
  const positioningRef = useRef(null);

  return (
    <>
      {calendarWeeks.map((weekStartDate, weekIndex) => {
        const productData = quarterData.find(
          (data) => data.startWeek === weekStartDate
        );
        const isToday = weekStartDate === previousSunday && row.fiscalyear==currentFiscalYear;
        const promotions = row?.promotions?.filter((data) => {
          return (
            productData?.week >= data?.promo_start_week_no &&
            productData?.week <= data?.promo_end_week_no
          );
        });
        const valueToDisplay = (() => {
          if (promotions && promotions.length > 0) {
            for (const promo of promotions) {
              const promoWeekData = promo.weekData[productData?.week]?.current;
              if (
                promoWeekData?.gp != null &&
                productData?.week >= currentWeek
              ) {
                return { data: promoWeekData.gp, isPromo: true };
              }
            }
          }
          return {
            data:
              fiscalyear != productData.fiscalYear &&
              productData.NoDataNextFY_Q1 == 1
                ? ""
                : productData?.data[5]?.value ?? 0,
            isPromo: false,
          };
        })();
        return (
          <td
          title={fiscalyear != productData.fiscalYear &&
            productData.NoDataNextFY_Q1 == 1
            ?"Data does not exist for this product":""}
            ref={positioningRef}
            key={weekIndex}
            className={`${
              isToday ? "bg-currentWeek" : fiscalyear != productData.fiscalYear &&
            productData.NoDataNextFY_Q1 == 1
            ? "bg-gray" : productData.hasData==0?"no-productsbg"
            : "bg-white"
            } !text-center hover:cursor-pointer relative`}
          >
            {productData.hasData==0?"":valueToDisplay &&
                valueToDisplay.data !== undefined &&
                valueToDisplay.data !== "" &&
                !isNaN(valueToDisplay.data) &&
                (valueToDisplay.data < 0
                  ? `-${currency}${Math.abs(
                      Number(valueToDisplay.data)
                    ).toFixed(2)}`
                  : `${currency}${Number(valueToDisplay.data).toFixed(2)}`)}
          </td>
        );
      })}
    </>
  );
};

export default TableGrossProfitTd;
