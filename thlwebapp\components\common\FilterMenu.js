import { apiConfig } from "@/services/apiConfig";
import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Menu,
  MenuTrigger,
  MenuList,
  MenuItemCheckbox,
  MenuGroup,
  MenuGroupHeader,
  MenuPopover,
  MenuItem,
} from "@fluentui/react-components";
import { toast } from "react-toastify";
// import exportExcel from "@/utils/exportExcel";

export default function FilterMenu({
  setCheckedStates,
  reloadHandler,
  menuItems,
  checkedValues,
  setCheckedValues,
  setSelectedReasons,
  setSelectedSubReasons,
  setSelectedMasterProductCode,
  setSelectedProducts,
}) {  
  const onChange = (e, { name, checkedItems }) => {
    setCheckedValues((s) => {
      return s ? { ...s, [name]: checkedItems } : { [name]: checkedItems };
    });
  };

  useEffect(() => {
    setCheckedStates((prev) => {
      const newStates = { ...prev };
      Object.keys(checkedValues).forEach((name) => {
        if (newStates[name]) {
          Object.keys(newStates[name]).forEach((item) => {
            newStates[name][item] = checkedValues[name].includes(item);
          });
        }
      });
      return newStates;
    });
  }, [checkedValues, setCheckedStates]);

  // //#region change below to execute a function in the back end 
  // //#endregion 

  // const exportFilteredData = async () => {
  //   // console.log("customerList[0].value",customerList[0].value)
  //   try {
  //     const dropdownsRequest = await fetch(
  //       `${apiConfig.serverAddress}whatif/export-what-if-customer-data/${customerList[0].value}`,
  //       {
  //         method: "POST",
  //         headers: {
  //           Authorization: `Bearer ${userData.token}`,
  //           Accept: "application/json",
  //           "Content-Type": "application/json",
  //         },
  //         body: JSON.stringify({
  //           // whatif_id: modalConfig.whatIfs[0].whatif_id,
  //           // productName: modalConfig.productName,
  //           // fiscalYear: modalConfig.fiscalYear,
  //         }), //TODO change this to export data in backend
  //       }
  //     );
  //   } catch (error) {
  //     console.log("error exporting excel", error);
  //   }
  // };
  

  return (
    <div className="flex justify-end">
      <Menu>
        <MenuTrigger disableButtonEnhancement>
          <Button className="!border-0 !min-w-fit">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 448 512"
              className="w-5 h-5"
              fill="#000000"
            >
              <path d="M0 80c0-8.8 7.2-16 16-16H432c8.8 0 16 7.2 16 16s-7.2 16-16 16H16C7.2 96 0 88.8 0 80zM0 240c0-8.8 7.2-16 16-16H432c8.8 0 16 7.2 16 16s-7.2 16-16 16H16c-8.8 0-16-7.2-16-16zM448 400c0 8.8-7.2 16-16 16H16c-8.8 0-16-7.2-16-16s7.2-16 16-16H432c8.8 0 16 7.2 16 16z" />
            </svg>
          </Button>
        </MenuTrigger>
        <MenuPopover
          className="!max-w-[600px]"
          style={{ fontFamily: "poppinsregular" }}
        >
          <MenuList
            className=""
            checkedValues={checkedValues}
            onCheckedValueChange={onChange}
            style={{ fontFamily: "poppinsregular" }}
          >
            <div className="flex flex-row gap-4 px-5 py-3">
              {menuItems.map((group, index) => (
                <MenuGroup key={index}>
                  <MenuGroupHeader className="border-b border-gray-200 h-[23px] !text-gray-300 mb-3 !font-normal">
                    {group.header}
                  </MenuGroupHeader>
                  {group.items.map((item) => (
                    <MenuItemCheckbox
                      key={item.value}
                      name={item.name}
                      value={item.value}
                    >
                      {item.label}
                    </MenuItemCheckbox>
                  ))}
                </MenuGroup>
              ))}
              <MenuGroup>
                <MenuGroupHeader className="border-b border-gray-200 h-[23px] !text-gray-300 mb-3 !font-normal">
                  ACTIONS
                </MenuGroupHeader>
                {/* <MenuItem
                  icon={
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 640 512"
                      className="w-5 h-5"
                      fill="#444"
                    >
                      <path d="M389.8 125.2C363.7 88.1 320.7 64 272 64c-77.4 0-140.5 61-143.9 137.5c-.6 13-9 24.4-21.3 28.8C63.2 245.7 32 287.2 32 336c0 61.9 50.1 112 112 112l368 0c53 0 96-43 96-96c0-36.8-20.7-68.8-51.2-84.9c-13.4-7.1-20-22.5-15.8-37.1c2-6.9 3-14.3 3-22c0-44.2-35.8-80-80-80c-12.3 0-23.9 2.8-34.3 7.7c-14.1 6.7-30.9 2.3-39.9-10.5zM272 32c59.5 0 112.1 29.5 144 74.8C430.5 99.9 446.8 96 464 96c61.9 0 112 50.1 112 112c0 10.7-1.5 21-4.3 30.8C612.3 260.2 640 302.9 640 352c0 70.7-57.3 128-128 128l-368 0C64.5 480 0 415.5 0 336c0-62.8 40.2-116.1 96.2-135.9C100.3 106.6 177.4 32 272 32zM228.7 299.3c-6.2-6.2-6.2-16.4 0-22.6s16.4-6.2 22.6 0L304 329.4 304 176c0-8.8 7.2-16 16-16s16 7.2 16 16l0 153.4 52.7-52.7c6.2-6.2 16.4-6.2 22.6 0s6.2 16.4 0 22.6l-80 80c-6.2 6.2-16.4 6.2-22.6 0l-80-80z" />
                    </svg>
                  }
                >
                  Extract Data
                </MenuItem> */}
                <MenuItem
                  onClick={() => {
                    // Cookies.remove("filters");
                    // Cookies.remove("slFilters")
                    setSelectedReasons([]);
                    setSelectedSubReasons([]);
                    setSelectedProducts([]);
                    setSelectedMasterProductCode({
                      value: "all",
                      label: "All Master Product Codes",
                    });
                    toast.success(`Filters have been reset successfully!`, {
                      theme: "colored",
                      autoClose: 3000,
                    });
                  }}
                  icon={
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 640 512"
                      className="w-5 h-5"
                      fill="#444"
                    >
                      <path d="M507.3 27.3c6.2-6.2 6.2-16.4 0-22.6s-16.4-6.2-22.6 0l-144 144-18.6-18.6c-20.2-20.2-51.4-24.6-76.3-10.7L16.4 246.9C6.3 252.5 0 263.2 0 274.8c0 8.5 3.4 16.6 9.3 22.6L214.7 502.7c6 6 14.1 9.3 22.6 9.3c11.6 0 22.3-6.3 27.9-16.4L392.6 266.2c13.9-25 9.5-56.1-10.7-76.3l-18.6-18.6 144-144zM299.5 152.8l59.7 59.7c10.1 10.1 12.3 25.7 5.3 38.2l-21.2 38.1L223.2 168.6l38.1-21.2c12.5-6.9 28.1-4.8 38.2 5.3zm-105.4 32L327.2 317.9l-90 162.1L113.9 356.7l41.4-41.4c6.2-6.2 6.2-16.4 0-22.6s-16.4-6.2-22.6 0L91.3 334.1 32.1 274.8l162.1-90z" />
                    </svg>
                  }
                >
                  Reset Filter
                </MenuItem>
                <MenuItem
                  onClick={reloadHandler}
                  icon={
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 640 512"
                      className="w-5 h-5"
                      fill="#444"
                    >
                      <path d="M442.6 210.6C422.2 126.5 346.4 64 256 64c-71.1 0-133.1 38.6-166.3 96l54.3 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-80.7 0L48 192c-8.8 0-16-7.2-16-16l0-96c0-8.8 7.2-16 16-16s16 7.2 16 16l0 60.6C103.2 75.5 174.5 32 256 32c106.4 0 195.5 74.2 218.3 173.6c2.2 9.6-5.4 18.4-15.3 18.4c-7.9 0-14.6-5.7-16.5-13.4zM69.4 301.4C89.8 385.5 165.6 448 256 448c71.1 0 133.1-38.6 166.3-96l-54.5 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l80.9 0 15.1 0c8.8 0 16 7.2 16 16l0 96c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-60.2C408.6 436.6 337.3 480 256 480C149.6 480 60.5 405.8 37.7 306.4C35.5 296.8 43.1 288 53 288c7.9 0 14.6 5.7 16.5 13.4z" />
                    </svg>
                  }
                >
                  Reload Data
                </MenuItem>
              </MenuGroup>
            </div>
          </MenuList>
        </MenuPopover>
      </Menu>
    </div>
  );
}
