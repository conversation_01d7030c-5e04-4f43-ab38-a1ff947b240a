select
    distinct delcust_customer.hocustcode as [value],
    delcust_customer.hocustcode as [label]
from
    FLR_DEV_TEST_dps_BI_lookup.[dbo].vw_dps_service_lvl_tc_sales_additional sales
    --join FLR_DEV_TEST_dps_BI_lookup.[dbo].sales_dm_stage_customer cust_customer on cust_customer.custcode = sales.custcode
    join FLR_DEV_TEST_dps_BI_lookup.[dbo].sales_dm_stage_customer delcust_customer on sales.delcustcode = delcust_customer.custcode
 left join FLR_DEV_TEST_dps_BI_lookup.[dbo].sales_dm_stage_product_lvl_grouping SPLG ON SPLG.prodnum = sales.product_number
WHERE
   delcust_customer.hocustcode NOT IN ('PPACK', 'REJISS', 'SMOVE')
    AND altfilid > 0
 AND Prov_Order_Flag = 0
    AND delivery_date >= @start_date 
    AND delivery_date <= @end_date
    AND delcust_customer.category_no = 1
    AND deptcode = 1
--  AND (
--    @product = 'All Products'
--    OR mascode_desc = @product
--   )
 
order by [label]