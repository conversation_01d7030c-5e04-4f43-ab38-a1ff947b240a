-- DECLARE @packaging_request_id INT =46

	;WITH LatestActionDraft AS (
		SELECT TOP 1
			id AS latest_id,packaging_request_id,
			action_id AS action_id,
			actioned_by_name AS actioned_by_name,
			actioned_by_email AS actioned_by_email,
			actioned_at AS actioned_at,
			Comment AS comment
		FROM 
			product_packaging_status
		WHERE 
			action_id = 1 AND packaging_request_id =@packaging_request_id AND is_active = 1
		ORDER BY 
			id DESC
	),
	LatestActionSubmit AS (
		SELECT TOP 1
			id AS latest_id,
			packaging_request_id,
			action_id AS action_id,
			actioned_by_name AS actioned_by_name,
			actioned_by_email AS actioned_by_email,
			actioned_at AS actioned_at,
			Comment AS comment
		FROM 
			product_packaging_status
		WHERE 
			action_id = 2 AND packaging_request_id =@packaging_request_id AND is_active = 1
		ORDER BY 
			id DESC
	),
	LatestActionIss AS (
		SELECT TOP 1
			id AS latest_id,
			packaging_request_id,
			action_id AS action_id,
			actioned_by_name AS actioned_by_name,
			actioned_by_email AS actioned_by_email,
			actioned_at AS actioned_at,
			Comment AS comment
		FROM 
			product_packaging_status
		WHERE 
			action_id =3 AND packaging_request_id = @packaging_request_id AND is_active = 1
		ORDER BY 
			id DESC
	)
	SELECT 
	PPR.id,
	PPR.change_launch_date,
	PPR.colour_of_material,
	PMC.name as colour_of_material_name,
	PPR.[component_weight(g)] as component_weight,
	PPR.[dimension_size(mm)] as dimension_size,
	PPR.end_customer,
	EC.name as end_customer_name,
	PPR.existing_packaging_code,
	PSCexist.sub_product_code as existing_packaging_code_name,
	PPR.packaging_name,
	PPR.paper_from_sustainable_forestry,
	PPsus.name as paper_from_sustainable_forestry_name,
	PPR.reason,
	PPRea.reason as reason_name,
	PPR.recyclable_content,
	PPR.recyclable_to_oprl,
	PPRTO.type as recyclable_to_oprl_name,
	PPR.supplier,
	PPR.trading_business,
	PPTB.name as trading_business_name,
	PPR.type_of_material,
	PMTy.name as type_of_material_name,
	PPTy.type as type_of_packaging_name,
	PPR.sub_product_code,
	PPR.sub_product_code_label,
	PSC.sub_product_code as sub_product_code_name,
	PPR.type_of_packaging
		 ,CONCAT('PK', PRN.request_no) AS request_no
		,MP.id AS master_product_id
		,MP.name AS master_product_name
		,MP.code AS master_product_code
		,S.[label] AS [status]
		,S.[id] AS [status_id]
		,COALESCE(LASubmit.actioned_by_name, LADraft.actioned_by_name) AS originator_name
		,COALESCE(LASubmit.actioned_by_email, LADraft.actioned_by_email) AS originator_email
		,COALESCE(LASubmit.comment, LADraft.comment) AS originator_comment
		,LAIss.actioned_by_name AS completed_by_name
		,LAIss.actioned_by_email AS completed_by_email
		,LAIss.comment AS completed_by_comment
		,PRN.company AS company_name
		,PRN.id AS request_id
		FROM product_packaging_request PPR
		JOIN product_request_type PRT ON PRT.id = 4
		LEFT JOIN master_products MP ON MP.id = PPR.master_product_code_id AND MP.prophet_id =5 AND MP.is_active = 1
		LEFT JOIN product_request_no PRN ON PRN.id = PPR.request_no
		LEFT JOIN product_packaging_reason PPRea ON PPRea.id = PPR.reason
		LEFT JOIN product_packaging_type PPTy on PPTy.id = PPR.type_of_packaging
		LEFT JOIN product_packaging_material_colors PMC on PMC.id = PPR.colour_of_material
		LEFT JOIN product_packaging_material_types PMTy on PMTy.id = PPR.type_of_material
		LEFT JOIN product_packaging_trading_business PPTB on PPTB.id = PPR.trading_business
		LEFT JOIN product_packaging_sustainability PPsus on PPsus.id = PPR.paper_from_sustainable_forestry
		LEFT JOIN product_packaging_recyclable_to_oprl PPRTO on PPRTO.id = PPR.recyclable_to_oprl
		LEFT JOIN end_customers EC on EC.id = PPR.end_customer 
		LEFT JOIN products_subproduct_code PSC on PSC.id = PPR.sub_product_code
		LEFT JOIN products_subproduct_code PSCexist on PSCexist.id = PPR.existing_packaging_code
		LEFT JOIN LatestActionDraft LADraft ON LADraft.packaging_request_id = PPR.id
		LEFT JOIN LatestActionSubmit LASubmit ON LASubmit.packaging_request_id = PPR.id
		LEFT JOIN LatestActionIss LAIss ON LAIss.packaging_request_id = PPR.id
		JOIN product_packaging_status PPS ON PPS.packaging_request_id = PPR.id AND PPS.is_latest = 1
		LEFT JOIN product_packaging_actions PPA ON PPA.id = PPS.action_id AND PPA.is_active = 1
		LEFT JOIN [status] S ON S.id = PPA.status_id
		WHERE
		 PPR.id =@packaging_request_id