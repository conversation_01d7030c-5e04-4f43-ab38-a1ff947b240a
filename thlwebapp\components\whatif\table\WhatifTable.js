import React, { memo, useContext, useEffect, useRef, useState } from "react";
import { CurrencyContext } from "../providers/CurrencyProvider";
import TableColumnTh from "./TableColumnTh";
import TableBreakevenSection from "./beSection/TableBreakevenSection";
import TablePriceSection from "./priceSection/TablePriceSection";
import TableVolumeTd from "./volumeSection/TableVolumeTd";
import TableValueSection from "./valueSection/TableValueSection";
import TableGrossProfitSection from "./grossProfitSection/TableGrossProfitSection";
import TableGrossProfitPercentageSection from "./grossProfitPercentageSection/TableGrossProfitPercentageSection";
import { Tooltip } from "@fluentui/react-components";
import { formatDisplay } from "@/utils/whatif/utils/getFormattedDate";

const WhatifTable = ({
  checkedStates,
  columnRefs,
  columnWidths,
  isQuaterlyTotalsOpen,
  rowHeights,
  rowRefs,
  noOfWeeks,
  calendarWeeks,
  setIsTableRendered,
  customerDataTotals,
  ctxStdCalenderData,
  currentData,
  ctxCalenderData,
  setModal,
  gridStates,
  checkboxes,
  currentYear,
}) => {
  const { currency } = useContext(CurrencyContext);
  const [isBreakEvenRendered, setIsBreakevenRendered] = useState(false);
  const [isPriceRendered, setIsPriceRendered] = useState(false);
  const [isValueRendered, setIsValueRendered] = useState(false);
  const [isGPRendered, setIsGPRendered] = useState(false);
  const [QuarterFiveExist, setQuarterFiveExist] = useState(false);
  const [currentFiscalYear, setCurrentFiscalYear] = useState("");

  useEffect(() => {
    setIsTableRendered((prev) => !prev);
    if (currentData[0].quarters.Q5.length > 0) {
      setQuarterFiveExist(true);
    }
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const startWeekYear = currentDate.getFullYear();

    const currentFinanceYear =
      currentMonth >= 10 ? startWeekYear + 1 : startWeekYear;
    setCurrentFiscalYear(currentFinanceYear);
  }, []);

  useEffect(() => {
    const currentWeekRow = document.getElementById("currentWeek");
    if (currentWeekRow) {
      currentWeekRow.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  }, []);

  return (
    <div
      className="relative overflow-auto flex flex-row w-[calc(100%-20px)]"
      style={{ maxHeight: `calc(100vh - ${rowHeights.filterHeight}px - 60px)` }}
    >
      <table
        className="planningtoolgrid relative table-fixed w-full"
        cellSpacing={0}
      >
        <thead ref={rowRefs.theadRef}>
          <tr>
            <TableColumnTh
              isDummy={true}
              checkedStates={checkedStates}
              columnRefs={columnRefs}
              columnWidths={columnWidths}
            />
            {ctxCalenderData.map((ele, index) => (
              <th key={index} className="!h-0 !border-0 !p-0"></th>
            ))}
          </tr>
          {isQuaterlyTotalsOpen && (
            <tr
              id="wrapper"
              className={
                isQuaterlyTotalsOpen ? "open overflow-hidden" : "closed"
              }
            >
              <TableColumnTh
                checkedStates={checkedStates}
                columnRefs={columnRefs}
                columnWidths={columnWidths}
              />

              {checkboxes.q1 && (
                <th
                  colSpan="13"
                  className={`!h-0 !p-0 ${
                    isQuaterlyTotalsOpen ? "border border-[#ddd]" : "!border-0"
                  }`}
                >
                  <div className="flex flex-row list">
                    <div className="transform -rotate-90 flex justify-end">
                      Quarter 1 //!Quarter 1
                    </div>
                    <table
                      className="quartertotals border-collapse"
                      align="center"
                      border="0"
                    >
                      <thead>
                        <tr>
                          <th></th>
                          <th>Budget</th>
                          <th>Actuals</th>
                          <th>Difference</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>TO</td>
                          <td>
                            {Object.keys(
                              customerDataTotals?.quarterlyBudgetTotals ?? {}
                            ).length > 0
                              ? `${currency}${customerDataTotals?.quarterlyBudgetTotals?.Q1?.total_budget_value?.toLocaleString(
                                  undefined,
                                  {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2,
                                  }
                                )}`
                              : "-"}
                          </td>
                          <td>
                            {`${currency}${customerDataTotals?.quarterlyAllProductsTotals?.Q1?.total_all_products_value?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`}
                          </td>
                          <td>
                            {`${currency}${(
                              (customerDataTotals?.quarterlyAllProductsTotals
                                ?.Q1?.total_all_products_value
                                ? customerDataTotals?.quarterlyAllProductsTotals
                                    ?.Q1?.total_all_products_value
                                : 0) -
                              (customerDataTotals?.quarterlyBudgetTotals?.Q1
                                ?.total_budget_value
                                ? customerDataTotals?.quarterlyBudgetTotals?.Q1
                                    ?.total_budget_value
                                : 0)
                            )?.toLocaleString(undefined, {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}`}
                          </td>
                        </tr>
                        <tr>
                          <td>GP</td>
                          <td>
                            {Object.keys(
                              customerDataTotals?.quarterlyBudgetTotals ?? {}
                            ).length > 0
                              ? `${currency}${customerDataTotals?.quarterlyBudgetTotals?.Q1?.total_budget_gross_profit?.toLocaleString(
                                  undefined,
                                  {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2,
                                  }
                                )}`
                              : "-"}
                          </td>
                          <td>
                            {" "}
                            {`${currency}${customerDataTotals?.quarterlyAllProductsTotals?.Q1?.total_all_products_gross_profit?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`}
                          </td>
                          <td>{`${currency}${(
                            (customerDataTotals?.quarterlyAllProductsTotals?.Q1
                              ?.total_all_products_gross_profit
                              ? customerDataTotals?.quarterlyAllProductsTotals
                                  ?.Q1?.total_all_products_gross_profit
                              : 0) -
                            (customerDataTotals?.quarterlyBudgetTotals?.Q1
                              ?.total_budget_gross_profit
                              ? customerDataTotals?.quarterlyBudgetTotals?.Q1
                                  ?.total_budget_gross_profit
                              : 0)
                          )?.toLocaleString(undefined, {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          })}`}</td>
                        </tr>
                        <tr>
                          <td>GP%</td>
                          <td>
                            {Object.keys(
                              customerDataTotals?.quarterlyBudgetTotals ?? {}
                            ).length > 0
                              ?`${customerDataTotals?.quarterlyBudgetTotals?.Q1?.total_budget_gp_percent?.toFixed(
                              2
                            )}%`:"-"}
                          </td>
                          <td>
                            {`${customerDataTotals?.quarterlyAllProductsTotals?.Q1?.total_all_gp_percent?.toFixed(
                              2
                            )}%`}
                          </td>
                          <td></td>
                        </tr>
                      </tbody>
                    </table>
                    <div className="transform -rotate-90 flex justify-end">
                      Quarter 1
                    </div>
                  </div>
                </th>
              )}
              {checkboxes.q2 && (
                <th
                  colSpan="13"
                  className={`!h-0 !p-0 ${
                    isQuaterlyTotalsOpen ? "border border-[#ddd]" : "!border-0"
                  }`}
                >
                  <div className="flex flex-row list">
                    <div className="transform -rotate-90 flex justify-end">
                      Quarter 2 //!Quarter 2
                    </div>
                    <table
                      className="quartertotals border-collapse"
                      align="center"
                      border="0"
                    >
                      <thead>
                        <tr>
                          <th></th>
                          <th>Budget</th>
                          <th>Actuals</th>
                          <th>Difference</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>TO</td>
                          <td>
                            {Object.keys(
                              customerDataTotals?.quarterlyBudgetTotals ?? {}
                            ).length > 0
                              ?`${currency}${customerDataTotals?.quarterlyBudgetTotals?.Q2?.total_budget_value?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`: "-"}
                          </td>
                          <td>
                            {`${currency}${customerDataTotals?.quarterlyAllProductsTotals?.Q2?.total_all_products_value?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`}
                          </td>
                          <td>
                            {`${currency}${(
                              (customerDataTotals?.quarterlyAllProductsTotals
                                ?.Q2?.total_all_products_value
                                ? customerDataTotals?.quarterlyAllProductsTotals
                                    ?.Q2?.total_all_products_value
                                : 0) -
                              (customerDataTotals?.quarterlyBudgetTotals?.Q2
                                ?.total_budget_value
                                ? customerDataTotals?.quarterlyBudgetTotals?.Q2
                                    ?.total_budget_value
                                : 0)
                            )?.toLocaleString(undefined, {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}`}
                          </td>
                        </tr>
                        <tr>
                          <td>GP</td>
                          <td>
                            {Object.keys(
                              customerDataTotals?.quarterlyBudgetTotals ?? {}
                            ).length > 0
                              ?`${currency}${customerDataTotals?.quarterlyBudgetTotals?.Q2?.total_budget_gross_profit?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`: "-"}
                          </td>
                          <td>
                            {" "}
                            {`${currency}${customerDataTotals?.quarterlyAllProductsTotals?.Q2?.total_all_products_gross_profit?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`}
                          </td>
                          <td>{`${currency}${(
                            (customerDataTotals?.quarterlyAllProductsTotals?.Q2
                              ?.total_all_products_gross_profit
                              ? customerDataTotals?.quarterlyAllProductsTotals
                                  ?.Q2?.total_all_products_gross_profit
                              : 0) -
                            (customerDataTotals?.quarterlyBudgetTotals?.Q2
                              ?.total_budget_gross_profit
                              ? customerDataTotals?.quarterlyBudgetTotals?.Q2
                                  ?.total_budget_gross_profit
                              : 0)
                          )?.toLocaleString(undefined, {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          })}`}</td>
                        </tr>
                        <tr>
                          <td>GP%</td>
                          <td>
                            {Object.keys(
                              customerDataTotals?.quarterlyBudgetTotals ?? {}
                            ).length > 0
                              ?`${customerDataTotals?.quarterlyBudgetTotals?.Q2?.total_budget_gp_percent?.toFixed(
                              2
                            )}%`:"-"}
                          </td>
                          <td>
                            {`${customerDataTotals?.quarterlyAllProductsTotals?.Q2?.total_all_gp_percent?.toFixed(
                              2
                            )}%`}
                          </td>
                          <td></td>
                        </tr>
                      </tbody>
                    </table>
                    <div className="transform -rotate-90 flex justify-end">
                      Quarter 1
                    </div>
                  </div>
                </th>
              )}
              {checkboxes.q3 && (
                <th
                  colSpan="13"
                  className={`!h-0 !p-0 ${
                    isQuaterlyTotalsOpen ? "border border-[#ddd]" : "!border-0"
                  }`}
                >
                  <div className="flex flex-row list">
                    <div className="transform -rotate-90 flex justify-end">
                      Quarter 3 //!Quarter 3
                    </div>
                    <table
                      className="quartertotals border-collapse"
                      align="center"
                      border="0"
                    >
                      <thead>
                        <tr>
                          <th></th>
                          <th>Budget</th>
                          <th>Actuals</th>
                          <th>Difference</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>TO</td>
                          <td>
                            {Object.keys(
                              customerDataTotals?.quarterlyBudgetTotals ?? {}
                            ).length > 0
                              ?`${currency}${customerDataTotals?.quarterlyBudgetTotals?.Q3?.total_budget_value?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`:"-"}
                          </td>
                          <td>
                            {`${currency}${customerDataTotals?.quarterlyAllProductsTotals?.Q3?.total_all_products_value?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`}
                          </td>
                          <td>
                            {`${currency}${(
                              (customerDataTotals?.quarterlyAllProductsTotals
                                ?.Q3?.total_all_products_value
                                ? customerDataTotals?.quarterlyAllProductsTotals
                                    ?.Q3?.total_all_products_value
                                : 0) -
                              (customerDataTotals?.quarterlyBudgetTotals?.Q3
                                ?.total_budget_value
                                ? customerDataTotals?.quarterlyBudgetTotals?.Q3
                                    ?.total_budget_value
                                : 0)
                            )?.toLocaleString(undefined, {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}`}
                          </td>
                        </tr>
                        <tr>
                          <td>GP</td>
                          <td>
                            {Object.keys(
                              customerDataTotals?.quarterlyBudgetTotals ?? {}
                            ).length > 0
                              ?`${currency}${customerDataTotals?.quarterlyBudgetTotals?.Q3?.total_budget_gross_profit?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`:"-"}
                          </td>
                          <td>
                            {" "}
                            {`${currency}${customerDataTotals?.quarterlyAllProductsTotals?.Q3?.total_all_products_gross_profit?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`}
                          </td>
                          <td>{`${currency}${(
                            (customerDataTotals?.quarterlyAllProductsTotals?.Q3
                              ?.total_all_products_gross_profit
                              ? customerDataTotals?.quarterlyAllProductsTotals
                                  ?.Q3?.total_all_products_gross_profit
                              : 0) -
                            (customerDataTotals?.quarterlyBudgetTotals?.Q3
                              ?.total_budget_gross_profit
                              ? customerDataTotals?.quarterlyBudgetTotals?.Q3
                                  ?.total_budget_gross_profit
                              : 0)
                          )?.toLocaleString(undefined, {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          })}`}</td>
                        </tr>
                        <tr>
                          <td>GP%</td>
                          <td>
                            {Object.keys(
                              customerDataTotals?.quarterlyBudgetTotals ?? {}
                            ).length > 0?`${customerDataTotals?.quarterlyBudgetTotals?.Q3?.total_budget_gp_percent?.toFixed(
                              2
                            )}%`:"-"}
                          </td>
                          <td>
                            {`${customerDataTotals?.quarterlyAllProductsTotals?.Q3?.total_all_gp_percent?.toFixed(
                              2
                            )}%`}
                          </td>
                          <td></td>
                        </tr>
                      </tbody>
                    </table>
                    <div className="transform -rotate-90 flex justify-end">
                      Quarter 1
                    </div>
                  </div>
                </th>
              )}
              {checkboxes.q4 && (
                <th
                  colSpan="13"
                  className={`!h-0 !p-0 ${
                    isQuaterlyTotalsOpen ? "border border-[#ddd]" : "!border-0"
                  }`}
                >
                  <div className="flex flex-row list">
                    <div className="transform -rotate-90 flex justify-end">
                      Quarter 4 //!Quarter 4
                    </div>
                    <table
                      className="quartertotals border-collapse"
                      align="center"
                      border="0"
                    >
                      <thead>
                        <tr>
                          <th></th>
                          <th>Budget</th>
                          <th>Actuals</th>
                          <th>Difference</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>TO</td>
                          <td>
                            {Object.keys(
                              customerDataTotals?.quarterlyBudgetTotals ?? {}
                            ).length > 0?`${currency}${customerDataTotals?.quarterlyBudgetTotals?.Q4?.total_budget_value?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`:"-"}
                          </td>
                          <td>
                            {`${currency}${customerDataTotals?.quarterlyAllProductsTotals?.Q4?.total_all_products_value?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`}
                          </td>
                          <td>
                            {`${currency}${(
                              (customerDataTotals?.quarterlyAllProductsTotals
                                ?.Q4?.total_all_products_value
                                ? customerDataTotals?.quarterlyAllProductsTotals
                                    ?.Q4?.total_all_products_value
                                : 0) -
                              (customerDataTotals?.quarterlyBudgetTotals?.Q4
                                ?.total_budget_value
                                ? customerDataTotals?.quarterlyBudgetTotals?.Q4
                                    ?.total_budget_value
                                : 0)
                            )?.toLocaleString(undefined, {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}`}
                          </td>
                        </tr>
                        <tr>
                          <td>GP</td>
                          <td>
                            {Object.keys(
                              customerDataTotals?.quarterlyBudgetTotals ?? {}
                            ).length > 0?`${currency}${customerDataTotals?.quarterlyBudgetTotals?.Q4?.total_budget_gross_profit?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`:"-"}
                          </td>
                          <td>
                            {" "}
                            {`${currency}${customerDataTotals?.quarterlyAllProductsTotals?.Q4?.total_all_products_gross_profit?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`}
                          </td>
                          <td>{`${currency}${(
                            (customerDataTotals?.quarterlyAllProductsTotals?.Q4
                              ?.total_all_products_gross_profit
                              ? customerDataTotals?.quarterlyAllProductsTotals
                                  ?.Q4?.total_all_products_gross_profit
                              : 0) -
                            (customerDataTotals?.quarterlyBudgetTotals?.Q4
                              ?.total_budget_gross_profit
                              ? customerDataTotals?.quarterlyBudgetTotals?.Q4
                                  ?.total_budget_gross_profit
                              : 0)
                          )?.toLocaleString(undefined, {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          })}`}</td>
                        </tr>
                        <tr>
                          <td>GP%</td>
                          <td>
                            {Object.keys(
                              customerDataTotals?.quarterlyBudgetTotals ?? {}
                            ).length > 0?`${customerDataTotals?.quarterlyBudgetTotals?.Q4?.total_budget_gp_percent?.toFixed(
                              2
                            )}%`:"-"}
                          </td>
                          <td>
                            {`${customerDataTotals?.quarterlyAllProductsTotals?.Q4?.total_all_gp_percent?.toFixed(
                              2
                            )}%`}
                          </td>
                          <td></td>
                        </tr>
                      </tbody>
                    </table>
                    <div className="transform -rotate-90 flex justify-end">
                      Quarter 1
                    </div>
                  </div>
                </th>
              )}

              {checkboxes.q4 && QuarterFiveExist && (
                <th
                  colSpan="13"
                  className={`!h-0 !p-0 ${
                    isQuaterlyTotalsOpen ? "border border-[#ddd]" : "!border-0"
                  }`}
                >
                  <div className="flex flex-row list">
                    <div className="transform -rotate-90 flex justify-end">
                      Quarter 5 //!Quarter 5
                    </div>
                    <table
                      className="quartertotals border-collapse"
                      align="center"
                      border="0"
                    >
                      <thead>
                        <tr>
                          <th></th>
                          <th>Budget</th>
                          <th>Actuals</th>
                          <th>Difference</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>TO</td>
                          <td>
                            {`${currency}${customerDataTotals?.quarterlyBudgetTotals?.Q5?.total_budget_value?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`}
                          </td>
                          <td>
                            {`${currency}${customerDataTotals?.quarterlyAllProductsTotals?.Q5?.total_all_products_value?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`}
                          </td>
                          <td>
                            {`${currency}${(
                              (customerDataTotals?.quarterlyAllProductsTotals
                                ?.Q5?.total_all_products_value
                                ? customerDataTotals?.quarterlyAllProductsTotals
                                    ?.Q5?.total_all_products_value
                                : 0) -
                              (customerDataTotals?.quarterlyBudgetTotals?.Q5
                                ?.total_budget_value
                                ? customerDataTotals?.quarterlyBudgetTotals?.Q5
                                    ?.total_budget_value
                                : 0)
                            )?.toLocaleString(undefined, {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}`}
                          </td>
                        </tr>
                        <tr>
                          <td>GP</td>
                          <td>
                            {`${currency}${customerDataTotals?.quarterlyBudgetTotals?.Q5?.total_budget_gross_profit?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`}
                          </td>
                          <td>
                            {" "}
                            {`${currency}${customerDataTotals?.quarterlyAllProductsTotals?.Q5?.total_all_products_gross_profit?.toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}`}
                          </td>
                          <td>{`${currency}${(
                            (customerDataTotals?.quarterlyAllProductsTotals?.Q5
                              ?.total_all_products_gross_profit
                              ? customerDataTotals?.quarterlyAllProductsTotals
                                  ?.Q5?.total_all_products_gross_profit
                              : 0) -
                            (customerDataTotals?.quarterlyBudgetTotals?.Q5
                              ?.total_budget_gross_profit
                              ? customerDataTotals?.quarterlyBudgetTotals?.Q5
                                  ?.total_budget_gross_profit
                              : 0)
                          )?.toLocaleString(undefined, {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          })}`}</td>
                        </tr>
                        <tr>
                          <td>GP%</td>
                          <td>
                            {`${customerDataTotals?.quarterlyBudgetTotals?.Q5?.total_budget_gp_percent?.toFixed(
                              2
                            )}%`}
                          </td>
                          <td>
                            {`${customerDataTotals?.quarterlyAllProductsTotals?.Q5?.total_all_gp_percent?.toFixed(
                              2
                            )}%`}
                          </td>
                          <td></td>
                        </tr>
                      </tbody>
                    </table>
                    <div className="transform -rotate-90 flex justify-end">
                      Quarter 5
                    </div>
                  </div>
                </th>
              )}
            </tr>
          )}
          {/* todo: commented this temporarily */}
          {/* {checkedStates.weeks.retailerWeek && (
            <tr>
              <TableColumnTh
                calenderName="Retailer Week"
                checkedStates={checkedStates}
                columnRefs={columnRefs}
                columnWidths={columnWidths}
              />
              {ctxCalenderData.map((ele, index) => (
                <th key={index}></th>
              ))}
            </tr>
          )} */}

          {/* {checkedStates.weeks.promoCycles && (
            <tr>
              <TableColumnTh
                calenderName="Promo Cycles"
                checkedStates={checkedStates}
                columnRefs={columnRefs}
                columnWidths={columnWidths}
              />
              {ctxCalenderData.map((ele) => (
                <th></th>
              ))}
            </tr>
          )} */}

          {checkedStates.weeks.calenderWeek && (
            <tr>
              <TableColumnTh
                calenderName="Calendar Week"
                checkedStates={checkedStates}
                columnRefs={columnRefs}
                columnWidths={columnWidths}
              />
              {ctxStdCalenderData.map((ele, index) => (
                <th key={index} className="text-sm">
                  {ele?.fiscalweek}
                </th>
              ))}
            </tr>
          )}

          {checkedStates.weeks.EFCWeek && (
            <tr>
              <TableColumnTh
                calenderName="EFC Week"
                checkedStates={checkedStates}
                columnRefs={columnRefs}
                columnWidths={columnWidths}
              />
              {ctxCalenderData.map((ele, index) => (
                <th
                  id={ele.CurrentWeek == ele.fiscalweek ? "currentWeek" : ""}
                  key={index}
                  className="text-sm"
                >
                  {ele.fiscalweek}
                </th>
              ))}
            </tr>
          )}

          <tr>
            <TableColumnTh
              isMainHeader={true}
              checkedStates={checkedStates}
              columnRefs={columnRefs}
              columnWidths={columnWidths}
            />
            {ctxCalenderData?.map((ele, index) => (
              <th key={index} className="text-sm">
                {formatDisplay(ele.startweek)}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="relative">
          <tr
            style={{ top: `${rowHeights.theadHeight}px` }}
            className="sticky z-10"
          >
            <TableColumnTh
              rowId="titlerow"
              rowTitle="FORECAST"
              rowRef={rowRefs.forecastRef}
              headHeight={rowHeights.theadHeight}
              noOfWeeks={noOfWeeks}
              checkedStates={checkedStates}
              columnRefs={columnRefs}
              columnWidths={columnWidths}
            />
          </tr>

          {gridStates.volume && (
            <>
              <tr
                style={{ top: `${rowHeights.forecastHeight}px` }}
                className="sticky z-10"
              >
                <TableColumnTh
                  rowId="sectionrow"
                  rowTitle="VOLUME"
                  rowRef={rowRefs.forecastRef}
                  headHeight={rowHeights.forecastHeight}
                  noOfWeeks={noOfWeeks}
                  checkedStates={checkedStates}
                  columnRefs={columnRefs}
                  columnWidths={columnWidths}
                />
              </tr>
              {currentData?.map((row, index) => {
                const quarterData = [
                  ...(row.quarters?.Q1 || []),
                  ...(row.quarters?.Q2 || []),
                  ...(row.quarters?.Q3 || []),
                  ...(row.quarters?.Q4 || []),
                  ...(row.quarters?.Q5 || []),
                ];
                return (
                  <tr
                    key={index}
                    title={`${
                      !!row.isLockedBy
                        ? `${row.isLockedBy} is currently working on this product`
                        : ""
                    }`}
                  >
                    <TableColumnTh
                      isTd={true}
                      checkedStates={checkedStates}
                      columnRefs={columnRefs}
                      columnWidths={columnWidths}
                      row={row}
                      tdValue={row?.total_product_volume}
                      isLockedBy={row.isLockedBy}
                    />
                    <TableVolumeTd
                      quarterData={quarterData}
                      row={row}
                      previousSunday={ctxCalenderData[0]?.currentStartWeek}
                      currentWeek={ctxCalenderData[0]?.currentWeek}
                      calendarWeeks={calendarWeeks}
                      setModal={setModal}
                      currency={currency}
                      isLockedBy={row.isLockedBy}
                      currentYear={currentYear}
                      fiscalyear={ctxCalenderData[0]?.fiscalyear}
                      currentData={currentData}
                      currentFiscalYear={currentFiscalYear}
                    />
                  </tr>
                );
              })}
              {/* table row to display section totals */}
              <tr>
                <TableColumnTh
                  checkedStates={checkedStates}
                  columnRefs={columnRefs}
                  columnWidths={columnWidths}
                />
                {ctxCalenderData.map((ele, index) => {
                  const weekNo = ele.fiscalweek.toString();
                  return (
                    <Tooltip
                      key={index}
                      content="Total of all products for this week"
                      relationship="label"
                      className="!bg-white"
                    >
                      <td
                        key={index}
                        className={`!text-center font-bold border-[#ddd] ${
                          ele.fiscalyear == currentFiscalYear &&
                          ele.currentWeek == weekNo
                            ? "bg-currentWeek"
                            : ""
                        }`}
                      >
                        {
                          customerDataTotals.weeklyAllProductsTotals?.[
                            ele.fiscalyear
                          ]?.[weekNo]?.total_all_products_volume
                        }
                      </td>
                    </Tooltip>
                  );
                })}
              </tr>
            </>
          )}

          <TableBreakevenSection
            currentData={currentData}
            rowHeight={rowHeights.forecastHeight}
            noOfWeeks={noOfWeeks}
            checkedStates={checkedStates}
            columnRefs={columnRefs}
            columnWidths={columnWidths}
            currentStartWeek={ctxCalenderData[0]?.currentStartWeek}
            currentWeek={ctxCalenderData[0]?.currentWeek}
            calendarWeeks={calendarWeeks}
            setModal={setModal}
            currency={currency}
            setIsBreakevenRendered={setIsBreakevenRendered}
            isGridVisible={gridStates.breakeven}
            fiscalyear={ctxCalenderData[0]?.fiscalyear}
            currentFiscalYear={currentFiscalYear}
          />

          {gridStates.unitprice &&
            ((gridStates.breakeven && isBreakEvenRendered) ||
              !gridStates.breakeven) && (
              <TablePriceSection
                currentData={currentData}
                rowHeight={rowHeights.forecastHeight}
                noOfWeeks={noOfWeeks}
                checkedStates={checkedStates}
                columnRefs={columnRefs}
                columnWidths={columnWidths}
                currentStartWeek={ctxCalenderData[0]?.currentStartWeek}
                currentWeek={ctxCalenderData[0]?.currentWeek}
                calendarWeeks={calendarWeeks}
                setModal={setModal}
                currency={currency}
                setIsPriceRendered={setIsPriceRendered}
                fiscalyear={ctxCalenderData[0]?.fiscalyear}
                currentFiscalYear={currentFiscalYear}
              />
            )}

          {gridStates.value &&
            ((!gridStates.breakeven && !gridStates.unitprice) ||
              (gridStates.breakeven &&
                isBreakEvenRendered &&
                gridStates.unitprice &&
                isPriceRendered) ||
              (!gridStates.breakeven &&
                gridStates.unitprice &&
                isPriceRendered) ||
              (gridStates.breakeven &&
                isBreakEvenRendered &&
                !gridStates.unitprice)) && (
              <TableValueSection
                currentData={currentData}
                rowHeight={rowHeights.forecastHeight}
                noOfWeeks={noOfWeeks}
                checkedStates={checkedStates}
                columnRefs={columnRefs}
                columnWidths={columnWidths}
                currentStartWeek={ctxCalenderData[0]?.currentStartWeek}
                currentWeek={ctxCalenderData[0]?.currentWeek}
                calendarWeeks={calendarWeeks}
                setIsValueRendered={setIsValueRendered}
                currency={currency}
                ctxCalenderData={ctxCalenderData}
                customerDataTotals={customerDataTotals}
                fiscalyear={ctxCalenderData[0]?.fiscalyear}
                currentFiscalYear={currentFiscalYear}
              />
            )}

          {gridStates.grossprofit &&
            ((!gridStates.breakeven &&
              !gridStates.unitprice &&
              !gridStates.value) || // None required
              (gridStates.breakeven &&
                isBreakEvenRendered &&
                gridStates.unitprice &&
                isPriceRendered &&
                gridStates.value &&
                isValueRendered) || // All required and rendered
              (!gridStates.breakeven &&
                gridStates.unitprice &&
                isPriceRendered &&
                gridStates.value &&
                isValueRendered) || // Only unitprice and value required and rendered
              (!gridStates.breakeven &&
                !gridStates.unitprice &&
                gridStates.value &&
                isValueRendered) || // Only value required and rendered
              (!gridStates.breakeven &&
                gridStates.unitprice &&
                isPriceRendered &&
                !gridStates.value) || // Only unitprice required and rendered
              (gridStates.breakeven &&
                isBreakEvenRendered &&
                !gridStates.unitprice &&
                gridStates.value &&
                isValueRendered) || // Only breakeven and value required and rendered
              (gridStates.breakeven &&
                isBreakEvenRendered &&
                !gridStates.unitprice &&
                !gridStates.value) || // Only breakeven required and rendered
              (gridStates.breakeven &&
                isBreakEvenRendered &&
                gridStates.unitprice &&
                isPriceRendered &&
                !gridStates.value)) && ( // Only breakeven and unitprice required and rendered}
              <TableGrossProfitSection
                currentData={currentData}
                rowHeight={rowHeights.forecastHeight}
                noOfWeeks={noOfWeeks}
                checkedStates={checkedStates}
                columnRefs={columnRefs}
                columnWidths={columnWidths}
                currentStartWeek={ctxCalenderData[0]?.currentStartWeek}
                currentWeek={ctxCalenderData[0]?.currentWeek}
                calendarWeeks={calendarWeeks}
                setIsGPRendered={setIsGPRendered}
                currency={currency}
                ctxCalenderData={ctxCalenderData}
                customerDataTotals={customerDataTotals}
                fiscalyear={ctxCalenderData[0]?.fiscalyear}
                currentFiscalYear={currentFiscalYear}
              />
            )}

          {gridStates.gppercent &&
            ((!gridStates.breakeven &&
              !gridStates.unitprice &&
              !gridStates.value &&
              !gridStates.grossprofit) || // None required
              (gridStates.breakeven &&
                isBreakEvenRendered &&
                gridStates.unitprice &&
                isPriceRendered &&
                gridStates.value &&
                isValueRendered &&
                gridStates.grossprofit &&
                isGPRendered) || // All required and rendered
              (!gridStates.breakeven &&
                gridStates.unitprice &&
                isPriceRendered &&
                gridStates.value &&
                isValueRendered &&
                gridStates.grossprofit &&
                isGPRendered) || // Only unitprice, value, and grossprofit required and rendered
              (gridStates.unitprice &&
                isPriceRendered &&
                !gridStates.breakeven &&
                !gridStates.value &&
                !gridStates.grossprofit) || //Only unitprice is required and rendered
              (!gridStates.unitprice &&
                !gridStates.breakeven &&
                gridStates.value &&
                isValueRendered &&
                !gridStates.grossprofit) || //Only value is required and rendered
              (!gridStates.unitprice &&
                gridStates.breakeven &&
                isBreakEvenRendered &&
                gridStates.value &&
                isValueRendered &&
                !gridStates.grossprofit) || //Only breakeven and value is required and rendered
              (gridStates.unitprice &&
                isPriceRendered &&
                !gridStates.breakeven &&
                gridStates.value &&
                isValueRendered &&
                !gridStates.grossprofit) || //Only unitprice and value is required and rendered
              (!gridStates.breakeven &&
                !gridStates.unitprice &&
                gridStates.value &&
                isValueRendered &&
                gridStates.grossprofit &&
                isGPRendered) || // Only value and grossprofit required and rendered
              (!gridStates.breakeven &&
                gridStates.unitprice &&
                isPriceRendered &&
                !gridStates.value &&
                gridStates.grossprofit &&
                isGPRendered) || // Only unitprice and grossprofit required and rendered
              (gridStates.breakeven &&
                isBreakEvenRendered &&
                !gridStates.unitprice &&
                gridStates.value &&
                isValueRendered &&
                gridStates.grossprofit &&
                isGPRendered) || // Only breakeven, value, and grossprofit required and rendered
              (gridStates.breakeven &&
                isBreakEvenRendered &&
                !gridStates.unitprice &&
                !gridStates.value &&
                gridStates.grossprofit &&
                isGPRendered) || // Only breakeven and grossprofit required and rendered
              (gridStates.breakeven &&
                isBreakEvenRendered &&
                gridStates.unitprice &&
                isPriceRendered &&
                !gridStates.value &&
                gridStates.grossprofit &&
                isGPRendered) || // Only breakeven, unitprice, and grossprofit required and rendered
              (gridStates.breakeven &&
                isBreakEvenRendered &&
                gridStates.unitprice &&
                isPriceRendered &&
                gridStates.value &&
                isValueRendered &&
                !gridStates.grossprofit) || // Only breakeven, unitprice, and value required and rendered
              (!gridStates.breakeven &&
                !gridStates.unitprice &&
                !gridStates.value &&
                gridStates.grossprofit &&
                isGPRendered) || // Only grossprofit required and rendered
              (gridStates.breakeven &&
                isBreakEvenRendered &&
                gridStates.unitprice &&
                isPriceRendered &&
                !gridStates.grossprofit &&
                !gridStates.value)) && ( // Only breakeven and unitprice required and rendered // Only grossprofit required and rendered
              <TableGrossProfitPercentageSection
                currentData={currentData}
                rowHeight={rowHeights.forecastHeight}
                noOfWeeks={noOfWeeks}
                checkedStates={checkedStates}
                columnRefs={columnRefs}
                columnWidths={columnWidths}
                currentStartWeek={ctxCalenderData[0]?.currentStartWeek}
                currentWeek={ctxCalenderData[0]?.currentWeek}
                calendarWeeks={calendarWeeks}
                fiscalyear={ctxCalenderData[0]?.fiscalyear}
                currentFiscalYear={currentFiscalYear}
              />
            )}
        </tbody>
      </table>
      <div className="hidden !bg-wip-status"></div>
      <div className="hidden !bg-confirm-status"></div>
      <div className="hidden !bg-issue-status"></div>
      <div className="hidden !bg-volumechange-status"></div>
      <div className="hidden !bg-be-status"></div>
      <div className="hidden !bg-pricechange-status"></div>
    </div>
  );
};

export default WhatifTable;
// export default memo(WhatifTable);
