@tailwind base;
@tailwind components;
@tailwind utilities;
/*@import "bootstrap/bootstrap";*/

@font-face {
  font-family: poppinsblack;
  src: url("../assets/fonts/Poppins-Black.ttf");
}
@font-face {
  font-family: poppinsblackitalic;
  src: url("../assets/fonts/Poppins-BlackItalic.ttf");
}
@font-face {
  font-family: poppinsbold;
  src: url("../assets/fonts/Poppins-Bold.ttf");
}
@font-face {
  font-family: poppinsbolditalic;
  src: url("../assets/fonts/Poppins-BoldItalic.ttf");
}
@font-face {
  font-family: poppinsextrabold;
  src: url("../assets/fonts/Poppins-ExtraBold.ttf");
}
@font-face {
  font-family: poppinsextrabolditalic;
  src: url("../assets/fonts/Poppins-ExtraBoldItalic.ttf");
}
@font-face {
  font-family: poppinsextralight;
  src: url("../assets/fonts/Poppins-ExtraLight.ttf");
}
@font-face {
  font-family: poppinsextralightitalic;
  src: url("../assets/fonts/Poppins-ExtraLightItalic.ttf");
}
@font-face {
  font-family: poppinsitalic;
  src: url("../assets/fonts/Poppins-Italic.ttf");
}
@font-face {
  font-family: poppinslight;
  src: url("../assets/fonts/Poppins-Light.ttf");
}
@font-face {
  font-family: poppinslightitalic;
  src: url("../assets/fonts/Poppins-LightItalic.ttf");
}
@font-face {
  font-family: poppinsmedium;
  src: url("../assets/fonts/Poppins-Medium.ttf");
}
@font-face {
  font-family: poppinsmediumitalic;
  src: url("../assets/fonts/Poppins-MediumItalic.ttf");
}
@font-face {
  font-family: poppinsregular;
  src: url("../assets/fonts/Poppins-Regular.ttf");
}
@font-face {
  font-family: poppinssemibold;
  src: url("../assets/fonts/Poppins-SemiBold.ttf");
}
@font-face {
  font-family: poppinssemibolditalic;
  src: url("../assets/fonts/Poppins-SemiBoldItalic.ttf");
}
@font-face {
  font-family: poppinsitalic;
  src: url("../assets/fonts/Poppins-Italic.ttf");
}
@font-face {
  font-family: poppinsitalic;
  src: url("../assets/fonts/Poppins-Italic.ttf");
}

body,
html {
  height: 100%;
  background-repeat: repeat;
  font-family: "poppinsregular";
  overflow-x: hidden;
  background-color: #f3f8ff;
  font-size: 12px;
}

.dark body,
html {
  height: 100%;
  background-repeat: repeat;
  font-family: "poppinsregular" !important;
  overflow-x: hidden;
  background-color: #0e0e10;
  font-size: 14px;
}

.ag-header .ag-header-cell.header-with-border  {
  border-bottom: 1px solid #ccc;
}

.ag-header-cell-text {
  font-size: 12px;
  color: #333333;
  font-family: "poppinsmedium";
}

button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.dark .ag-header-cell-text {
  font-size: 12px;
  color: #fff;
  font-family: "poppinsmedium";
}

.ag-ltr .ag-cell {
  color: #333333;
  font-family: "poppinsregular";
  font-size: 12px;
}

.dark .ag-ltr .ag-cell {
  color: white;
  font-family: "poppinsregular";
  font-size: 12px;
  background-color: #1d212d;
}

.ag-paging-row-summary-panel-number {
  font-family: "poppinsregular";
  font-size: 12px;
}
.dark .ag-paging-row-summary-panel-number {
  font-family: "poppinsregular";
  font-size: 12px;
  color: white;
}

.ag-paging-page-summary-panel {
  font-family: "poppinsregular";
  font-size: 12px;
}
.dark .ag-paging-page-summary-panel {
  font-family: "poppinsregular";
  font-size: 12px;
  color: white;
}

.pagination {
  font-family: "poppinsregular";
  font-size: 12px;
}
.dark .pagination {
  font-family: "poppinsregular";
  font-size: 12px;
  color: white;
}

.ag-paging-panel {
  justify-content: between;
}
.dark .ag-paging-panel {
  justify-content: between;
  color: white;
  background-color: #1d212d;
}

.dark .ag-center-cols-viewport {
  background-color: #1d212d;
}

.contentsectionbg {
  background-color: white;
}

.dark .contentsectionbg {
  background-color: #1d212d;
  font-family: "poppinsregular";
}

.dark
  .ag-header.ag-header-allow-overflow
  .ag-header-row
  .ag-root-wrapper.ag-layout-normal {
  background-color: #1d212d;
  font-family: "poppinsregular";
}

.dark .ag-header-container {
  background-color: #1d212d;
  font-family: "poppinsregular";
}

.dark .pagination-style {
  color: white;
  font-family: "poppinsregular";
}
.dark .ag-paging-button,
.ag-paging-description {
  font-family: "poppinsregular";
  color: white;
}
.ag-paging-button,
.ag-paging-description {
  font-family: "poppinsregular";
  color: #000;
}
.dark .ag-paging-button.ag-disabled {
  font-family: "poppinsregular";
  color: white;
}

.ag-body-horizontal-scroll-viewport {
  display: none;
} 


.ag-overlay-no-rows-center {
  font-family: "poppinsregular";
  font-size: 12px;
}

nav.sidebar .navbar-nav .open .dropdown-menu > li > a:hover,
nav.sidebar .navbar-nav .open .dropdown-menu > li > a:focus {
  color: #ccc;
  background-color: transparent;
}

nav:hover .forAnimate {
  opacity: 1;
}

.mainmenu {
  background: #002d73;
  border: #002d73;
  border-radius: 0px;
}

.mainmenu .navbar-nav > .active > a,
.mainmenu .navbar-nav > .active > a:hover,
.mainmenu .navbar-nav > .active > a:focus {
  color: #fff;
  background-color: #2a3344;
  border-left: 2px solid #1ca9c0;
  padding-top: 15px;
  padding-bottom: 15px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.mainmenu .navbar-nav > li > a:hover,
.mainmenu .navbar-nav > li > a:focus {
  color: #fff;
  background-color: lightgray;
  padding-top: 15px;
  padding-bottom: 15px;
}

.navbar-default .navbar-nav > li > a {
  color: #00b1a3;
  padding-top: 15px;
  padding-bottom: 15px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-decoration: none;
}

.navbar {
  min-height: 45px;
}

.page-heading {
  color: #333333;
  font-size: 14px;
  margin-top: 0px !important;
  margin-left: 25px;
  /* position: absolute; */
  top: 10px;
  left: 90px;
  font-weight: 600;
}

.page-heading h2 {
  font-size: 16px;
  margin-top: 15px !important;
}

/* ----------- Notification dropdown end ----------------------- */

nav.sidebar .brand a {
  padding: 0;
}

.brand {
  font-size: 24px;
  /* padding: 0px 5px; */
  color: #fff;
  width: 69px;
  /* background-color: #002d73; */
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.titlebar {
  width: 100%;
  position: fixed;
  /* height: 55px; */
  z-index: 98;
  top: 0;
  padding-left: 69px;
}

.accordion {
  cursor: pointer;
  width: 100%;
  text-align: left;
  outline: none;
  transition: 0.4s;
}

.active,
.accordion:hover {
  background-color: transparent;
}

.panel {
  background-color: white;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.2s ease-out;
}

/* .reactSelectCustom .css-1fdsijx-ValueContainer , .reactSelectCustom .css-b62m3t-ValueContainer {
  position: relative;
  top: -5px;
}

.reactSelectCustom .css-1hb7zxy-IndicatorsContainer, .reactSelectCustom .css-1xc3v61-IndicatorsContainer{
  position: relative;
  top: -5px;
} */
/* .reactSelectCustom .css-1jgx7bw-control{
  flex-wrap: nowrap !important;
} */
input[type=date]:invalid::-ms-datetime-edit {
  color: #808080;
}
/* label {
  font-size: 14px;
  color: #505050;
} */
.top-navbar {
  background-color: white;
}
.dark .top-navbar {
  background-color: #000;
  color: white;
}

.dark .pageName {
  color: white;
}

input[type="number"] {
  -webkit-appearance: textfield;
     -moz-appearance: textfield;
          appearance: textfield;
}
input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none;
}

input[type="text"],
input[type="tel"],
input[type="date"],
input[type="email"],
input[type="number"],
select {
  padding-top: 4px;
  padding-bottom: 4px;
  border-color: #d6d6d6;
  outline: 2px solid #ffffff;
  color: #333333;
  font-family: "poppinsregular";
}

input:focus, select:focus , textarea:focus{
  outline-color: #0066ff;
}

.button {
  padding: 4px 12px 4px 12px;
  font-family: "poppinsmedium";
}

.labels {
  font-family: "poppinsregular";
  font-size: 12px;
  color: #505050;
}
.dark .labels {
  font-family: "poppinsregular";
  font-size: 12px;
  color: white;
}

.searchbar {
  font-family: "poppinsregular";
  font-size: 12px;
  color: #AFAFAF;
  background-color: #FFFFFF;
}

.dark .searchbar {
  font-family: "poppinsregular";
  font-size: 12px;
  color: #c0c0c0;
  background-color: #1d212d;
  outline-color: #1d212d;
}

.checkboxbg {
  background-color: #D9D9D9;
}
.dark .checkboxbg {
  background-color: #4d4d4d;
}

.dark select {
  background-color: #1d1d1d;
  color: white;
}

.borderc-theme-blue2 {
  border-color: #0066ff;
}
.dark .borderc-theme-blue2 {
  border-color: #6699ff;
}
.textc-theme-blue2 {
  color: #0066ff;
}
.dark .textc-theme-blue2 {
  color: #6699ff;
}

.bgc-theme-blue2 {
  background-color: #0066ff;
}
.dark .bgc-theme-blue2 {
  background-color: #6699ff;
}
.textc-dark-gray {
  color: #727272;
}
.dark .textc-dark-gray {
  color: #a3a3a3;
}
.borderc-dark-gray {
  color: #727272;
}
.dark .borderc-dark-gray {
  color: #a3a3a3;
}

.input {
  font-size: 12px;
  font-family: "poppinsregular";
}

.dark .inputs {
  font-family: "poppinsregular";
  font-size: 12px;
  color: white;
}

.confirmInputs {
    font-size: 12px;
    color: #333333;
    font-family: "poppinsmedium";
}

.formtitle {
  font-family: "poppinssemibold";
  color: #333333;
  font-size: 12px;
}

.subtitles {
  font-family: "poppinssemibold";
  color: #333333;
  font-size: 14px;
}

.text-blackcolor {
  color:#505050;
  font-size: 12px;
  font-family: "poppinsregular";
}

.ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within {
  border: none !important;
}

.ag-row-odd{
  background-color: #f3f3f3 !important;
}

.ag-cell-value span {
  height: auto !important;
  line-height: normal !important;
}

.ag-cell{
  display:flex;
  align-items:center;
  border: none !important;
}

.ag-cell-value, .ag-header-cell-text{
  text-overflow: unset !important;
}

.pagination-style {
  position: absolute;
  bottom: 10px;
  left: 20px;
}

.pagination-style select {
  background-color: #f2f2f2;
  border-radius: 3px;
}

.ag-ltr .ag-header-select-all{
  margin-right: 12px !important; 
}

.ag-root-wrapper.ag-layout-normal{
  border-radius: 5px !important;
}

.distribution-point .ag-header-container {
    /* width: 100% !important; */
}

.distribution-point .ag-header-row.ag-header-row-column {
    /* width: 100% !important; */
}

.distribution-point .ag-center-cols-container {
    /* width: 100% !important; */
}

.general_section .ag-header-container, .general_section .ag-center-cols-container, .general_section .ag-header-row  {
  width: 100% !important;
}

.general_section .ag-cell {
  /* display: flex; */
  margin-right: 10px;
}

.product_link_def .ag-row .ag-cell {
  display: flex;
  align-items: center;
}

.product_data_def .ag-cell {
  display: flex;
  align-items: center;
}

/* @media (min-width: 640px) {
  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{
    font-size: 10px;
  }
} */

.viewlog .ag-cell-value {
    align-items: center;
    display: flex;
}

@media (min-width: 765px) {
  .main {
    position: absolute;
    width: calc(100% - 40px);
    margin-left: 40px;
    float: right;
  }
  nav.sidebar:hover + .main {
    margin-left: 200px;
  }
  nav.sidebar.navbar.sidebar > .container .navbar-brand,
  .navbar > .container-fluid .navbar-brand {
    margin-left: 0px;
  }
  nav.sidebar .navbar-brand,
  nav.sidebar .navbar-header {
    text-align: center;
    width: 100%;
    margin-left: 0px;
  }
  /* nav.sidebar a {
    padding-bottom: 34px;
  } */

  nav.sidebar .navbar-nav > li {
    font-size: 13px;
  }
  nav.sidebar .navbar-nav .open .dropdown-menu {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: transparent;
    border: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
  }
  nav.sidebar .navbar-collapse,
  nav.sidebar .container-fluid {
    padding: 0 0px 0 0px;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
    color: #777;
  }
  nav.sidebar {
    width: 69px;
    height: 100%;
    margin-bottom: 0px;
    position: fixed;
    top: 0px;
    display: flex;
    align-items: flex-start;
  }
  nav.sidebar li {
    width: 100%;
  }

  .forAnimate {
    opacity: 0;
  }
}


@media (min-width: 1024px) {
  
}

/* @media (min-width: 1280px) {
  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{
    font-size: 14px;
  }}

@media (min-width: 1536px) {
  
}
@media (min-width: 1940px) {
  
} */

.desktop-view-message {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999; /* Ensure it's on top of other content */
}
.block-view-message {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(214, 21, 21, 0.5); /* Semi-transparent black */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999; /* Ensure it's on top of other content */
}

.message-content {
  text-align: center;
  color: white;
}

.message-content h2 {
  font-size: 24px;
  margin-bottom: 10px;
}

.message-content p {
  font-size: 16px;
}
/* quarter filter css */

 #q1:checked  + .labelcheck, #q2:checked  + .labelcheck, #q3:checked  + .labelcheck, #q4:checked  + .labelcheck, #all:checked  + .labelcheck, #needsupdate:checked  + .labelcheck{
  background-color: #00E0D5;
  border:1px solid #00BBB2;
  color:#fff;
  font-weight: 500;
  font-family: "poppinsregular";
}
/* section filter css */
 #volume:checked  + .labelcheck, #breakeven:checked  + .labelcheck, #unitprice:checked  + .labelcheck, #grossprofit:checked  + .labelcheck, #gppercent:checked  + .labelcheck, #value:checked  + .labelcheck{
  background-color: #00E0D5;
  border:1px solid #00BBB2;
  color:#fff;
  font-weight: 500;
  font-family: "poppinsregular";
}
 #bestatus-1:checked  + .labelcheck {
  background-color: #FF9A03;
  color:#fff;
  font-weight: 500;
  font-family: "poppinsregular";
}
#bestatus-2:checked  + .labelcheck{
  background-color: #33CA7F;
  /* border:1px solid #00BBB2; */
  color:#444;
  font-weight: 500;
  font-family: "poppinsregular";
}
#bestatus-3:checked  + .labelcheck{
  background-color: #FFDF37;
  /* border:1px solid #00BBB2; */
  color:#fff;
  font-weight: 500;
  font-family: "poppinsregular";
}
#bestatus-4:checked  + .labelcheck{
  background-color: #FB4646;
  /* border:1px solid #00BBB2; */
  color:#fff;
  font-weight: 500;
  font-family: "poppinsregular";
}

.planningtoolgrid
{
   border-collapse: separate;
   border-spacing: 0;
   /* border: 1px solid #ddd; */
}
.input-number::-webkit-inner-spin-button,
.input-number::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}


.planningtoolgrid thead th {
  padding: 5px;
  text-align: center;
  position: -webkit-sticky; /* for Safari */
  height: 35px;
  width:90px;
  overflow:hidden;
  border: 1px solid #ddd;
  font-family: "poppinsregular";
  font-family: "poppinsregular";
}
/* .planningtoolgrid thead tr, .planningtoolgrid tbody tr{
  border: 1px solid #ddd;
} */
.planningtoolgrid thead tr:first-child th{
  border: none !important;
}
/* .planningtoolgrid tbody tr{
 height:70px !important;
} */
.planningtoolgrid tbody th{
  width:120px; 
  height: 40px;
}
.planningtoolgrid td{
  padding: 5px;
  border: 1px solid #ddd;
  text-align: left;
  z-index:0;
  width:90px; 
  height: 35px !important;
  overflow:hidden;
  font-family: "poppinsregular";
}
.planningtoolgrid thead{
  position:sticky;
  top:0;
  left:0;
  background:#f3f8ff;
  z-index:10;
}

.quartertotals{
  /* border:0; */
  table-layout: fixed;
  width:50%;
  text-align:center;
}
.quartertotals thead th, .quartertotals tbody td{
  text-align: center;
  border:0;
}
.quartertotals thead tr, .quartertotals tbody tr {
    border: none;
}
.quartertotals thead th{
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  background-color: #fff !important;
}
.quartertotals thead{
  z-index: 5 !important;
}

.quartertotals thead th:first-child, .quartertotals tbody td:first-child{
  background-color: #f3f8ff !important;
  width:50px !important;
}
.quartertotals tbody td:nth-child(2){
  background-color: #201E50 !important;
  color:#fff;
  border-bottom: 1px solid #504F66;
  border-right: 5px solid #f3f8ff;
}
.quartertotals tbody td:nth-child(3){
  background-color: #D499B9 !important;
  color:#fff;
  border-bottom: 1px solid #C0749E;
  border-right: 5px solid #f3f8ff;
  border-left: 5px solid #f3f8ff;
}
.quartertotals tbody td:nth-child(4){
  background-color: #EE6C4D !important;
  color:#fff;
  border-bottom: 1px solid #CE4B2C;
  border-left: 5px solid #f3f8ff;
}
.quartertotals tbody tr:last-child td{
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
    border-bottom: none;
}

.titlerow{
  background-color: #00E0D5;
  color:#fff;
  z-index: 9;
  font-size: 15px;
  height: 28px !important;
}
td.sectionrow,th.sectionrow{
  background-color: #DDD;
  color:#444;
  font-weight: 600;
  z-index: 9;
  height: 28px !important;
  font-size: 13px !important;
}
input[type="text"]:disabled, input[type="number"]:disabled, select:disabled{
  background-color: #F6F3F3;
}

#wrapper.closed .list {
  display: none;
   transition: height 200ms;
}

#wrapper.open .list {
  display: block;
   transition: height 200ms;
}

.bg-currentWeek{
  background-color: #fcf6b1;
}
.nodata{
  background-color:#ffecd4;
  color:#b31818
}
.selected{
  color: #0066FF;
  border-bottom: 3px solid #0066FF;
}
.buttonText{
  font-family: 'poppinsmedium';
  
}

/*--- SLP CSS ---*/

.service-level-grid{
   border-collapse: separate;
   border-spacing: 0;
}

.service-level-grid thead th {
  padding: 5px;
  text-align: left;
  position: -webkit-sticky; /* for Safari */
  height: 35px;
  width:90px;
  overflow:hidden;
  border: 1px solid #ddd;
  white-space: normal;
  background:#f3f8ff;
  font-family: "poppinsregular";
}

/* .service-level-grid thead tr:first-child th{
  border: none !important;
} */
.service-level-grid thead tr th:last-child{
  background:#f3f8ff;
  position:sticky;
  right:0;
}
.service-level-grid tbody tr td:last-child{
  background:#fff;
  position:sticky;
  right:0;
}

.service-level-grid tbody th{
  width:120px; 
  height: 40px;
  border: 1px solid #ddd;
  padding:5px;
  z-index: 5;
  background:#f3f8ff;
  font-family: "poppinsregular";
}
.service-level-grid td{
  padding: 5px;
  border: 1px solid #ddd;
  background-color:white;
  text-align: left;
  z-index:0;
  width:90px; 
  height: 35px !important;
  overflow:hidden;
  font-family: "poppinsregular";
}
.service-level-grid thead{
  position:sticky;
  top:0;
  left:0;
  /* background:#f3f8ff; */
  z-index:10;
}

.ag-grid-checkbox-cell .ag-checkbox-input {
  cursor: pointer;
}

.depotdaterange .rdrDateDisplayWrapper{
  display:none;
}

label{
  font-family: "poppinsregular";
}
select::placeholder, .css-1jqq78o-placeholder, .placeholdertext {
  color: #333333 !important;
  opacity: 0.5;
  font-weight: 500;
  font-family:"poppinsregular";
}
.text-truncate2L {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  }

  .variety-disabled-block{
    background-color: #f6f6f6;
    color: #888888;
    border:1px solid #E2E2E2;
    box-shadow: none;
  }
  .variety-disabled-block h4, .variety-disabled-block label{
    color:#797979;
  }