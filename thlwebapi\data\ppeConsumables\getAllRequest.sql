SELECT r.[id]
      ,r.[request_id]
      ,r.[user_id]
      ,r.[site_id]
      ,r.[created_at]
      ,r.[required_date]
      ,r.[is_active]
	  ,ste.[name] as site_name
	  ,sus.[action_id]
	  ,sus.[actioned_by]
	  ,sus.[actioned_by_email]
	  ,sus.[actioned_at] as created_date
	  ,COUNT(ori.id) AS total_items
  FROM [Iss_ppe_consumables_portal].[dbo].[order_requests] r
  left join [Iss_ppe_consumables_portal].[dbo].[order_request_items] ori on r.request_id = ori.request_id and ori.is_active = 1
    left join [Iss_ppe_consumables_portal].[dbo].sites ste on r.site_id = ste.id and ste.is_active = 1
	left join [Iss_ppe_consumables_portal].[dbo].status sus on ori.id = sus.request_item_id and sus.is_active = 1 AND sus.is_latest = 1
   where r.is_active = 1
  group by     
  r.[id],
  r.[request_id],
    r.[user_id],
    r.[site_id],
    r.[created_at],
	   r.[required_date], 
    r.[is_active],      
	ste.[name]
	,sus.[action_id]
	  ,sus.[actioned_by]
	  ,sus.[actioned_by_email]
	  ,sus.[actioned_at]
	order by r.created_at desc
	
