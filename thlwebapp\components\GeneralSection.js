import React, { useState, useRef, useEffect, useMemo, Fragment } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faFloppyDisk,
  faPenToSquare,
  faTrash,
  faInfo,
  faXmark,
} from "@fortawesome/free-solid-svg-icons";
import { Dialog, Transition } from "@headlessui/react";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles//ag-grid.css";
import "ag-grid-community/styles//ag-theme-alpine.css";
import { useRouter } from "next/router";

import { ThreeCircles } from "react-loader-spinner";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { addProphetAjaxCall } from "@/utils/ajaxHandler";
import { useMsal } from "@azure/msal-react";
import { useLoading } from "@/utils/loaders/loadingContext";
import Cookies from "js-cookie";
import { logout } from "@/utils/secureStorage";

const GeneralSection = ({
  data,
  onSubmit,
  isEdit,
  dropdowns,
  setNavType,
  navType,userData
}) => {
  const router = useRouter();
  const { supplierId } = router.query;
  const [tradingName, setTradingName] = useState("");
  const [isTradingNameValid, setIsTradingNameValid] = useState(true); //trading name validation
  const [tradingNameError, setTradingNameError] = useState("");
  const [allowedSections, setAllowedSections] = useState([]);

  const [telephone, setTelephone] = useState("");
  const [edi, setEdi] = useState("");
  const [isTelephoneValid, setIsTelephoneValid] = useState(true); //telephone validation
  const [telephoneError, setTelephoneError] = useState("");

  const [email, setEmail] = useState("");
  const [isEmailValid, setIsEmailValid] = useState(true); //email validation.

  const [addressLine1, setAddressLine1] = useState("");
  const [isAddressLine1Valid, setIsAddressLine1Valid] = useState(true);
  const [addressLine1Error, setAddressLine1Error] = useState("");

  const [isAddressLine2Valid, setIsAddressLine2Valid] = useState("");
  const [addressLine2Error, setAddressLine2Error] = useState("");

  const [addressLine2, setAddressLine2] = useState("");
  const [addressLine3, setAddressLine3] = useState("");
  const [addressLine4, setAddressLine4] = useState("");
  const [countryError, setCountryError] = useState("");
  const [supplierName, setSupplierName] = useState("");
  const [contactId, setContactId] = useState(null);
  const [isSupplierNameValid, setIsSupplierNameValid] = useState(true); //supplier name validation part
  const [supplierNameError, setSupplierNameError] = useState("");

  const [supplierEmail, setSupplierEmail] = useState("");
  const [isSupplierEmailValid, setIsSupplierEmailValid] = useState(true);

  const [supplierTelephone, setSupplierTelephone] = useState("");
  const [isSupplierTelephoneValid, setIsSupplierTelephoneValid] =
    useState(true); //supplier telephone validation.
  const [supplierTelephoneError, setSupplierTelephoneError] = useState("");

  const [TypeOfContact, setTypeOfContact] = useState("");
  const [atLeastOneContactEntered, setAtLeastOneContactEntered] =
    useState(false);
  const [isTypeofContactValid, setIsTypeofContactValid] = useState(true);

  const gridRef = useRef(null);
  const [contacts_Json, setcontacts_json] = useState([]);
  const [prophets, setProphets] = useState([]);
  const [postalCode, setPostalCode] = useState("");
  const [isPostalCodeValid, setIsPostalCodeValid] = useState(true);
  const [postalCodeError, setPostalCodeError] = useState("");

  const [country, setCountry] = useState("");
  const [countryName, setCountryName] = useState("");
  const [loading, setLoading] = useState(false);
  const [isCommonError, setCommonError] = useState("");
  const { instance, accounts } = useMsal();
  const [isOpen, setIsOpen] = useState(false);
  const [isCancelled, setIsCancelled] = useState(false);
  const [isContinue, setIsContinue] = useState(true);
  const [formChange, setFormChange] = useState(false);
  const [countryChange, setCountryChange] = useState(false);
  const [status, setStatus] = useState("");
  const [isContactVisible, setIsContactVisible] = useState(false);
  const [role, setRole] = useState("");
  const [ggn, setGGN] = useState("");
  const [redTractor, setRedTractor] = useState("");
  const [isSupplierAccount, setIsSupplierAccount] = useState("");
  const [prophetObj, setProphetObj] = useState([]);
  const { setIsLoading } = useLoading();
  const [prophetsIds, setProphetIds] = useState();

  const validateTradingName = (name) => {
    const isValid = name?.length <= 50;
    setIsTradingNameValid(isValid);
    setTradingNameError(
      isValid ? "" : "Trading name must be 50 characters or less."
    );
  };

  const handleTradingNameChange = (event) => {
    setFormChange(true);
    const newName = event.target.value;
    setTradingName(newName);
    validateTradingName(newName.trim());
  };

  const handleEmailChange = (event) => {
    setFormChange(true);
    const newEmail = event.target.value;
    setEmail(newEmail);
    if (role?.includes(1)) {
      setIsEmailValid(validateEmail(newEmail.trim()));
    }
  };

  const validateEmail = (email) => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    // const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
    return emailRegex.test(email);
  };

  const validateTelephone = (telephone) => {
    const telephoneRegex = /^(?:\+\d{1,3}\s?)?[\d\s]{9,15}$/;
    const isValid = telephoneRegex.test(telephone);
    if (role?.includes(1)) {
      setIsTelephoneValid(isValid);
      setTelephoneError(
        isValid ? "" : "Please enter a valid telephone number."
      );
    }
  };

  const handleTelephoneChange = (event) => {
    setFormChange(true);
    const newTelephone = event.target.value;
    setTelephone(newTelephone);
    validateTelephone(newTelephone.trim());
  };

  const validateAddressLine1 = (line) => {
    const isValid = line?.length <= 50;
    setIsAddressLine1Valid(isValid);
    setAddressLine1Error(
      isValid ? "" : "Please add a small address of less than 50 characters."
    );
  };

  const validateAddressLine2 = (line) => {
    const isValid = line?.length <= 50;
    setIsAddressLine2Valid(isValid);
    setAddressLine2Error(
      isValid ? "" : "Please add a small address of less than 50 characters."
    );
  };

  const handleAddressLine1Change = (event) => {
    setFormChange(true);
    const newAddressLine1 = event.target.value;
    setAddressLine1(newAddressLine1);
    validateAddressLine1(newAddressLine1.trim());
  };

  const handleAddressLine2Change = (event) => {
    setFormChange(true);
    const newAddressLine2 = event.target.value;
    setAddressLine2(newAddressLine2);
    validateAddressLine2(newAddressLine2.trim());
  };

  const handleAddressLine3Change = (event) => {
    setFormChange(true);
    setAddressLine3(event.target.value);
  };

  const handleAddressLine4Change = (event) => {
    setFormChange(true);
    setAddressLine4(event.target.value);
  };

  const handlePostalCodeChange = (event) => {
    setFormChange(true);
    const newPostalCode = event.target.value.toUpperCase(); // postal code
    setPostalCode(newPostalCode);
    validatePostalCode(newPostalCode.trim());
  };

  const validatePostalCode = (code) => {
    // const regex =
    //   /^[A-Z]{1,2}([0-9]{1,2}|[0-9][A-Z])\s*[0-9][A-Z]{2}$|^([A-Z]\d{2,3}\s?[A-Z]{2})$/;
    const regex =
      /^[A-Z]{1,2}([0-9]{1,2}|[0-9][A-Z])\s*[0-9][A-Z]{2}$|^([A-Z]\d{2,3}\s?[A-Z]{2})$|^\d{5}$/;
    //const isValid = regex.test(code);
    const isValid =
      postalCode !== null || postalCode !== "" || postalCode.length > 15
        ? true
        : false;
    setIsPostalCodeValid(isValid);
    setPostalCodeError(
      isValid
        ? ""
        : "Please enter a valid UK postal code (XX9 9XX format), zip code (ANN NAA format), or a 5-digit number."
    );
  };

  const validateSupplierName = (name) => {
    const isValid = name?.length <= 50;
    setIsSupplierNameValid(isValid);
    setSupplierNameError(
      isValid ? "" : "Supplier name must be 50 characters or less."
    );
  };

  const handleSupplierNameChange = (event) => {
    setFormChange(true);
    const newName = event.target.value;
    setSupplierName(newName);
    validateSupplierName(newName.trim());
  };

  const handleSupplierEmailChange = (event) => {
    if (role?.includes(1)) {
      setFormChange(true);
      const newSupplierEmail = event.target.value;
      setSupplierEmail(newSupplierEmail);
      setIsSupplierEmailValid(validEmail(newSupplierEmail.trim()));
    }
  };

  const handleSupplierTelephoneChange = (event) => {
    setFormChange(true);
    const newSupplierTelephone = event.target.value;
    setSupplierTelephone(newSupplierTelephone);
    validateSupplierTelephone(newSupplierTelephone.trim());
  };

  const validateSupplierTelephone = (telephone) => {
    const telephoneRegex = /^(?:\+\d{1,3}\s?)?[\d\s]{9,15}$/;
    const isValid = telephoneRegex.test(telephone);
    setIsSupplierTelephoneValid(isValid);
    setSupplierTelephoneError(
      isValid ? "" : "Please enter a valid supplier telephone number."
    );
  };

  useEffect(() => {
    const prophetsIdsCookie = Cookies.get("prophets");
    const prophetsIds = prophetsIdsCookie
      ? parseInt(prophetsIdsCookie, 10)
      : null;
    setProphetIds(prophetsIds);
    if (typeof window !== "undefined") {
      const sectionsString = localStorage.getItem("allowedSections");
      if (sectionsString) {
        const parsedSections = sectionsString.split(",");
        setAllowedSections(parsedSections);
      }
    }

    setTradingName(data[0]?.trading_name ?? data[0]?.name ?? "");
    setEmail(data[0]?.email_id ?? "");
    setTelephone(data[0]?.telephone ?? "");
    setAddressLine1(data[0]?.address_line_1 ?? "");
    setAddressLine2(data[0]?.address_line_2 ?? "");
    setAddressLine3(data[0]?.address_line_3 ?? "");
    setAddressLine4(data[0]?.address_line_4 ?? "");
    setPostalCode(data[0]?.postal_code ?? "");
    setCountry(data[0]?.country_id ?? "");
    setCountryName(data[0]?.country_name ?? "");
    setStatus(data[0]?.status ?? "");
    setRedTractor(data[0]?.red_tractor);
    setGGN(data[0]?.global_gap_number);
    setEdi(data[0]?.edi ?? "");

    const role_parse = JSON.parse(data[0].role_ids ?? "[]");
    //const role_ids = role_parse.map()
    const role_ids = role_parse?.map((roleId) => roleId.role_id);
    if (
      role_ids?.includes(2) ||
      role_ids?.includes(3) ||
      role_ids?.includes(4)
    ) {
      setIsContactVisible(false);
    } else {
      setIsContactVisible(true);
    }
    //const role_ids = role_parse?.map((roleId) => roleId.role_id);
    //console.log(role_ids)
    setRole(role_ids);

    const supplierAccountExist = role_ids?.includes(1);
    if (supplierAccountExist) {
      setIsSupplierAccount("true");
    } else {
      setIsSupplierAccount("false");
    }

    const contacts_data_json = JSON.parse(data[0]?.contacts_json ?? "[]");

    const formattedContactsData = contacts_data_json?.map((row) => ({
      id: row?.id,
      supplierName: row?.name,
      supplierEmail: row?.email_id,
      supplierTelephone: row?.telephone,
      TypeOfContact: row?.type_of_contact,
    }));
    setcontacts_json(formattedContactsData);

    //const existingProphetCode = checkExistingProphetCode(supplierId, user);
    const supplierName = data[0]?.name;
    const prophetIds = data[0]?.prophets_id_code;
    setIsLoading(false);
  }, []);

  const validEmail = (email) => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  };

  const savedata = (e) => {
    setFormChange(true);
    e.preventDefault();
    const newItem = {
      supplierName: supplierName.trim(),
      supplierEmail: supplierEmail.trim(),
      supplierTelephone: supplierTelephone.trim(),
      TypeOfContact: TypeOfContact.trim(),
      id: contactId,
    };

    let errorCount = 0;

    if (supplierName == "") {
      setIsSupplierNameValid(false);
      errorCount++;
    }
    // if (supplierName.length <= 50) {
    //   validateSupplierName(supplierName);
    //   errorCount++;
    // }
    if (supplierEmail == "" || !validEmail(supplierEmail.trim())) {
      setIsSupplierEmailValid(false);
      errorCount++;
    }
    if (supplierTelephone == "") {
      setIsSupplierTelephoneValid(false);
      validateSupplierTelephone(supplierTelephone.trim());
      errorCount++;
    }

    if (TypeOfContact == "") {
      setIsTypeofContactValid(false);
      errorCount++;
    }

    if (errorCount > 0) {
      return;
    }

    setcontacts_json((prevContactsJson) => {
      if (!Array.isArray(prevContactsJson)) {
        return [newItem];
      }
      return [...prevContactsJson, newItem];
      // setAtLeastOneContactEntered(true); // Update state to indicate at least one contact is entered
      // return updatedContacts;
    });
    setSupplierEmail("");
    setSupplierName("");
    setSupplierTelephone("");
    setTypeOfContact("");
    setContactId(null);
    setAtLeastOneContactEntered(true);
  };

  const IconsRenderer = (props) => {
    let updatedData;

    const handleDelete = (e) => {
      setFormChange(true);
      e.preventDefault();
      const rowData = props.data;
      updatedData = [...contacts_Json];
      const index = updatedData?.indexOf(rowData);
      updatedData.splice(index, 1);

      props.api.applyTransaction({ remove: updatedData });
      setcontacts_json(updatedData);
    };

    const handleEdit = (e) => {
      setFormChange(true);
      e.preventDefault();
      const rowData = props?.data;

      updatedData = [...contacts_Json];

      const index = updatedData.indexOf(rowData);
      updatedData.splice(index, 1);

      props.api.applyTransaction({ remove: updatedData });
      setcontacts_json(updatedData);
      setContactId(rowData?.id);
      setSupplierName(rowData?.supplierName);
      setSupplierEmail(rowData?.supplierEmail);
      setSupplierTelephone(rowData?.supplierTelephone);
      setTypeOfContact(rowData?.TypeOfContact ?? "");
      if (!isSupplierNameValid) {
        setIsSupplierNameValid(true);
      }
      if (!isSupplierEmailValid) {
        setIsSupplierEmailValid(true);
      }
      if (!isSupplierTelephoneValid) {
        setIsSupplierTelephoneValid(true);
      }
      if (!isTypeofContactValid) {
        setIsTypeofContactValid(true);
      }
    };

    return (
      <div className="flex flex-row gap-4 justify-center text-skin-primary">
        <button onClick={handleEdit}>
          <FontAwesomeIcon icon={faPenToSquare} />
        </button>
        <button onClick={handleDelete} className="text-red-500">
          <FontAwesomeIcon icon={faTrash} />
        </button>
      </div>
    );
  };

  const defaultColDef = useMemo(() => ({
    //sortable: true,
    filter: false,
    resizable: true,
    flex: 1,
    suppressMenu: false,
  }));
  // const CustomCellRenderer = (params) => {
  //   const truncatedText =
  //     params?.value && params?.value?.length > 12
  //       ? params?.value?.substring(0, 12) + "..."
  //       : params?.value;

  //   return <span title={params?.value}>{truncatedText}</span>;
  // };

  const CustomTooltipComponent = ({ value }) => (
    <div title={value}>{value}</div>
  );

  const columnDefs = [
    {
      headerName: "Name",
      field: "supplierName",
      tooltipComponent: CustomTooltipComponent,
      headerClass: "header-with-border",
      cellStyle: { display: "flex" },
      flex: "2%",
    },
    {
      headerName: "Email ID",
      field: "supplierEmail",
      tooltipComponent: CustomTooltipComponent,
      headerClass: "header-with-border",
      cellStyle: { display: "flex" },
      flex: "2%",
    },
    {
      headerName: "Telephone",
      field: "supplierTelephone",
      tooltipComponent: CustomTooltipComponent,
      headerClass: "header-with-border",
      cellStyle: { display: "flex" },
      flex: "2%",
    },
    {
      headerName: "Type of Contact",
      field: "TypeOfContact",
      tooltipComponent: CustomTooltipComponent,
      headerClass: "header-with-border",
      cellStyle: { display: "flex" },
      flex: "2%",
    },
    {
      field: "",
      cellRenderer: IconsRenderer,
      headerClass: "header-with-border",
      cellStyle: { display: "flex", justifyContent: "end" },
      flex: "1%",
    },
  ];

  function handleTypeOfContactChange(e) {
    setFormChange(true);
    setTypeOfContact(e.target.value);
    setIsTypeofContactValid(true);
  }

  const isFormValid = () => {
    // Check validity of all required fields
    const isTradingNameValid = tradingName?.length <= 50;
    const isEmailValid = validateEmail(email.trim());
    const isTelephoneValid = validateTelephone(telephone.trim());
    const isPostalCodeValid = postalCode?.length <= 8;
    const isSupplierNameValid = supplierName?.length <= 50;
    const isSupplierEmailValid = validEmail(supplierEmail.trim());
    const isSupplierTelephoneValid = validateSupplierTelephone(
      supplierTelephone.trim()
    );

    // Check if at least one contact is entered
    const isAtLeastOneContactEntered = atLeastOneContactEntered;

    // Set state to update UI based on validity
    setIsTradingNameValid(isTradingNameValid);
    setIsEmailValid(isEmailValid);
    setIsTelephoneValid(isTelephoneValid);
    setIsPostalCodeValid(isPostalCodeValid);
    setIsSupplierNameValid(isSupplierNameValid);
    setIsSupplierEmailValid(isSupplierEmailValid);
    setIsSupplierTelephoneValid(isSupplierTelephoneValid);

    // Return true only if all required fields are valid and at least one contact is entered
    return (
      isTradingNameValid &&
      isEmailValid &&
      isTelephoneValid &&
      isPostalCodeValid &&
      isSupplierNameValid &&
      isSupplierEmailValid &&
      isSupplierTelephoneValid &&
      isAtLeastOneContactEntered
    );
  };

  const handleValidate = (step, isContinue) => {
    let errorCount = 0;
    const roles = JSON.parse(data[0]?.role_ids ?? "[]");
    const role_ids = roles?.map((item) => item?.role_id);

    if (!country) {
      //alert(country);
      setCountryError("Please select country.");
      toast.error(
        "Cannot proceed without selecting a Country. Kindly select a Country"
      );
      return;
    }

    if (tradingName === "") {
      setIsTradingNameValid(false);
      setTradingNameError("Trading name is required.");
      errorCount++;
    }
    if (role_ids?.includes(1)) {
      if (email === "" || !isEmailValid) {
        setIsEmailValid(false);
        errorCount++;
      }

      if (telephone === "" || !isTelephoneValid) {
        setIsTelephoneValid(false);
        setTelephoneError("Telephone is required.");
        errorCount++;
      }
    }

    if (addressLine1 === "") {
      setIsAddressLine1Valid(false);
      errorCount++;
      setAddressLine1Error("Please add an address. ");
    }

    if (addressLine2 === "") {
      setIsAddressLine2Valid(false);
      errorCount++;
      setAddressLine2Error("Please add an address line 2. ");
    }

    if (postalCode === "" || !isPostalCodeValid) {
      setIsPostalCodeValid(false);
      setPostalCodeError("Postal code is required.");
      errorCount++;
    }
    if (role?.includes(1)) {
      if (!contacts_Json || (contacts_Json && contacts_Json.length < 1)) {
        errorCount++;
        toast.error("Please add atleast one contact.");
        //return;
      }
    }
    if (errorCount > 0) {
      setNavType(step);
      setIsOpen(true);
    } else {
      handleSubmit(step, "Complete");
    }

    if (isCancelled) {
      setIsCancelled(false);
      return;
    }

    if (isContinue) {
      handleSubmit(step);
      setIsOpen(false);
    }
  };

  const handleSubmit = (step, technical = "Incomplete") => {
    //const isFormValidResult = isFormValid();
    setLoading(true);
    const apiBase =
      process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8081";

    if (formChange) {
      let currentStatus;
      if (status == 3 || status == 4 || status == 1) {
        currentStatus = 4;
      } else if (status == 2) {
        currentStatus = 2;
      } else {
        currentStatus = 3;
      }
      localStorage.setItem("isFormNew", false);
      fetch(`${apiBase}/api/suppliers/update-supplier/${supplierId}`, {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        credentials: "include", // Use session authentication
        body: JSON.stringify({
          sectionName: "generalSection",
          trading_name: tradingName?.trim(),
          telephone: isTelephoneValid ? telephone?.trim() : "",
          email_id: isEmailValid ? email?.trim() : "",
          address_line_1: isAddressLine1Valid ? addressLine1?.trim() : "",
          address_line_2: isAddressLine2Valid ? addressLine2?.trim() : "",
          address_line_3: addressLine3?.trim(),
          address_line_4: addressLine4?.trim(),
          postal_code: isPostalCodeValid ? postalCode?.trim() : "",
          contacts_json: contacts_Json,
          country: country,
          country_name: countryName ? countryName : null,
          technical: technical,
          updated_date: new Date().toISOString(),
          userId: userData?.user_id,
          status: currentStatus,
          compliance: data[0].compliance,
          procurement: data[0].procurement,
          financial: data[0].financial,
          allowedSections: allowedSections,
          roleIds: role,
          prophet_id: prophetsIds,
          requestor_email: data[0]?.requestor_email,
          requestor_name: data[0]?.requestor_name,
          edi: edi,
        }),
      })
        .then(async (res) => {
          if (res.status === 400) {
            toast.error(
              "There was an error with your request. Please check your data and try again."
            );
          } else if (res.status === 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(async () => {
              await logout();
              router.push("/login");
            }, 3000);
          }
          if (res.status === 200) {
            if (step == "sap") {
              if (isEdit) {
                if (
                  isSupplierAccount == "false" &&
                  countryChange &&
                  (data[0]?.prophets_id_code ||
                    Object.keys(prophetData).length !== 0) &&
                  prophetObj.prophet_code != null
                ) {
                  const result = addProphetAjaxCall(prophetObj);
                  result.then((data) => {
                    if (data?.data) {
                      localStorage.removeItem("isEdit");
                      router.back();
                    }
                  });
                } else {
                  localStorage.removeItem("isEdit");
                  router.back();
                }
              } else {
                if (
                  isSupplierAccount == "false" &&
                  countryChange &&
                  (data[0]?.prophets_id_code.length > 0 ||
                    Object.keys(prophetData).length !== 0) &&
                  prophetObj[0].prophet_code != null
                ) {
                  const result = addProphetAjaxCall(prophetObj);
                  result.then((data) => {
                    if (data?.data) {
                      onSubmit();
                    }
                  });
                } else {
                  onSubmit();
                }
              }
            } else {
              router.push({ pathname: "/suppliers" });
            }
            // setLoading(false);
          }
          return Promise.reject(res);
        })
        .catch((err) => {
          setLoading(false);
          // toast.error(
          //   `Error saving data in general forms file: ${err.statusText}`,
          //   {
          //     position: "top-right",
          //   }
          // );
          return err;
        });
    } else {
      if (step == "sap") {
        if (isEdit) {
          localStorage.removeItem("isEdit");
          router.back();
        } else {
          onSubmit();
        }
      } else {
        router.push({ pathname: "/suppliers" });
      }
    }
  };

  const handleSaveAndExit = () => {
    if (!isFormValid()) {
      return;
    }
    router.push("/confirmPage");
  };
  const [prophetData, setProphetData] = useState({});
  const handleCountryChange = (e) => {
    setFormChange(true);
    setCountryChange(true);
    if (country) {
      setCountryError("");
    }
    const prophetIds = data[0]?.prophets_id_code;
    if (prophetIds && e.target.value && isSupplierAccount === "false") {
      const selectedCountryName = e.target.options[e.target.selectedIndex].text;

      const countryCode = selectedCountryName === "United Kingdom" ? "UK" : "";

      const identifier = countryCode === "UK" ? redTractor : ggn;
      const generateProphetCode =
        identifier && identifier?.slice(-6).padStart(6, "X");

      const prophet = JSON.parse(prophetIds ?? "[]");

      setProphetData({
        prophet_id: prophet[0]?.prophet_id,
        prophet_code:
          generateProphetCode &&
          generateProphetCode?.toString().trim().toUpperCase(),
        supplier_id: parseInt(supplierId),
      });
      setProphetObj([prophetData]);
    }
  };
  const closeModal = (e) => {
    if (e) {
      e.preventDefault();
    }
    setIsCancelled(true);
    setIsOpen(false);
  };

  const handleContinueSubmit = () => {
    handleValidate(navType, isContinue);
    setIsOpen(false);
  };

  return (
    <>
      <ToastContainer limit={1} />
      {loading ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "calc(100vh - 100px)",
            width: "calc(100vw - 125px)",
          }}
        >
          <ThreeCircles
            color="#002D73"
            height={50}
            width={50}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      ) : (
        <div className="relative panel-container bg-white rounded-lg w-[93%] lg:w-[95%] 2xl:w-[calc(100%-70px)] p-4 pb-0">
          <div className="flex md:flex-row flex-col my-2">
            <div
              className={`px-5 pe-8 mb-0 h-100vh border-e-[1px] border-light-gray ${
                role?.includes(1) ? "md:w-1/2" : "md:w-full"
              }`}
            >
              <div className="mb-6">
                <div className="mb-3">
                  <h4 className="formtitle pb-1 border-b border-light-gray3">
                    Supplier Details
                  </h4>
                </div>

                <div
                  className={`grid ${
                    role?.includes(1) ? "lg:grid-cols-2" : "lg:grid-cols-3"
                  } gap-4  ${
                    role?.includes(1) ? "grid-cols-1" : "grid-cols-3"
                  }`}
                >
                  <div className="flex flex-col">
                    <label className="labels mb-1">
                      Trading Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      name="Trading_name"
                      maxLength={50}
                      value={tradingName}
                      onChange={handleTradingNameChange}
                      className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border ${
                        isTradingNameValid
                          ? "border-light-gray"
                          : "!border-red-500"
                      } rounded-md`}
                      required
                      style={{ textTransform: "capitalize" }}
                      tabIndex={1}
                    />
                    {!isTradingNameValid && (
                      <p className="text-red-500 text-sm mt-1">
                        Please enter a valid name of max 50 chars.
                      </p>
                    )}
                  </div>

                  <div className="flex flex-col">
                    <label className="labels mb-1">
                      Email ID
                      {role?.includes(1) && (
                        <span className="text-red-500">*</span>
                      )}
                    </label>
                    <input
                      type="text"
                      name="email_id"
                      maxLength={80}
                      value={email}
                      onChange={handleEmailChange}
                      className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border ${
                        isEmailValid ? "border-light-gray" : "border-red-500"
                      } rounded-md`}
                      required
                      tabIndex={2}
                    />
                    {!isEmailValid && (
                      <p className="text-red-500 text-sm mt-1">
                        Please enter a valid email address.
                      </p>
                    )}
                  </div>

                  <div className="flex flex-col">
                    <label className="labels mb-1">
                      Telephone{" "}
                      {role?.includes(1) && (
                        <span className="text-red-500">*</span>
                      )}
                    </label>
                    <input
                      type="text"
                      name="telephone"
                      maxLength={15}
                      value={telephone}
                      onChange={handleTelephoneChange}
                      className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border ${
                        isTelephoneValid
                          ? "border-light-gray"
                          : "border-red-500"
                      } rounded-md`}
                      required
                      tabIndex={3}
                    />
                    {!isTelephoneValid && (
                      <p className="text-red-500 text-sm mt-1">
                        {telephoneError}
                      </p>
                    )}
                  </div>
                  <div className="flex flex-col">
                    <label className="labels mb-1">EDI ANA Number</label>
                    <input
                      type="text"
                      name="edi"
                      maxLength={13}
                      value={edi}
                      onChange={(e) => {
                        setEdi(e.target.value);
                        setFormChange(true);
                      }}
                      className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md`}
                      tabIndex={4}
                    />
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <div className="mb-3">
                  <h4 className="formtitle pb-2 border-b border-light-gray3">
                    Address
                  </h4>
                </div>

                <div
                  className={`grid ${
                    role?.includes(1) ? "lg:grid-cols-2" : "lg:grid-cols-3"
                  } grid-cols-1 gap-4`}
                >
                  <div className="flex flex-col">
                    <label className="labels mb-1">
                      Address Line 1 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      name="address_line_1"
                      maxLength={50}
                      value={addressLine1}
                      onChange={handleAddressLine1Change}
                      className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border ${
                        isAddressLine1Valid
                          ? "border-light-gray"
                          : "border-red-500"
                      } rounded-md`}
                      required
                      tabIndex={4}
                      style={{ textTransform: "capitalize" }}
                    />
                    {!isAddressLine1Valid && (
                      <p className="text-red-500 text-sm mt-1">
                        {addressLine1Error}
                      </p>
                    )}
                  </div>

                  <div className="flex flex-col">
                    <label className="labels mb-1">
                      Address Line 2 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      name="address_line_2"
                      maxLength={50}
                      value={addressLine2}
                      onChange={handleAddressLine2Change}
                      className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border border-light-gray rounded-md ${
                        isAddressLine2Valid
                          ? "border-light-gray"
                          : "border-red-500"
                      }`}
                      required
                      tabIndex={5}
                    />
                    {!isAddressLine2Valid && (
                      <p className="text-red-500 text-sm mt-1">
                        {addressLine2Error}
                      </p>
                    )}
                  </div>

                  <div className="flex flex-col">
                    <label className="labels mb-1">Address Line 3</label>
                    <input
                      type="text"
                      name="address_line_3"
                      maxLength={50}
                      value={addressLine3}
                      onChange={handleAddressLine3Change}
                      className="w-full py-1 px-2 2xl:px-3 2xl:py-3 border border-light-gray rounded-md"
                      tabIndex={6}
                    />
                  </div>

                  <div className="flex flex-col">
                    <label className="labels mb-1">Address Line 4</label>
                    <input
                      type="text"
                      name="address_line_4"
                      maxLength={50}
                      value={addressLine4}
                      onChange={handleAddressLine4Change}
                      className="w-full py-1 px-2 2xl:px-3 2xl:py-3 border border-light-gray rounded-md"
                      tabIndex={7}
                    />
                  </div>

                  <div className="flex flex-col">
                    <label className="labels mb-1" required>
                      Country <span className="text-red-500">*</span>
                    </label>

                    <select
                      tabIndex={8}
                      type="text"
                      name="country"
                      value={country}
                      onChange={(e) => {
                        setFormChange(true);
                        const selectedCountryId = e.target.value;
                        const selectedCountryName =
                          e.target.options[e.target.selectedIndex].text;
                        setCountry(selectedCountryId);
                        setCountryName(selectedCountryName);
                        handleCountryChange(e);
                      }}
                      className="w-full px-2 2xl:px-3 border border-light-gray rounded-md"
                      required
                      onBlur={handleCountryChange}
                    >
                      <option value="" disabled>
                        Select...
                      </option>
                      {dropdowns?.countries &&
                        dropdowns?.countries?.map((con, key) => {
                          return (
                            <option
                              key={key}
                              value={con.id}
                              defaultValue={
                                con?.name?.trim() == countryName?.trim()
                                  ? true
                                  : false
                              }
                            >
                              {con?.name}
                            </option>
                          );
                        })}
                    </select>
                    {countryError && (
                      <p className="text-red-500 text-sm mt-1">
                        {countryError}
                      </p>
                    )}
                  </div>

                  <div className="flex flex-col">
                    <label className="labels mb-1">
                      Postal Code/Zip Code{" "}
                      <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      name="postal_code"
                      maxLength={10}
                      value={postalCode}
                      onChange={handlePostalCodeChange}
                      className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border ${
                        isPostalCodeValid
                          ? "border-light-gray"
                          : "border-red-500"
                      } rounded-md`}
                      required
                      tabIndex={9}
                    />
                    {!isPostalCodeValid && (
                      <p className="text-red-500 text-sm mt-1">
                        {postalCodeError}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
            {role?.includes(1) && (
              <div className="p-x5 ps-8 mb-0 w-1/2">
                <div className="mb-6">
                  <div className="mb-3">
                    <h4 className="formtitle pb-1 border-b border-light-gray3">
                      Contact
                    </h4>
                  </div>

                  <div className="flex flex-row">
                    {/* <!-- <div className="flex flex-row justify-between items-center mb-3">
<h4 className="text-lg font-semibold pb-0">For Stock / order queries</h4>
<button className="border border-blue-500 text-blue-500 rounded-md py-2 px-14 font-semibold">Add</button>
</div> --> */}
                    <div className="grid lg:grid-cols-2 grid-cols-1 gap-4 mb-6 w-full">
                      <div className="flex flex-col">
                        <label className="labels mb-1">
                          Name <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          name="supplier_name"
                          value={supplierName}
                          onChange={handleSupplierNameChange}
                          className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border ${
                            isSupplierNameValid
                              ? "border-light-gray"
                              : "!border-red-500"
                          } rounded-md`}
                          maxLength={50}
                          required
                          style={{ textTransform: "capitalize" }}
                        />
                        {!isSupplierNameValid && (
                          <p className="text-red-500 text-sm mt-1">
                            Please enter a name with max 50 chars.
                          </p>
                        )}
                      </div>

                      <div className="flex flex-col">
                        <label className="labels mb-1">
                          Email ID <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          name="supplier_email_id"
                          value={supplierEmail}
                          onChange={handleSupplierEmailChange}
                          className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border ${
                            isSupplierEmailValid
                              ? "border-light-gray"
                              : "!border-red-500"
                          } rounded-md`}
                          maxLength={50}
                          required
                        />
                        {!isSupplierEmailValid && (
                          <p className="text-red-500 text-sm mt-1">
                            Please enter a valid email address.
                          </p>
                        )}
                      </div>

                      <div className="flex flex-col">
                        <label className="labels mb-1">
                          Telephone <span className="text-red-500">*</span>
                        </label>
                        <div className="relative">
                          <input
                            type="text"
                            name="supplier_telephone"
                            value={supplierTelephone}
                            onChange={handleSupplierTelephoneChange}
                            className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border ${
                              isSupplierTelephoneValid
                                ? "border-light-gray"
                                : "!border-red-500"
                            } rounded-md`}
                            maxLength={15}
                            required
                          />
                          {!isSupplierTelephoneValid && (
                            <p className="text-red-500 text-sm mt-1">
                              Please enter a valid Telephone number.
                            </p>
                          )}
                        </div>
                      </div>

                      <div className="flex flex-col">
                        <label className="labels mb-1">
                          Type of Contact{" "}
                          <span className="text-red-500">*</span>
                        </label>
                        <div className="relative">
                          <select
                            className={`border ${
                              isTypeofContactValid
                                ? "border-light-gray"
                                : "border-bright-red"
                            } rounded-md px-2 2xl:px-3`}
                            name="sendac group"
                            onChange={handleTypeOfContactChange}
                            value={TypeOfContact}
                            style={{ width: "100%" }}
                          >
                            <option value="" disabled>
                              Select...
                            </option>
                            {dropdowns?.type_of_contacts &&
                              dropdowns?.type_of_contacts.map(
                                (contact, key) => {
                                  return (
                                    <option key={key} value={contact.name}>
                                      {contact.name}
                                    </option>
                                  );
                                }
                              )}
                            {/* <option value="For stock / order queries">
                            For stock / order queries
                          </option>
                          <option value="For account queries">
                            For account queries
                          </option>
                          <option value="For IT account queries">
                            For IT account queries
                          </option>
                          <option value="For QC queries">For QC queries</option> */}
                          </select>
                          {!isTypeofContactValid ? (
                            <p className="text-red-500 text-sm mt-1 absolute bottom-[-20px]">
                              Please select a type of contact.
                            </p>
                          ) : (
                            ""
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex w-[10%] items-end justify-center mb-6 ">
                      <span
                        onClick={savedata}
                        tabIndex={0}
                        className={`px-2 py-1 2xl:px-3.5 2xl:py-1 border border-skin-primary  text-skin-primary  rounded-md ml-8  cursor-pointer ${
                          !isTypeofContactValid && !isSupplierTelephoneValid
                            ? "mb-6"
                            : !isSupplierTelephoneValid
                            ? "mb-6"
                            : "mb-0"
                        }`}
                      >
                        <FontAwesomeIcon icon={faFloppyDisk} />
                      </span>
                    </div>
                  </div>
                </div>
                <div
                  style={{ height: 200, width: "100%" }}
                  className="general_section"
                >
                  <AgGridReact
                    defaultColDef={defaultColDef}
                    columnDefs={columnDefs}
                    rowData={contacts_Json}
                    rowHeight={25}
                    ref={gridRef}
                  ></AgGridReact>
                  <style>
                    {`
          .ag-header .ag-header-cell.header-with-border {
            border-bottom: 1px solid #ccc; /* Style for header row */
          }
        `}
                  </style>
                </div>
                {/* <div>
<table>
<thead>
<td>Name</td>
</thead>
</table>
</div> */}
              </div>
            )}
          </div>
          <div className="flex justify-between border-t border-light-gray py-5 bg-white">
            <div>
              <button
                className="border border-skin-primary text-skin-primary button px-8 rounded-md"
                onClick={() =>
                  router.push({
                    pathname: `/supplier/${supplierId}/edit`,
                  })
                }
              >
                Previous
              </button>
            </div>
            <div>
              <button
                className="border border-skin-primary text-skin-primary me-10 py-1 px-8 font-medium rounded-md"
                onClick={() => handleValidate("sae")}
              >
                Save & Exit
              </button>
              <button
                className="border  border-skin-primary text-white bg-skin-primary rounded-md py-1 px-8 font-medium"
                onClick={() => handleValidate("sap")}
              >
                Save & Proceed
              </button>
            </div>
          </div>
        </div>
      )}
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                  {/* <!-- Modal content --> */}
                  <div className="relative bg-white rounded-lg shadow">
                    {/* <!-- Modal header --> */}
                    <div className="flex items-start justify-between p-8 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                        <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                          <FontAwesomeIcon icon={faInfo} />{" "}
                        </span>{" "}
                        Warning
                      </h3>
                      <button
                        onClick={closeModal}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                        data-modal-hide="default-modal"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />{" "}
                      </button>
                    </div>
                    {/* <!-- Modal body --> */}
                    <div className="p-8 py-0 space-y-6">
                      <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                        Mandatory information missing. Do you want to continue?
                      </p>
                    </div>
                    {/* <!-- Modal footer --> */}
                    <div className="flex items-end p-6 space-x-2 justify-end">
                      <button
                        onClick={closeModal}
                        data-modal-hide="default-modal"
                        type="button"
                        className="border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Cancel
                      </button>
                      <button
                        onClick={handleContinueSubmit}
                        data-modal-hide="default-modal"
                        type="button"
                        className="text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Continue
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default GeneralSection;
