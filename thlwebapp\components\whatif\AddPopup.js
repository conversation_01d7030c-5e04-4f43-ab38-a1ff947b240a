import React, { use<PERSON><PERSON>back, useContext, useEffect, useState } from "react";
import {
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogBody,
  DialogContent,
  Tooltip,
} from "@fluentui/react-components";
import { ModalContext } from "./providers/ModalProvider";
import {
  GRID_BE,
  GRID_PRICE,
  GRID_VOLUME,
  WHATIF_TYPE_BREAKEVEN_ID,
  WHATIF_TYPE_PRICE_ID,
  WHATIF_TYPE_PROMO_ID,
  WHATIF_TYPE_RR_VOLUME_ID,
  WHATIF_TYPE_SUPPLIR_ISSUE_ID,
} from "../../utils/whatif/utils/constants";
import BreakEvenChoices from "./addModal/BreakEvenChoices";
import PopupSelectedWeeks from "./addModal/PopupSelectedWeeks";
import PopupWhatIfDetails from "./addModal/PopupWhatIfDetails";
import findQuarter from "@/utils/whatif/utils/findQuarter";
import { getNextQuarterDataByPkey } from "@/utils/whatif/utils/getNextQuarterDataByPkey";
import Cookies from "js-cookie";
import { addNextQuarterByPkey } from "@/utils/whatif/utils/indexedDB";
import findFirstEmptyKey from "@/utils/whatif/utils/findFirstEmptyKey";
import { apiConfig } from "@/services/apiConfig";
//


const AddPopup = ({ userData, customerList, onCancel, currentYear }) => {
  const {
    currentData,
    modalConfig,
    setModalConfig,
    saveWhatifHandler,
    removeWhatifHandler,
    addLock,
    removeLocks,
  } = useContext(ModalContext);
  const [isValidToSave, setIsValidToSave] = useState(false);
  const [overlappingWeekArr, setOverlappingWeekArr] = useState([]);
  const [storedWeeks, setStoredWeeks] = useState([]);
  const [isConfirmConfirmationShown, setIsConfirmConfirmationShown] = useState(false);
  const [isFetchingNextQuarterData, setIsFetchingNextQuarterData] =
    useState(false);
      const [initialValue, setInitialValue] = useState({});
    
  const [disabelbutton, setdisabelbutton] = useState(true);
  const typePromo = modalConfig.whatIfs?.filter(
    (wi) => wi.type === WHATIF_TYPE_PROMO_ID
  );
  const typeSupplierIssues = modalConfig.whatIfs?.filter(
    (wi) => wi.type === WHATIF_TYPE_SUPPLIR_ISSUE_ID
  );
  const typeRrVolume = modalConfig.whatIfs?.filter(
    (wi) => wi.type === WHATIF_TYPE_RR_VOLUME_ID
  );
  const typeBreakEven = modalConfig.whatIfs?.filter(
    (wi) => wi.type === WHATIF_TYPE_BREAKEVEN_ID
  );
  const typePrice = modalConfig.whatIfs?.filter(
    (wi) => wi.type === WHATIF_TYPE_PRICE_ID
  );

  useEffect(() => {
    let selectedQuarter = [];
    if (typeof window !== "undefined") {
      let selectedQuarterString = localStorage.getItem("selectedQuarterString");
      selectedQuarter = selectedQuarterString?.split(",").map(Number); // Convert to array of numbers
    }
    const overlappingWeeks = [
      [13, 14], // Example overlap for Q2
      [26, 27], // Example overlap for Q3
      [39, 40], // Example overlap for Q4
    ];

    const weeksData = modalConfig.whatIfs[0].weeksData;

    const weekNumbers = weeksData.map((week) => week.weekNumber);

    const overlappingWeek = [];
    const storedWeeksArr = [];

    const quarterWeekRanges = {
      Q2: [14, 26], // Weeks for Quarter 2
      Q3: [27, 39], // Weeks for Quarter 3
      Q4: [40, 52], // Weeks for Quarter 4
    };

    overlappingWeeks.forEach((pair) => {
      const [first, second] = pair;
      let quarterKey;
      if (first === 13 || second === 14) {
        quarterKey = "Q2";
      } else if (first === 26 || second === 27) {
        quarterKey = "Q3";
      } else if (first === 39 || second === 40) {
        quarterKey = "Q4";
      }
      const quarterNumberMap = {
        Q1: 1,
        Q2: 2,
        Q3: 3,
        Q4: 4,
      };

      if (
        quarterKey &&
        selectedQuarter.includes(quarterNumberMap[quarterKey])
      ) {

        return; // Skip to the next iteration
      }
      if (weekNumbers.includes(first) && weekNumbers.includes(second)) {
        // Add the second week to overlappingWeek
        overlappingWeek.push(second);

        // Store the second week

        const foundWeek = weeksData.find((week) => week.weekNumber === second);
        if (foundWeek) {

          storedWeeksArr.push(foundWeek);
        }

        // Remove the second week from weeksData
        weeksData.forEach((week, index) => {
          if (week.weekNumber === second) {
            weeksData.splice(index, 1);
          }
        });

        // Determine which quarter to include weeks for based on the overlap
        let quarterKey;
        if (first === 13 || second === 14) {
          quarterKey = "Q2";
        } else if (first === 26 || second === 27) {
          quarterKey = "Q3";
        } else if (first === 39 || second === 40) {
          quarterKey = "Q4";
        }

        // Include weeks for the identified quarter
        if (quarterKey) {
          const [startWeek, endWeek] = quarterWeekRanges[quarterKey];
          for (let weekNum = startWeek; weekNum <= endWeek; weekNum++) {
            if (weekNumbers.includes(weekNum)) {
              const foundWeek = weeksData.find(
                (week) => week.weekNumber === weekNum
              );

              if (foundWeek) {

                storedWeeksArr.push(foundWeek);
              }


              // Remove the week from weeksData
              weeksData.forEach((week, index) => {
                if (week.weekNumber === weekNum) {
                  weeksData.splice(index, 1);
                }
              });
            }
          }
        }
      }
    });

    if (overlappingWeek.length > 0) {
      setOverlappingWeekArr(overlappingWeek);

      setStoredWeeks(storedWeeksArr);
    }
  }, []);

  const [popupData, setPopupData] = useState({
    whatIfType: WHATIF_TYPE_PROMO_ID,
    isEditMode: false,
    selectedWeeks: [],
    description: "",
    beStatusId: null,
  });

  const [removeExtra, setRemoveExtra] = useState({});

  const currentProduct = currentData.filter(
    (data) => data.pkey === modalConfig.selectedProductPkey
  );
  if (!currentProduct || currentProduct.length <= 0)
    return <div>No Product</div>; //return if no product to edit

  const allQuarters = [
    ...currentProduct[0].quarters.Q2,
    ...currentProduct[0].quarters.Q1,
    ...currentProduct[0].quarters.Q3,
    ...currentProduct[0].quarters.Q4,
    ...currentProduct[0].quarters.Q5,
  ];

  const [isRemoveConfirmationShown, setIsRemoveConfirmationShown] =
    useState(false);
  const [previousPromoValues, setPreviousPromoValues] = useState({});

  useEffect(() => {
    addLock();

    if (modalConfig.grid === GRID_VOLUME) {
      if (typeSupplierIssues.length > 0) {
        
        setPopupData((prev) => {
          return {
            ...popupData,
            whatIfType: WHATIF_TYPE_SUPPLIR_ISSUE_ID,
            isEditMode: true,
            selectedWeeks: typeSupplierIssues[0]?.weeksData,
            description: typeSupplierIssues[0]?.description,
          };
        });
      } else if (typePromo.length > 0) {
       

        setPopupData((prev) => {
          return {
            ...popupData,
            whatIfType: WHATIF_TYPE_PROMO_ID,
            isEditMode: !typePromo[0]?.isNew,
            selectedWeeks: typePromo[0]?.weeksData,
            description: typePromo[0]?.description,
          };
        });
      } else if (typeRrVolume.length > 0) {
    

        setPopupData((prev) => {
          return {
            ...popupData,
            whatIfType: WHATIF_TYPE_RR_VOLUME_ID,
            isEditMode: true,
            selectedWeeks: typeRrVolume[0]?.weeksData,
            description: typeRrVolume[0]?.description,
          };
        });
      }
    } else if (modalConfig.grid === GRID_BE) {
      setPopupData((prev) => {
        return {
          ...popupData,
          whatIfType: WHATIF_TYPE_BREAKEVEN_ID,
          isEditMode: typeBreakEven.length > 0,
          selectedWeeks: typeBreakEven[0]?.weeksData,
          description: typeBreakEven[0]?.description,
          beStatusId: typeBreakEven[0]?.beStatusId,
        };
      });
    } else if (modalConfig.grid === GRID_PRICE) {
      setPopupData((prev) => {
        return {
          ...popupData,
          whatIfType: WHATIF_TYPE_PRICE_ID,
          isEditMode: typePrice.length > 0,
          selectedWeeks: typePrice[0]?.weeksData,
          description: typePrice[0]?.description,
        };
      });
    }

    const existingWeeksData = {};

    modalConfig.whatIfs[0].weeksData.forEach((week) => {
      const quarter = findQuarter(week.weekNumber, currentProduct[0].quarters);
      existingWeeksData[week.weekNumber] = { quarter, current: week.current };
    });

    setPreviousPromoValues(existingWeeksData);

    return () => {
      removeLocks();
    };
  }, []);

  const saveDataHandler = async (isWip = false) => {
    // console.log("popupData",popupData);
    const isSaved = await saveWhatifHandler(
      popupData.whatIfType,
      popupData.selectedWeeks,
      popupData.description,
      popupData.beStatusId,
      isWip,
      modalConfig.existingWhatifId,
      previousPromoValues,
      currentProduct[0].quarters,
      removeExtra,
      storedWeeks,
      overlappingWeekArr,
      popupData?.selectedWeeks[0]?.fiscalYear,
    );
  };

  const compareValues = () => {
    // Start with valid as false, indicating no changes detected
    let isValid = false; 
  
    for (const week of popupData.selectedWeeks) {
      if(!week.isNew){
        return true;
      }
      const initialWeekData = initialValue[week.weekNumber];
      if (initialWeekData) {
        // Check if the current values are different from the initial values
        if (
          week.current.volume !== initialWeekData.current.volume ||
          week.current.be !== initialWeekData.current.be ||
          week.current.price !== initialWeekData.current.price
        ) {
          isValid = true; // Set isValid to true if any value is different
          break; // Exit the loop early since we found a change
        }
      }
    }
    return isValid; // Return isValid
  };

  const toggleRemoveConfirmationHandler = (choice) => {
    setIsRemoveConfirmationShown(choice);
  };
  const toggleConfirmConfirmationHandler = (choice) => {
    const isValidSave=compareValues();
    setIsValidToSave(isValidSave)
    // return;
    setIsConfirmConfirmationShown(choice);
  };
  
  const { setCurrentData } = useContext(ModalContext);


  const updatePopupData = async () => {
    setIsFetchingNextQuarterData(true);
    let nextFiscalYear = parseInt(currentYear.value, 10);

    let nextSessionData =
      (await getNextQuarterDataByPkey(
        modalConfig.selectedProductPkey,
        overlappingWeekArr[0] - 1,
        currentYear.value,
        modalConfig.whatIfs[0].whatif_id
      )) ?? [];

    setCurrentData((prev) => {
      const productIndex = prev.findIndex(
        (product) => product.pkey === modalConfig.selectedProductPkey
      );
      const nextQuarterKey = findFirstEmptyKey(prev[productIndex].quarters);

      if (!nextQuarterKey) return prev;

      prev[productIndex].quarters[nextQuarterKey] =
        nextSessionData.nextQuarterProductDataByWeek;
      addNextQuarterByPkey(
        prev[productIndex].pkey,
        nextQuarterKey,
        nextSessionData.nextQuarterProductDataByWeek
      );
      return prev;
    });


    setPopupData((prevState) => {
      // Merge `storedWeeks` into `selectedWeeks` first
      let mergedSelectedWeeks = [...prevState.selectedWeeks, ...storedWeeks];

      // Update `nextWeekHavingWhatif` after merging
      if (nextSessionData.whatifPromoWeeks.length === 0) {
        const lastWeekData =
          nextSessionData.nextQuarterProductDataByWeek.at(-1);

        if (lastWeekData && lastWeekData.week) {
          mergedSelectedWeeks = mergedSelectedWeeks.map((week) => ({
            ...week,
            nextWeekHavingWhatif: lastWeekData.week,
          }));
        }
      }

      // Return the updated state
      return {
        ...prevState,
        selectedWeeks: mergedSelectedWeeks,
      };
    });

    setOverlappingWeekArr([]);
    let allQuartersCombined = [
      ...allQuarters,
      ...nextSessionData.nextQuarterProductDataByWeek,
    ];
    let previousCombined = {};

    allQuartersCombined.map((data) => {
      previousCombined[`${data.week}-${nextFiscalYear}`] = {
        volume: !!data.data[0].value ? data.data[0].value : 0,
        be: !!data.data[1].value ? data.data[1].value : 0,
        price: !!data.data[2].value ? data.data[2].value : 0,
        sales: !!data.data[7].value ? data.data[7].value : 0,
        gp: !!data.data[3].value ? data.data[3].value : 0,
        gp_percent: !!data.data[4].value ? data.data[4].value : 0,
      };
    });

    setModalConfig((prev) => {
      return { ...prev, previousValues: previousCombined };
    });
    setIsFetchingNextQuarterData(false);
  };

  return (
    <div>
      <Dialog open={true}>
        <DialogSurface className="!max-w-[90%] 2xl:!max-w-[70%] !p-5">
          <DialogBody>
            <DialogTitle>
              <div>
                <div className="flex flex-row justify-between items-baseline">
                  <h3 className="w-1/5">Add New</h3>
                  {modalConfig.existingWhatifId && (
                    <div className="flex flex-1 justify-center">
                      <div className="nodata flex justify-center items-center py-2 px-4 text-sm mb-2">
                        BE/Price change already exits for this week(s). Adding
                        Promo OR Volume change will override BE/Price change.
                      </div>
                    </div>
                  )}
                </div>

                <hr className="border border-gray-200"></hr>
              </div>
            </DialogTitle>

            <DialogContent className="!max-h-[70vh]">
              <div className="flex flex-row gap-8 w-full">
                <PopupWhatIfDetails
                  modalConfig={modalConfig}
                  popupData={popupData}
                  setPopupData={setPopupData}
                  isRemoveConfirmationShown={isRemoveConfirmationShown}
                  overlappingWeek={overlappingWeekArr}
                />
                <div className="flex flex-col gap-4 w-3/4">
                  <PopupSelectedWeeks
                    popupData={popupData}
                    setPopupData={setPopupData}
                    modalConfig={modalConfig}
                    setModalConfig={setModalConfig}
                    isRemoveConfirmationShown={isRemoveConfirmationShown}
                    allQuarters={allQuarters}
                    setPreviousPromoValues={setPreviousPromoValues}
                    quarters={currentProduct[0].quarters}
                    setRemoveExtra={setRemoveExtra}
                    currentYear={currentYear}
                    overlappingWeek={overlappingWeekArr}
                    storedWeeks={storedWeeks}
                    updatePopupData={updatePopupData}
                    currentData={currentData}
                    isFetchingNextQuarterData={isFetchingNextQuarterData}
                    initialValue={initialValue}
                    setInitialValue={setInitialValue}
                  />
                  {popupData.whatIfType === WHATIF_TYPE_BREAKEVEN_ID && (
                    <BreakEvenChoices
                      beStatus={popupData.beStatusId}
                      isRemoveConfirmationShown={isRemoveConfirmationShown}
                      setBeStatus={(status) => {
                        setPopupData((prev) => {
                          return { ...prev, beStatusId: status };
                        });
                      }}
                    />
                  )}
                </div>
              </div>
            </DialogContent>
          </DialogBody>
          {!isRemoveConfirmationShown && !isConfirmConfirmationShown && (
            <div className="flex flex-col">
              <hr className="border border-gray-200 w-full mt-2"></hr>
              <div className="flex flex-row justify-between w-full pt-4">
                <div>
                  {modalConfig.whatIfs[0]?.whatif_id && (
                    <button
                      tabIndex="-1"
                      className="border bg-issue-status text-white rounded-lg px-5 py-2"
                      onClick={() => toggleRemoveConfirmationHandler(true)}
                    >
                      Remove
                    </button>
                  )}
                </div>

                <div className="flex flex-row gap-3">
                  <DialogTrigger disableButtonEnhancement>
                    <button
                      tabIndex="-1"
                      className="border border-skin-primary text-skin-primary bg-white rounded-lg px-5 py-2"
                      onClick={onCancel}
                    >
                      Cancel
                    </button>
                  </DialogTrigger>

                  <button
                    className="bg-confirm-status text-white rounded-lg px-5 py-2"
                    onClick={() => toggleConfirmConfirmationHandler(true)}
                    // disabled={disabelbutton}
                  >
                    {popupData.whatIfType === WHATIF_TYPE_PROMO_ID
                      ? "Confirm"
                      : "Save"}
                  </button>
                  {popupData.whatIfType === WHATIF_TYPE_PROMO_ID && (
                    <button
                      className="bg-wip-status text-black rounded-lg px-5 py-2"
                      onClick={() => {
                        saveDataHandler(true);
                      }}
                    >
                      WIP
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}
          {isRemoveConfirmationShown && (
            <div className="text-center">
              <hr className="border border-gray-200 w-full mt-2"></hr>
              <div className="flex flex-row justify-between items-center">
                <h3 className="font-bold text-xl p-4 pt-5">
                  Are you sure you want to remove the above promotion?
                </h3>
                <div className="flex flex-row gap-3 justify-center pt-4">
                  <DialogTrigger disableButtonEnhancement>
                    <button
                      className="border border-skin-primary text-skin-primary bg-white rounded-lg px-5 py-2 font-bold"
                      onClick={() => toggleRemoveConfirmationHandler(false)}
                    >
                      Cancel
                    </button>
                  </DialogTrigger>

                  <button
                    className="text-white bg-red-500 hover:bg-red-500 rounded-lg px-8 py-2 font-bold"
                    onClick={async () => {
                      await removeWhatifHandler(
                        popupData.selectedWeeks,
                        previousPromoValues,
                        currentProduct[0].quarters
                      );
                      toggleRemoveConfirmationHandler(false);
                    }}
                  >
                    Yes
                  </button>
                </div>
              </div>
            </div>
          )}
          {isConfirmConfirmationShown && (
            <div className="text-center">
              <hr className="border border-gray-200 w-full mt-2"></hr>
              <div className="flex flex-row justify-between items-center">
                {!isValidToSave && <h3 className="font-bold text-xl pt-4">You can't confirm a promotion without any changes</h3>}
                {isValidToSave && <h3 className="font-bold text-xl pt-4">
                  Confirming Promotion only for the time being. Are you sure
                  you want to confirm this Promotion?
                </h3>}
                <div className="flex flex-row gap-3 justify-center pt-4">
                  <DialogTrigger disableButtonEnhancement>
                    <button
                      className="border border-skin-primary text-skin-primary bg-white rounded-lg px-5 py-2 font-bold"
                      onClick={() => toggleConfirmConfirmationHandler(false)}
                    >
                      Cancel
                    </button>
                  </DialogTrigger>

                 {isValidToSave && <button
                    className="text-white bg-confirm-status hover:bg-confirm-status/80 rounded-lg px-9 py-2 font-bold"
                    onClick={() => {
                      saveDataHandler();
                    }}

                  >
                    Yes
                  </button>}
                </div>
              </div>
            </div>
          )}
        </DialogSurface>
      </Dialog>
    </div>
  );
};

export default AddPopup;
