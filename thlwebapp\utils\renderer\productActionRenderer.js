import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCopy,
  faPenToSquare,
  faXmark,
  faInfo,
  faFileExport,
  faEye,
} from "@fortawesome/free-solid-svg-icons";
import { Router, useRouter } from "next/router";
import { apiConfig } from "@/services/apiConfig";
import { useState, useEffect, Fragment } from "react";
import * as XLSX from "xlsx";
import exportExcelData from "../exportExcel";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { Dialog, Transition } from "@headlessui/react";
//
import { useMsal } from "@azure/msal-react";
import Cookies from "js-cookie";
import { logout } from "../secureStorage";

const productActionRenderer = (
  params, 
  userData,
  company,
  typeId,
  setIsLoading,
  isIssUser,
  isIssProcurementTeamUser,
  isIssAdmin,
  superAdmin
) => {
  const router = useRouter();
  const product_id = params.data.id;
  const data = params.data;
  const serverAddress = apiConfig.serverAddress;
  const [isCancelOpen, setIsCancelOpen] = useState(false);
  const [cancelledReasonapi, setCancelledReasonapi] = useState("");
  const [isValidCancelReason, setIsValidCancelReason] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const editProduct = () => {
    setIsEditing(true);
    setIsLoading(true);
    if (typeof window !== "undefined") {
      if (params && params?.data?.type == "FG") {
        router.push({
          pathname: `/finished-product-request/${product_id}/edit`,
        });
      } else if (params && params.data.type == "RM") {
        setIsLoading(true);

        router.push({
          pathname: `/raw-material-request/${product_id}/edit`,
        });
      } else if (params && params.data.type == "NV") {
        setIsLoading(true);

        router.push({
          pathname: `/variety/${product_id}/edit`,
        });
      } else if (params && params.data.type == "PK"){
        setIsLoading(true);
        router.push({
          pathname: `/packaging-form/${product_id}/edit`
        })
      }
    }
  };

  const handleCancelReason = (data) => {
    if (data) {
      setIsValidCancelReason(true);
    } else {
      setIsValidCancelReason(false);
    }
  };
  const trimInputText = (input) => {
    return input.trim();
  };

  const exportToExcel = async () => {
    if (data.status === "Submitted" || data.status === "Exported") {
      let userText3 = "";
      let markVariety = "";
      if (data.company == "dpsltd") {
        userText3 = "DPS";
        markVariety = data?.mark_variety_name;
      } else if (data.company == "efcltd") {
        userText3 = "OFF";
        markVariety = "RM" + " " + data?.mark_variety_name;
      } else if (data.company == "fpp-ltd") {
        userText3 = "FPP";
        markVariety = "RM" + " " + data?.mark_variety_name;
      } else {
        userText3 = "FLRS"; //TODO: remove this later
        markVariety = data?.mark_variety_name;
      }

      const filteredExportData = [
        [
          "Product Extract",
          {
            "User Boolean 1": "True",
            "Master Product Code": data?.master_product_code,
            "Commodity Code": data?.intrastat_commodity_code_id,
            "User Text 4": data?.userText4,
            "User Text 5": data?.userText5,
            "User Text 6": data?.userText6,
            "Intrastat weight mass": data?.outer_gross_weight,
            "Sub Product Code": data?.sub_product_code,
            "Mark/variety": markVariety,
            "Count or size": data?.count_or_size,
            "Sort Group Number": data?.group_id,
            "User Text 3": userText3,
            "Product Number": "",
            "Units in Outer": data?.units_in_outer,
            "Packs per pallet": data?.cases_per_pallet,
            "Sell packs per pallet": data?.cases_per_pallet,
            "Weight of outer": data?.outer_gross_weight,
            "Product distribution Point": "",
            Buyer: 1,
            "Temperature grade": data?.temperature_grade_id,
            "Temperature Grade": data?.temperature_grade_name,
            "Product Type": data?.product_type_id,
            "Product type": data?.product_type_name,
            Active: "True",
          },
        ],
      ];

      if (data.company == "efcltd" || data.company == "fpp-ltd") {
        filteredExportData.push([
          "ALTFIL Extract",
          {
            Active: "True",
            "Altfil record id": "",
            "Generic Code": userText3,
            "Alternate product number": "",
            "Alternate number": "",
            "Alternate bar code number": "",
            "Alternate description": data?.sub_product_code,
            "Alternate product Master product code": "",
            "Alternate product number Count or size": "",
            "Alternate product number Gross weight outer": "",
            "Alternate product number Mark/variety": "",
            "Alternate group": data?.group_id,
            "Alternate count or size": data?.units_in_outer,
            "Alternate prefix": "",
            "Inner product barcode": "",
            "Outer product barcode": "",
            "Alternate product number extension": "",
            "End Customer": "",
            Brand: "",
            "Display until days": "",
            "GTIN 14": "",
            "Calibre / Size": "",
            "Alternate product number Packs per pallet": "",
            "Inner stock keeping unit": "",
            "Stock keeping unit": "",
            "Customer product code": "",
            "Alternate use standard prefix (1=yes)": "1",
            "User text 1": "",
          },
        ]);
        // console.log(
        //   "filtered export data after creating new array",
        //   filteredExportData
        // );
      }

      const productEmailParagraph = `<p>User ${userData.name} submitted a Raw material request with request number ${params.data.request_no} 
          to ISS.
       
      </p>`;
      let productEmailCommentPlaceholder = `<p style='
      color: #32353e; margin: 0 0 10px 0; padding: 0;font-family: "HelveticaNeueLight", "HelveticaNeue-Light", "Helvetica Neue Light", "HelveticaNeue", "Helvetica Neue", "TeXGyreHerosRegular", "Helvetica", "Tahoma", "Geneva", "Arial", sans-serif; font-weight: 300;
        font-stretch: normal; font-size: 14px; line-height: 1.7; text-align: left;'>Comments: <i>${
          params.data.emailComment ? params.data.emailComment : "-"
        }</i></p>
      `;

      const export_response = await exportExcelData(
        filteredExportData,
        false,
        "",
        data.company,
        userData,
        "",
        params.data.originator_email,
        false,
        true,
        true,
        productEmailParagraph,
        productEmailCommentPlaceholder,
        params.data.request_no
      );
      // console.log("export_response", export_response);
      if (export_response === 401) {
        toast.error("Your session has expired. Please log in again.");
        setTimeout(async() => {
          await logout();
          const redirectUrl = `/login?redirect=${encodeURIComponent(
            window.location.pathname
          )}`;
          router.push(redirectUrl);
        }, 3000);
        return null;
      }
    } else {
      toast.error("Kindly Submit the Request to Export it.", {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: false,
        draggable: true,
        progress: undefined,
        theme: "light",
      });
      return;
    }
  };

  const cancelProduct = () => {
    setIsCancelOpen(true);
  };

  const closeCancelModal = () => {
    setIsCancelOpen(false);
  };

  const getProphetId = () => {
    switch (params.data.company) {
      case "dpsltd":
        return 1;
      case "efcltd":
        return 3;
      case "fpp-ltd":
        return 4;
      default:
        return 1;
    }
  };

  const saveModalData = () => {
    const prophetId = getProphetId();
    // return;
    if (!cancelledReasonapi) {
      setIsValidCancelReason(false);
      return;
    }
    try {
      fetch(`${serverAddress}products/product-update-status`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          status: 6,
          productId: product_id,
          updated_date: new Date().toISOString(),
          reason: cancelledReasonapi,
          request_no: params.data.request_no,
          type: params.data.type,
          cancelled_by: userData.email,
          cancelled_by_name: userData.name,
          cancelled_date: new Date().toISOString(),
          originator_email: params.data?.originator_email,
          current_action_id: params.data.action_id,
          prophetId: prophetId,
          code: params.data.code,
        }),
      })
        .then((res) => {
          if (res.status === 400) {
            toast.error(
              "There was an error with your request. Please check your data and try again."
            );
          }
          if (res.status === 200) {
            return res.json();
          } else if (res.status === 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(async() => {
              await logout();
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              router.push(redirectUrl);
            }, 3000);
          }
          return null;
        })
        .then((json) => {
          if (json) {
            setIsCancelOpen(false);
            if (params.data.type == "NV") {
              Cookies.set("PreviousPage", true);
            }
            window.location.reload();
          }
        });
    } catch (error) {
      console.error("Failed to cancel product by :", error);
    }
  };
  return (
    <>
      {/* <ToastContainer limit={1} /> */}
      <div className="flex flex-row gap-4 justify-start text-blue-500 pl-3">
        {params.data.status == "Prophet Setup Completed" ||
        params.data.status == "Prophet to Setup" ||
        params.data.status == "Cancelled" ? (
          <button title="View Request" onClick={editProduct}>
            <FontAwesomeIcon
              icon={faEye}
              size="lg"
              className="text-skin-primary"
            />
          </button>
        ) : (
          <>
            <button
              disabled={isEditing}
              title="Edit Request"
              onClick={editProduct}
              // className={`${userData.role == 1 ? '' : 'curson-not-allowed pointer-events-none'}`}
            >
              <FontAwesomeIcon
                icon={faPenToSquare}
                size="lg"
                className="text-skin-primary"
              />
            </button>
            {
              params.data.status != "Setup Completed" &&
              params.data.status != "Submitted" &&
              params.data.status != "Cancelled" &&
              ((typeId === 4 && //if type id 4 
                (!isIssUser || isIssProcurementTeamUser || isIssAdmin || superAdmin)) || //then
                (typeId !== 4 && !isIssUser)) && ( //else
                <button
                  onClick={cancelProduct}
                  title="Cancel Request"
                  className="flex items-center"
                >
                  <FontAwesomeIcon
                    icon={faXmark}
                    size="sm"
                    className="border rounded-sm border-skin-primary text-skin-primary m-0 w-[15px] h-[15px]"
                  />
                </button>
              )}
            {params.data.status == "Submitted" && (
              <button
                onClick={() => exportToExcel(params)}
                title="Export Request"
              >
                <FontAwesomeIcon
                  icon={faFileExport}
                  size="lg"
                  className="cursor-pointer text-skin-primary"
                />
              </button>
            )}
          </>
        )}
      </div>

      <Transition appear show={isCancelOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeCancelModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                  {/* <!-- Modal content --> */}
                  <div className="relative bg-white rounded-lg shadow">
                    {/* <!-- Modal header --> */}
                    <div className="flex items-start justify-between p-8 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                        <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                          <FontAwesomeIcon icon={faInfo} />{" "}
                        </span>{" "}
                        Cancellation Reason
                      </h3>
                      <button
                        onClick={closeCancelModal}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                        data-modal-hide="default-modal"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />{" "}
                      </button>
                    </div>
                    {/* <!-- Modal body --> */}
                    <div className="p-8 py-0 space-y-6">
                      {params.data.type == "NV" ? (
                        <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                          Enter Variety Cancellation Reason.
                        </p>
                      ) : (
                        <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                          Enter Product Cancellation Reason.
                        </p>
                      )}
                      <textarea
                        className="flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2"
                        rows="8"
                        value={cancelledReasonapi}
                        onChange={(e) => {
                          setCancelledReasonapi(e.target.value),
                            handleCancelReason(e.target.value);
                        }}
                        onBlur={(e) => {
                          const trimmedValue = trimInputText(e.target.value);
                          setCancelledReasonapi(trimmedValue);
                          handleCancelReason(trimmedValue);
                        }}
                        // disabled={(e) => {e.target.value == ""}}
                        placeholder="Provide reason for cancellation..."
                        maxlength="500"
                      ></textarea>
                      {!isValidCancelReason && (
                        <span className="text-red-500">
                          Please Provide reason for cancellation
                        </span>
                      )}
                    </div>
                    {/* <!-- Modal footer --> */}
                    <div className="flex items-end p-6 space-x-2 justify-end">
                      <button
                        onClick={saveModalData}
                        data-modal-hide="default-modal"
                        type="button"
                        className=" bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md text-white mr-1 px-6 py-2 text-center "
                      >
                        Cancel Request
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default productActionRenderer;
