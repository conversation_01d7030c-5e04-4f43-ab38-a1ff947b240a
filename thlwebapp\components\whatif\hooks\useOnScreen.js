import { useState, useEffect } from 'react';

function useOnScreen(ref, rootMargin = '100px 0px', once = true) {
  const [isIntersecting, setIntersecting] = useState(false);
  useEffect(() => {
    if (!('IntersectionObserver' in window)) {
      console.warn('IntersectionObserver is not supported in this browser.');
      setIntersecting(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIntersecting(true);
          if (once) {
            observer.unobserve(entry.target);
          }
        }
      },
      {
        rootMargin,
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [ref, rootMargin, once]);

  return isIntersecting;
}

export default useOnScreen;
