async function getClculatedFinancials(price = 0, volume = 0, be = 0, caseSize) {
  const sales = volume * caseSize * price;
  const netSales = volume * caseSize * be;
  const grossProfit = sales - netSales;

  // Handle division by zero for gross profit percentage calculation
  let grossProfitPercentage = 0;
  if (sales !== 0) {
    grossProfitPercentage = (grossProfit / sales) * 100;
  }

  // Round all values to 2 decimal places
  const roundedSales = parseFloat(sales);
  const roundedGrossProfit = parseFloat(grossProfit);
  const roundedGrossProfitPercentage = parseFloat(grossProfitPercentage);
  return [roundedSales, roundedGrossProfit, roundedGrossProfitPercentage]; //return Sales, GP, GP%
}

export { getClculatedFinancials };
