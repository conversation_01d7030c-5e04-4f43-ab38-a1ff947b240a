BEGIN TRANSACTION;

<PERSON><PERSON><PERSON><PERSON> @last_request_number INT = 0;
 
SELECT @last_request_number = COALESCE(request_no, 0) 
FROM product_request_no
WHERE company = @company AND product_type = 3;
 
DECLARE @padded_request_number NVARCHAR(6);
SET @padded_request_number = RIGHT('000000' + CAST(@last_request_number + 1 AS VARCHAR), 6);

INSERT INTO product_request_no (
    [company],
    [request_no],
    [product_type]
)
VALUES (
    @company,
    @padded_request_number ,
    3
);

DECLARE @generatedRequestNo INT = SCOPE_IDENTITY();

-- Insert into product_nvariety
INSERT INTO product_nvariety (
    request_no,
    [type],
    code,
    [description],
    master_product_code
)
VALUES (
    @generatedRequestNo,
    3,
    @code,
    @description,
    @masterProductCode
);



DECLARE @varietyId INT = SCOPE_IDENTITY();

-- Insert into product_nvariety_status
INSERT INTO product_nvariety_status (
    variety_id,
    action_id,
    actioned_by_email,
    actioned_by_name,
    comment
)
VALUES (
    @varietyId,
    @actionId,
    @actionedByEmail,
    @actionedByName,
    @comment
);

COMMIT TRANSACTION;
