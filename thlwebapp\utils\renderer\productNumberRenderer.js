import { useEffect, useState, useRef } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { apiConfig } from "@/services/apiConfig";


const productNumberRenderer = (params) => {
    const [isChecked, setIsChecked] = useState(false);
    const gridRef = useRef();

    useEffect(() => {
        params.setRadioSelect(isChecked)
    })
   
    const handleProductClicked = (value) => {
        const saveProductNumber = [{
            product_number: params.data.product_number,
            description:params.data.description,
            brand: params.data.brand,
            end_customer: params.data.end_customer,
        }]
        setIsChecked(true)
        params.setProcurementList(saveProductNumber)
    }       
    
    return (
        <div className="flex flex-row product_number">
            <input 
                type="radio" 
                className="ml-3 mr-3" 
                name="product_number" 
                onChange={handleProductClicked} 
                checked={isChecked} 
                value={params.data.product_number}/>
            <p>{params.data.product_number}</p>
        </div>
    );
}

export default productNumberRenderer;