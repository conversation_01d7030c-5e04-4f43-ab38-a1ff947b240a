const { createLogger, format, transports } = require("winston");
const Transport = require("winston-transport");
const config = require("../config");
const sql = require("mssql");

const customFormat = format.combine(
  format.timestamp(),
  format.printf((info) => {
    return `${info.timestamp} - [${info.level.toUpperCase()}] - ${
      info.message
    }`;
  })
);

const insertAuditTrailQuery = `
  INSERT INTO dbo.audit_logs (item_id, date, username, description, type,module_id)
  VALUES (@item_id, GETDATE(), @username, @description, @type,@module_id)
`;

class SqlTransport extends Transport {
  constructor(options) {
    super(options);
  }

  async log(info, callback) {
    try {
      const { item_id, date, username, description, type, module_id } =
        info.message;
      console.log("logger info:", info.message);
      // Create a connection pool
      const pool = await sql.connect(config.sql);
      // Execute the insert query
      await pool
        .request()
        .input("item_Id", sql.Int, item_id)
        .input("date", sql.DateTime, date)
        .input("username", sql.VarChar, username)
        .input("description", sql.Text, description)
        .input("type", sql.VarChar, type)
        .input("module_id", sql.Int, module_id)
        .query(insertAuditTrailQuery);

      // Emit the "logged" event
      this.emit("logged", info);
    } catch (err) {
      console.error("Error logging message:", err);
      // Emit the "error" event if an error occurs
      this.emit("error", err);
    } finally {
      // Close the SQL connection

      callback();
    }
  }
}

// Handle SQL errors
sql.on("error", (err) => {});

const logger = createLogger({
  level: "debug",
  format: customFormat,
  transports: [new transports.Console({ level: "silly" }), new SqlTransport()],
});

module.exports = logger;
