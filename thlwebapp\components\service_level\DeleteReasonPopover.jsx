import React, {useState} from "react";
import {
  make<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>over,
  Popover<PERSON>ur<PERSON>,
  PopoverTrigger,
} from "@fluentui/react-components";
import Link from "next/link";

const DeleteReasonPopover = ({
  setDeleteId,
  setDeleteReason,
  setIsDeleteTrue,
  id,
}) => {
  const [isOpen, setIsOpen] = useState(false); // State to control Popover open/close

  const handleReasonChange = (e) => {
    const reason = e.target.value;
    setDeleteReason(reason.trim());
  };
  return (
    <div>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger disableButtonEnhancement>
          {/* <Button>Popover trigger</Button> */}
            <svg
            fill="currentcolor"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 448 512"
              className="w-5 h-5 fill !text-skin-primary"
            >
              <path d="M170.5 51.6L151.5 80l145 0-19-28.4c-1.5-2.2-4-3.6-6.7-3.6l-93.7 0c-2.7 0-5.2 1.3-6.7 3.6zm147-26.6L354.2 80 368 80l48 0 8 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-8 0 0 304c0 44.2-35.8 80-80 80l-224 0c-44.2 0-80-35.8-80-80l0-304-8 0c-13.3 0-24-10.7-24-24S10.7 80 24 80l8 0 48 0 13.8 0 36.7-55.1C140.9 9.4 158.4 0 177.1 0l93.7 0c18.7 0 36.2 9.4 46.6 24.9zM80 128l0 304c0 17.7 14.3 32 32 32l224 0c17.7 0 32-14.3 32-32l0-304L80 128zm80 64l0 208c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-208c0-8.8 7.2-16 16-16s16 7.2 16 16zm80 0l0 208c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-208c0-8.8 7.2-16 16-16s16 7.2 16 16zm80 0l0 208c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-208c0-8.8 7.2-16 16-16s16 7.2 16 16z" />
            </svg>
        </PopoverTrigger>

        <PopoverSurface tabIndex={-1}>
          <div className="p-2 flex flex-col gap-3 justify-center">
            <h3 className="text-center pt-4 text-lg font-bold">
              Are you sure you want to
              <br />
              delete the reason?
            </h3>
            {/* <div className="w-full flex flex-col">
              <label htmlFor="customer" className="text-gray-500">
                If yes, enter reason for deletion
              </label>
              <textarea
                className="px-2 2xl:px-3 border rounded-md w-full"
                id="reason"
                rows="3"
                onChange={handleReasonChange}
              ></textarea>
            </div> */}
            <div className="flex gap-3 justify-center">
              <button
                className="border border-skin-primary text-skin-primary bg-white rounded-lg px-5 py-2"
                onClick={() => {
                  setDeleteId("");
                  setIsDeleteTrue(false);
                  setIsOpen(false);
                }}
              >
                No
              </button>
              <button
                className="bg-skin-primary text-white rounded-lg px-6 py-2 font-bold"
                onClick={() => {
                  setDeleteId(id);
                  setIsDeleteTrue(true);
                  setIsOpen(false);
                }}
              >
                Yes
              </button>
            </div>
          </div>
        </PopoverSurface>
      </Popover>
    </div>
  );
};

export default DeleteReasonPopover;
