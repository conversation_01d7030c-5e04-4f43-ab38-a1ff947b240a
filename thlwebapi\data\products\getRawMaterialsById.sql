SELECT
  p.id,
  p.originator,
  p.originator_email,
  p.email_comment,
  pt.type AS type_name,
  pt.id AS type_id,
  p.product_type AS product_type,
  mp.name AS product_name,
  mp.id AS master_product_id,
  mp.is_new AS mp_is_new,
  mp.code AS master_product_code,
  pg.group_name AS group_name,
  pg.is_new AS group_is_new,
  pg.group_id As group_id,
  b.name AS brand_name,
  b.id AS brand_id,
  b.is_new AS b_is_new,
  b.code AS brand_code,
  cs.name AS caliber_size_name,
  cs.id AS caliber_size_id,
  cs.is_new AS cs_is_new,
  cs.code AS caliber_size_code,
  ec.name AS end_customer_name,
  ec.id AS end_customer_id,
  ec.is_new AS ec_is_new,
  ec.code AS ec_code,
  v.description,
  v.id AS variety_id,
  v.is_new AS v_is_new,
  v.code AS v_code,
  p.product_description,
  p.suppliers_description,
  p.count_or_size,
  p.units_in_outer,
  p.cases_per_pallet,
  p.outer_net_weight,
  p.outer_gross_weight,
  p.sub_product_code,
  p.temperature_grade,
  p.class_required,
  p.intrastat_commodity_code,
  p.company_name,
  p.organic_certificate,
  p.is_classified_allergic_fsa14,
  p.coo,
  coc.name as coo_name,
  coc.id as coc_id,
  coc.is_new as coc_is_new,
  coc.code as coc_code,
  p.reason as reason_id,
  r.reason as reason_type,
  tg.name as temperature_grade_name,
  tg.id as temperature_grade_id,
  icc.name as intrastat_commodity_code_name,
  icc.code as intrastat_commodity_code_id,
  icc.is_new as intrastat_commodity_code_is_new,
  icc.User_text_4 as intrastat_commodity_code_User_text_4,
  icc.User_text_5 as intrastat_commodity_code_User_text_5,
  icc.User_text_6 as intrastat_commodity_code_User_text_6,
  p.requestor,
  mv.id AS mark_variety_id,
  mv.name AS mark_variety_name,
  mv.is_new AS mark_variety_is_new,
  mv.product_type AS mark_variety_product_type,
  p.request_no,
  p.delivery_date,
  p.submitted_to_iss,
  p.status,
  p.cancelled_date,
  p.cancelled_reason,
  p.cancelled_by,
  p.sort_group
FROM [dbo].[products] p
LEFT JOIN product_request_type pt ON p.type = pt.id
LEFT JOIN master_products mp ON p.product_code = mp.id
LEFT JOIN brands b ON p.brand = b.id
LEFT JOIN caliber_size cs ON p.caliber_size = cs.id
LEFT JOIN end_customers ec ON p.end_customer = ec.id
LEFT JOIN product_nvariety v ON p.variety = v.id
LEFT JOIN mark_variety mv ON p.mark_variaty = mv.id
LEFT JOIN reason r ON p.reason = r.id
LEFT JOIN temperature_grade tg ON p.temperature_grade = tg.id
LEFT JOIN commodity_codes icc ON p.intrastat_commodity_code = icc.id
LEFT JOIN country_of_origin coc ON p.coo = coc.id
LEFT JOIN product_groups pg ON pg.group_id = p.group_id AND pg.type = p.type
WHERE p.id=@id
