"use strict";

const logData = require("../data/logs");
const logger = require("../utils/logger");
const verifyToken = require("../auth");

const loginLog = async (req, res, next) => {
    const {userData,company} = req.body
    console.log("name,company log",userData?.name,company)
  try {
    //if (await verifyToken(req, res)) {
      logger.info({
        username: userData?.name,
        type: "success",
        description: `User from ${company?company:"THL"} with email ${userData?.username} loggedin successfully`,
      });
      res.status(200).send("Loggedin successfully");
    // } else {
    //   res.status(401).send("Invalid Token");
    // }
  } catch (error) {
    logger.error({
      username: userData?.name,
      type: "error",
      description: `User with email ${userData?.username} not loggedin successfully`,
    });
    res.status(400).send(error.message);
  }
};

const getLogs = async (req, res, next)  => {
  try {
    //if (await verifyToken(req, res)) {
      const logs = await logData.getLogs();
      const modules=await logData.getModules();
      const responseData={
        logs,modules
      }
      res.send(responseData);
    // } else {
    //   res.status(401).send("Invalid Token");
    // }
  } catch (error) {
    logger.error({
      username: req?.user?.name,
      type: "error",
      description: `Error fetching Logs for user with email ${req?.user?.preferred_username}`,
    });
    res.status(400).send(error.message);
  }
}

module.exports = {
    loginLog,
    getLogs
  };