DECLARE @CurrentQuarter INT;

DECLARE @currentWeek INT;

DECLARE @currentStartWeek DATE;

DECLARE @CurrentFinancialYear INT;

DECLARE @CurrentDate DATE;

SET
    @CurrentFinancialYear = CASE
        WHEN MONTH(GETDATE()) >= 10 THEN YEAR(GETDATE()) + 1
        ELSE YEAR(GETDATE())
    END;

SET
    @CurrentDate = CASE
        WHEN @financialYear = @CurrentFinancialYear THEN GETDATE() -- Use current date if year is current
        ELSE CONVERT(
            DATE,
            CAST(@financialYear - 1 AS CHAR(4)) + '1001',
            112
        ) -- 1 Oct of the input year if it's a past year
    END;

SELECT
    @CurrentQuarter = fiscalquarter,
    @currentWeek = fiscalweek,
    @currentStartWeek = startweek
FROM
    FLR_DEV_TEST_off_BI_lookup.dbo.off_cal_start_end_week
WHERE
    calendar_name = 'OFF Financial Calendar'
    AND fiscalyear = @financialYear
    AND @CurrentDate BETWEEN startweek
    AND endweek;

	select @currentWeek as currentWeek;