BEGIN TRY
    BEGIN TRANSACTION;

    -- A. Insert the main product details
    INSERT INTO [Iss_ppe_consumables_portal].[dbo].[products] (
        [name],
        [product_code],
        [type_id],
        [size_required],
        [size_label],
        [quantity],
        [comments],
        [is_active],
        [name_printable]
    )
    VALUES (
        @ProductName,
        @ProductCode,
        @TypeID,
        @IsSizeRequired,
        @SizeLabel,
        @PackageQuantity,
        @Comments,
        1,
        @namePrintable
    );

    -- B. Get the ID of the newly created product
    DECLARE @NewProductID INT = SCOPE_IDENTITY();

    -- C. Handle sizes if required
    IF (@IsSizeRequired = 1)
BEGIN
    -- Example input: @NewSizeNames = 'S, M, L'
    ;WITH NewSizes AS (
        SELECT DISTINCT
            NULLIF(LTRIM(RTRIM(value)), '') AS size_label
        FROM STRING_SPLIT(@NewSizeNames, ',')
        WHERE NULLIF(LTRIM(RTRIM(value)), '') IS NOT NULL
    )
    -- 1) Insert only missing sizes
    INSERT INTO [Iss_ppe_consumables_portal].[dbo].[product_sizes] (size_label, is_active, created_at)
    SELECT ns.size_label, 1, GETDATE()
    FROM NewSizes ns
    LEFT JOIN [Iss_ppe_consumables_portal].[dbo].[product_sizes] ps
        ON ps.size_label = ns.size_label
    WHERE ps.id IS NULL;

    -- 2) Link product to all requested sizes (existing + newly inserted)
    INSERT INTO [Iss_ppe_consumables_portal].[dbo].[product_available_sizes] (product_id, size_id, is_active)
    SELECT @NewProductID, ps.id, 1
    FROM (
        SELECT size_label FROM NewSizes
    ) ns
    JOIN [Iss_ppe_consumables_portal].[dbo].[product_sizes] ps
        ON ps.size_label = ns.size_label
    LEFT JOIN [Iss_ppe_consumables_portal].[dbo].[product_available_sizes] pas
        ON pas.product_id = @NewProductID
       AND pas.size_id   = ps.id
    WHERE pas.id IS NULL; -- avoid duplicate link rows
END

    COMMIT TRANSACTION;

    -- Return success message with the new ID
    PRINT 'Product created successfully!';
    PRINT 'New Product ID: ' + CAST(@NewProductID AS VARCHAR);
    SELECT @NewProductID AS NewProductID, 'Success' AS Status;

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0
        ROLLBACK TRANSACTION;

    PRINT 'Error occurred: ' + ERROR_MESSAGE();
    SELECT -1 AS NewProductID, 'Failed: ' + ERROR_MESSAGE() AS Status;
END CATCH

