import React, { useCallback, useEffect, useRef, useState } from "react";
import debounce from "lodash/debounce";
import { apiConfig } from "@/services/apiConfig";
import { Oval } from "react-loader-spinner";
import Cookies from "js-cookie";

const DebouncedAutocompleteDistribution = ({
  onChange,
  distribution,
  supplierId,
  setDistributionPoint,
  isValidDistribution,
  setIsValidDistribution,
  distributionErrorMessage,
  disabled,
}) => {
  const serverAddress = apiConfig.serverAddress;
  const [inputValue, setInputValue] = useState(distribution);
  const [filteredItems, setFilteredItems] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const alphaNumericRegex = /^([a-zA-Z0-9 _&'-.]+)$/;
  const dropdownRef = useRef(null);
  const handleSelect = (value, flag) => {
    // setInputValue(value);
    setDistributionPoint(value);
    setIsValidDistribution(true);
    setFilteredItems([]); // Clear the filtered items
  };
  const handleClickOutside = (event) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setFilteredItems([]);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  const handleBlur = () => {
    setFilteredItems([]);
  };

  const filterItems = useCallback((searchString) => {
    let prophetId = Cookies.get("prophets");

    fetch(
      `${serverAddress}suppliers/get-filtered-distributions/${searchString.trim()}`,
      {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ prophetsIds: parseInt(prophetId), supplierId }),
      }
    )
      .then((res) => {
        if(res.status === 401 || res.status==400){
          return;
        }
        if (res.status === 200) {
          return res.json();
        }
        return Promise.reject(res);
      })
      .then((data) => {
        if (data) {
          const filteredData = data.map((item) => ({
            name: item.name,
            prophet_id: item.prophet_id,
            supplier_id: item.supplier_id,
            from_dp: item.from_dp,
            direct_dp: item.direct_dp,
            distribution_point_id: item.distribution_point_id,
            id: item.id,
          }));
          setFilteredItems(filteredData);
        }

        setIsLoading(false);
      })
      .catch((error) => {
        console.log(error);
        setIsLoading(false);
      });
  }, []);

  // Debounce the filtering function
  const debouncedFilterItems = useCallback(debounce(filterItems, 500), [
    filterItems,
  ]);

  useEffect(() => {
    return () => {
      // Cleanup the debouncedFilterItems function on component unmount
      debouncedFilterItems.cancel();
    };
  }, [debouncedFilterItems]);

  const handleInputChange = (distribution) => {
    onChange(distribution);
    setInputValue(distribution);
    if (distribution.length > 50 || !alphaNumericRegex.test(distribution)) {
      setIsValidDistribution(false);
      return false;
    } else {
      setIsValidDistribution(true);
    }
    if (distribution.length <= 0) {
      debouncedFilterItems.cancel();
      setIsLoading(false);
      // setFilteredItems(() => []);
      setFilteredItems([]);
    } else {
      // Debounce the filtering logic
      setIsLoading(true);
      debouncedFilterItems(distribution);
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <input
        type="text"
        className={`w-full px-2 2xl:px-3 border searchbar rounded-md ${
          isValidDistribution ? "border-light-gray" : "border-bright-red"
        }`}
        maxLength={50}
        value={distribution}
        onChange={(e) => handleInputChange(e.target.value)}
        disabled={disabled}
        style={{ textTransform: "capitalize" }}
      />
      <br></br>
      {!isValidDistribution && (
        <span className="text-red-500">{distributionErrorMessage}</span>
      )}
      {isLoading && (
        <ul
          className="absolute border p-1 w-2/3 z-50"
          style={{ backgroundColor: "white", borderRadius: "4px" }}
        >
          <li>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                height: "20px",
                width: "20px",
              }}
            >
              <Oval
                color="#002D73"
                height={10}
                width={10}
                visible={true}
                ariaLabel="oval-loading"
                secondaryColor="#0066FF"
                strokeWidth={2}
                strokeWidthSecondary={2}
              />
            </div>
          </li>
        </ul>
      )}
      {!isLoading && filteredItems && filteredItems.length > 0 && (
        <ul
          className="absolute border p-1 w-full z-10 bg-white"
          style={{
            backgroundColor: "white",
            borderRadius: "4px",
            height: "200px",
            overflow: "auto",
          }}
        >
          {filteredItems.map((item, key) => {
            let prophetName;
            switch (item.prophet_id) {
              case 1:
                prophetName = "dps";
                break;
              case 2:
                prophetName = "dps-ms";
                break;
              case 3:
                prophetName = "efc";
                break;
              case 4:
                prophetName = "fpp";
                break;
              case 5:
                prophetName = "ISS";
                break;
              default:
                prophetName = "unknown";
            }

            return (
              <li
                key={key}
                onBlur={handleBlur}
                onClick={() => handleSelect(item.name, true)}
                className={`hover:bg-red-500 cursor-pointer ${
                  key < filteredItems.length - 1
                    ? "border-b border-gray-100 py-1"
                    : ""
                }`}
              >
                {`${item.from_dp}-${item.name} (${prophetName})`}
              </li>
            );
          })}
        </ul>
      )}
    </div>
  );
};

export default DebouncedAutocompleteDistribution;
