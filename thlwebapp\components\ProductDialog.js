import {
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogBody,
  DialogActions,
  DialogContent,
  Button,
} from "@fluentui/react-components";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { useEffect } from "react";

const RequestDialoge = ({
  isOpen,
  onClose,
  handleFormType,
  selectedRequestType,
  handleRequestType,
  isIssUser,
  isIssProcurmentUser,
  admin=0,
  superAdmin=0,
}) => {
  useEffect(() => {
  if (isIssUser) {
    handleRequestType(4);
  }
}, [isIssUser]);
  return (
    <FluentProvider
      theme={webLightTheme}
      className="!bg-transparent"
      style={{ fontFamily: "poppinsregular" }}
    >
      <Dialog>
        <DialogTrigger disableButtonEnhancement>
          <button className="ml-2 px-3 py-1 p-[6px] border rounded-md bg-skin-primary text-white text-sm cursor-pointer"
          // onClick={() => handleRequestType(selectedRequestType)}
          disabled={!superAdmin && (isIssUser && (!isIssProcurmentUser || admin))}> 
            {/* && admin}> */}
            Add Request
          </button>
        </DialogTrigger>
        <DialogSurface>
          <DialogBody>
            <DialogTitle>
              <div>
                <div className="flex flex-row justify-between items-baseline">
                  <h3 className="w-full">Request Type</h3>
                </div>
                <hr className="border border-gray-200"></hr>
              </div>
            </DialogTitle>
            <DialogContent className="flex flex-col">
              <label className="pt-6">
                Select the request type to proceed.
              </label>
              <div className="flex gap-6 w-auto mb-4 py-3">
                <div className={`flex gap-3 items-center border border-light-gray rounded-md px-3 py-1 ${
                      superAdmin ? "" : isIssUser ?  "variety-disabled-block" : ""
                  }`}>
                  <input
                    type="radio"
                    name="requesttype"
                    value="rawMaterialRequest"
                    id="raw-material"
                    className={`border rounded-md ${superAdmin ? "cursor-pointer" : isIssUser ?  "" : "cursor-pointer"} w-5 h-5`}
                    checked={selectedRequestType === 1}
                    disabled={isIssUser && !superAdmin}
                    onChange={
                      () => handleRequestType(1)
                    }                  
                    />
                  <label
                    htmlFor="raw-material"
                    className="font-bold cursor-pointer"
                  >
                    Raw Material
                  </label>
                </div>

                {process.env.NEXT_PUBLIC_AVAILABLE_MODULES.split(",").includes("newVariety") && (<div
                  className={`flex gap-3 items-center border border-light-gray rounded-md px-3 py-1 ${
                    superAdmin ? "" : isIssUser ?  "variety-disabled-block" : ""
                  }`}
                >
                  <input
                    type="radio"
                    name="requesttype"
                    value="newVarietyRequest"
                    id="new-variety"
                    // disabled={isIssUser && superAdmin}
                    className={`border rounded-md ${superAdmin ? "cursor-pointer" : isIssUser ?  "" : "cursor-pointer"} h-5 w-5`}
                    // checked={isIssUser && superAdmin ? false : selectedRequestType === "newVarietyRequest"}
                    // onChange={() => handleRequestType(isIssUser && superAdmin ? "packagingform" : "newVarietyRequest")}
                     checked={selectedRequestType === 3}
                      disabled={isIssUser && !superAdmin}
                      onChange={() => handleRequestType(3)}
                  />
                  <label
                    htmlFor="new-variety"
                    className="font-bold cursor-pointer"
                  >
                    New Variety
                  </label>
                </div>)}
                {(isIssProcurmentUser || superAdmin) && process.env.NEXT_PUBLIC_AVAILABLE_MODULES.split(",").includes("packaging") && <div
                  className={`flex gap-3 items-center border border-light-gray rounded-md px-3 py-1 ${
                    superAdmin ? "" : (isIssUser && ( isIssProcurmentUser)) ? "" : "variety-disabled-block"
                  }`}
                >
                  <input
                    type="radio"
                    name="requesttype"
                    value="packagingform"
                    id="packaging"
                    disabled={!(superAdmin || (isIssUser && isIssProcurmentUser))}
                    className={`border rounded-md ${superAdmin ? "cursor-pointer" : isIssUser ?  "" : "cursor-pointer"} h-5 w-5`}
                    checked={
                      selectedRequestType === 4
                    }
                    onChange={() => handleRequestType(4)}
                  />
                  <label
                    htmlFor="packaging"
                    className="font-bold cursor-pointer"
                  >
                    Packaging
                  </label>
                </div>}

                {/* <div className="flex gap-3 items-center ">
                    <input
                        type="radio"
                        name="requesttype"
                        value="finishedProductRequest"
                        id="finished-product"
                        className={`border rounded-md cursor-pointer`}
                        checked={selectedRequestType === "finishedProductRequest"}
                        onChange={() => handleRequestType("finishedProductRequest")}
                    />
                    <label htmlFor="finished-product" className="font-bold">
                        Finished Product
                    </label>
                </div> */}
                        </div>

                    </DialogContent>
                    <DialogActions>
                        <DialogTrigger disableButtonEnhancement>
                            <button className="ml-2 px-3 py-1 p-[6px] border rounded-md border-skin-primary text-skin-primary cursor-pointer ">Cancel</button>
                        </DialogTrigger>
                        <button
                        onClick={handleFormType}
                        className="ml-2 px-6 py-1 p-[6px] border rounded-md bg-skin-primary text-white cursor-pointer ">Continue</button>
                    </DialogActions>
                </DialogBody>
            </DialogSurface>
        </Dialog>
        </FluentProvider>
    )
}

export default RequestDialoge;
