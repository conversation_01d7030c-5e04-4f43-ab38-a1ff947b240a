"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import dpslogo from "../public/images/dps-logo.png";
import efc_logo from "../public/images/efc_logo.jpg";
import dps_ms_logo from "../public/images/dps_ms_logo.png";
import fpp_logo from "../public/images/fpp_logo.png";
import iss_logo from "../public/images/iss_logo.jpg";

import logoutIcon from "../public/images/logout-icon.png";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { useMsal } from "@azure/msal-react";
import { Oval } from "react-loader-spinner";
import SideBarLinks from "./SideBarLinks";
import { useSecureAuth } from "@/utils/auth/useSecureAuth";

export default function Sidebar({ userData: propUserData }) {
  const [userData, setUserData] = useState({});
  const [company, setCompany] = useState("");
  const [ADCompany, setADCompany] = useState("");
  const { secureLogout } = useSecureAuth();
  
  useEffect(() => {
    if (!propUserData) {
      // If no userData provided, redirect to login
      window.location.href = '/login';
      return;
    }
    
    setUserData(propUserData);
    setCompany(propUserData.company);
    setADCompany(propUserData.ADCompanyName || propUserData.companyName);
  }, [propUserData]);

  const { instance } = useMsal();
  const router = useRouter();
  const currentPathname = usePathname();

  const getLogo = (company) => {
    if (!company) return dpslogo; // Default fallback
    switch (company) {
      case "dpsltd":
        return dpslogo;
      case "DPS MS":
        return dps_ms_logo;
      case "efcltd":
        return efc_logo;
      case "fpp-ltd":
        return fpp_logo;
      case "thl":
        return efc_logo;
      case "issproduce":
        return iss_logo;
      case "flrs":
        return efc_logo;
      default:
        return dpslogo; // Default fallback
    }
  };

  const getLogoSizeClass = (company) => {
    if (!company) return "h-14 w-100";
    switch (company) {
      case "dpsltd":
        return "!h-16 !w-auto";
      default:
        return "h-14 w-100";
    }
  };

  const handleLogout = async () => {
    try {
      await secureLogout();
    } catch (error) {
      console.error('Secure logout error:', error);
      // Force redirect even if logout fails
      window.location.href = '/login';
    }
  };

  return (
    <>
      <nav
        id="sidemenu"
        className="navbar navbar-default sidebar bg-skin-primary"
        role="navigation"
      >
        <div className="container-fluid h-full">
          <Link href={"/suppliers"} title="Home" className="z-50">
            <div className="brand">
              {company ? (
                <Image
                  src={getLogo(
                    company == "dpsltd" && ADCompany == "DPS MS"
                      ? "DPS MS"
                      : company
                  )}
                  alt="logo"
                  className={getLogoSizeClass(
                    company == "dpsltd" && ADCompany == "DPS MS"
                      ? "DPS MS"
                      : company
                  )}
                />
              ) : (
                <div className="flex justify-center">
                  <Oval
                    color="#002D73"
                    height={20}
                    width={20}
                    visible={true}
                    ariaLabel="oval-loading"
                    secondaryColor="#0066FF"
                    strokeWidth={2}
                    strokeWidthSecondary={2}
                  />
                </div>
              )}
            </div>
          </Link>
          <div
            id="bs-sidebar-navbar-collapse-1"
            className="pt-10 w-100 text-center flex flex-col justify-between flex-end items-stretch h-full"
          >
            <div className="flex flex-col w-full">
              {userData && company && (
                <SideBarLinks
                  userData={userData}
                  currentPathname={currentPathname}
                  company={company}
                  ADCompany={ADCompany}
                />
              )}
            </div>
            <div className="flex justify-center flex-col items-center gap-4 my-4 mb-20 cursor-pointer">
              <a
                title="IT Training Material"
                href={`${process.env.NEXT_PUBLIC_TRAINING_MATERIAL}`}
                target="_blank"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 448 512"
                  fill="#FFFF"
                  className="w-6"
                >
                  <path d="M0 88C0 39.4 39.4 0 88 0L392 0c30.9 0 56 25.1 56 56l0 288c0 22.3-13.1 41.6-32 50.6l0 69.4 8 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L80 512c-44.2 0-80-35.8-80-80c0-2.7 .1-5.4 .4-8L0 424 0 88zM80 400c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0 0-64L80 400zM48 358.7c9.8-4.3 20.6-6.7 32-6.7l312 0c4.4 0 8-3.6 8-8l0-288c0-4.4-3.6-8-8-8L88 48C65.9 48 48 65.9 48 88l0 270.7zM160 112l8.8-17.7c2.9-5.9 11.4-5.9 14.3 0L192 112l17.7 8.8c5.9 2.9 5.9 11.4 0 14.3L192 144l-8.8 17.7c-2.9 5.9-11.4 5.9-14.3 0L160 144l-17.7-8.8c-5.9-2.9-5.9-11.4 0-14.3L160 112zM264 216l16.6-38.8c2.8-6.5 11.9-6.5 14.7 0L312 216l38.8 16.6c6.5 2.8 6.5 11.9 0 14.7L312 264l-16.6 38.8c-2.8 6.5-11.9 6.5-14.7 0L264 264l-38.8-16.6c-6.5-2.8-6.5-11.9 0-14.7L264 216z" />
                </svg>
              </a>
              <a
                title="Logout"
                onClick={handleLogout}
                className="cursor-pointer"
              >
                <Image src={logoutIcon} alt="logout" />
              </a>
            </div>
          </div>
        </div>
      </nav>
    </>
  );
}
