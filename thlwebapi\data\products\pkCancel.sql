-- This query handles cancellation of a packaging request (action ID 4)
-- It marks the request as inactive and updates the status

-- First, mark the request as inactive
UPDATE product_packaging_request
SET is_active = 0
WHERE id = @packaging_request_id;

-- Update all previous statuses to not be the latest
UPDATE product_packaging_status
SET is_latest = 0
WHERE packaging_request_id = @packaging_request_id;

-- Insert the new cancelled status
INSERT INTO product_packaging_status (
    packaging_request_id,
    action_id,
    actioned_by_email,
    actioned_by_name,
    comment,
    is_latest
) 
VALUES (
    @packaging_request_id,
    @actionId,
    @actionedByEmail,
    @actionedByName,
    COALESCE(@comment, NULL),
    1
);

-- Return the request number for reference
SELECT request_no 
FROM product_packaging_request 
WHERE id = @packaging_request_id;