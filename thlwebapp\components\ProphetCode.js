import React, { useState, useEffect, useCallback } from "react";
import debounce from "lodash/debounce";
import { apiConfig } from "@/services/apiConfig";
import { useRouter } from "next/router";

const ProphetCode = ({
  prophetC<PERSON>,
  setProphetCodes,
  prophets,
  setNewProphets,
  setFormChange,
  setProphetCodeChange,
  fetchedProphetCodes,
  setIsProphetCodeUnique,
  newProphets,
  setIsCodeSystemGenerated,
  forField,
  supplierRoles,handleSessionError,
}) => {
  const serverAddress = apiConfig.serverAddress;
  const router = useRouter();
  const { supplierId } = router.query;
  const [showRules, setShowRules] = useState(false); // State to manage rules visibility

  const filterItems = useCallback((searchString) => {
    fetch(
      `${serverAddress}suppliers/get-filtered-supplier-code/${searchString}/${supplierId}`,
      {
        method: "get",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        credentials: 'include',
      }
    )
      .then((res) => {
        if (res.status === 200) {
          return res.json();
        }else if(res.status === 401){
          handleSessionError();
          return;
        }
        return Promise.reject(res);
      })
      .then((data) => {
        if (data) {
          setIsProphetCodeUnique(true);
        } else if (data == false) {
          setIsProphetCodeUnique(false);
        }
      })
      .catch((error) => {
        console.log(error);
      });
  }, []);

  // Debounce the filtering function
  const debouncedFilterItems = useCallback(debounce(filterItems, 500), [
    filterItems,
  ]);

  useEffect(() => {
    return () => {
      // Cleanup the debouncedFilterItems function on component unmount
      debouncedFilterItems.cancel();
    };
  }, [debouncedFilterItems]);

  const handleValueChange = (value) => {
    if (value.length <= 0) {
      debouncedFilterItems.cancel();
    } else {
      debouncedFilterItems(value);
    }
  };

  useEffect(() => {
    if (prophetCodes?.length <= 0) {
      debouncedFilterItems.cancel();
    } else {
      debouncedFilterItems(prophetCodes);
    }
  }, []);

  return (
    <div className="relative col-span-2">
      {/* <label className="labels">Supplier Prophet Code</label> */}
      <input
        type="text"
        placeholder="Supplier Prophet Code"
        name="prophet_system"
        className="px-2 2xl:px-3 border searchbar rounded-md w-full"
        value={
          prophetCodes
            ? prophetCodes
            : newProphets?.length > 0
            ? newProphets[0][1]
            : ""
        }
        // value=""
        onChange={(e) => {
          let code = e.target.value.toUpperCase();
          setIsCodeSystemGenerated(false);
          setProphetCodeChange(true);
          setProphetCodes(code);
          setFormChange(true);
          setNewProphets([[prophets[0], code.replace(/\s/g, ""), prophets[2]]]);
          handleValueChange(code);
        }}
        maxLength="6"
        disabled={supplierRoles.length==0}
        onFocus={() => setShowRules(true)}
        onBlur={() => setShowRules(false)}
      />

      {/* Rules Box */}
      {showRules && !prophetCodes && (
        <div className="absolute bg-white border border-gray-300 rounded-md p-4 mt-2 z-50">
          <h3 className="font-bold">Rules:</h3>
          {forField == "DPS" ? (
            <ul className="list-disc list-inside">
              <li>Code must be unique.</li>
              <li>Must have 6 characters.</li>
              <li>No spaces allowed.</li>
              {(supplierRoles?.includes(1) || supplierRoles?.includes(6)) && (
                <>
                  <li>5 letters + currency symbol.</li>
                  <li>eg: XXXXX£.</li>
                </>
              )}
            </ul>
          ) : forField == "DPS MS" ? (
            <ul className="list-disc list-inside">
              <li>Code must be unique.</li>
              <li>Must have 6 characters.</li>
              <li>No spaces allowed.</li>
              {(supplierRoles?.includes(1) || supplierRoles?.includes(6)) && (
                <>
                  <li>First 4 characters must be uppercase letters.</li>
                  <li>Fifth character must be digit 9.</li>
                  <li>Must end with a currency symbol.</li>
                  <li>eg: XXXX9£.</li>
                </>
              )}
            </ul>
          ) : forField == "EFC" ? (
            <ul className="list-disc list-inside">
              <li>Code must be unique.</li>
              <li>Must have 6 characters.</li>
              <li>No spaces allowed.</li>
              {(supplierRoles?.includes(1) || supplierRoles?.includes(6)) && (
                <><li>Can contain letters or numbers.</li>
              <li>The fifth character cannot be 2 or 9.</li></>)}
            </ul>
          ) : forField == "FPP" ? (
            <ul className="list-disc list-inside">
              <li>Code must be unique.</li>
              <li>Must have 6 characters.</li>
              <li>No spaces allowed.</li>
              {(supplierRoles?.includes(1) || supplierRoles?.includes(6)) && (
                <><li>First 4 characters must be uppercase letters.</li>
              <li>Fifth character must be digit 2.</li>
              <li>Must end with a currency symbol.</li>
              <li>eg: XXXX2£.</li></>)}
            </ul>
          ) : (
            ""
          )}
        </div>
      )}
    </div>
  );
};

export default ProphetCode;
