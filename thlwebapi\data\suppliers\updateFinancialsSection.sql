UPDATE[dbo].[supplier]
   SET
      [vat_number] = @vat_number,
      [company_registration] = @company_registration,
      [currency] = @currency,
      [country_code] = @country_code,
      [payment_terms] = @payment_terms,
      [rejected_reason] = @rejected_reason,
      [payment_type] = @payment_type,
      [validated_procurement_team] = @validated_procurement_team,
      [financial]=@financial,
      [vatable]=@vatable,
      [updated_date] = @updated_date,
      [finance_authorization]= @finance_authorization,
      [finance_authorization_by]=@finance_authorization_by,
      [finance_authorization_date]=@finance_authorization_date,
      [validated_by]=@validated_by,
      [validated_date]=@validated_date,
      [status]=@status
WHERE [id] = @id
