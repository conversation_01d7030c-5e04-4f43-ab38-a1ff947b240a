"use strict";

const { validateSession } = require("./sessionAuth");

// Module-based access control
const requireModule = (moduleName) => {
  return (req, res, next) => {
    validateSession(req, res, (err) => {
      if (err) return next(err);
      
      const availableModules = process.env.AVAILABLE_MODULES?.split(",") || [];
      
      if (!availableModules.includes(moduleName)) {
        return res.status(403).json({ 
          error: 'Module not available',
          requiredModule: moduleName
        });
      }
      
      next();
    });
  };
};

// Role-based access control
const requireRole = (allowedRoles) => {
  return (req, res, next) => {
    validateSession(req, res, (err) => {
      if (err) return next(err);
      
      const userRoleId = req.session?.user?.role_id;
      
      if (!userRoleId) {
        return res.status(403).json({ error: 'User role not found' });
      }
      
      const hasAccess = Array.isArray(allowedRoles) 
        ? allowedRoles.includes(userRoleId)
        : allowedRoles === userRoleId;
      
      if (!hasAccess) {
        return res.status(403).json({ 
          error: 'Insufficient permissions',
          requiredRoles: allowedRoles,
          userRole: userRoleId
        });
      }
      
      next();
    });
  };
};

// Department-based access control
const requireDepartment = (allowedDepartments) => {
  return (req, res, next) => {
    validateSession(req, res, (err) => {
      if (err) return next(err);
      
      const userDeptId = req.session?.user?.department_id;
      
      if (!userDeptId) {
        return res.status(403).json({ error: 'User department not found' });
      }
      
      const hasAccess = Array.isArray(allowedDepartments) 
        ? allowedDepartments.includes(userDeptId)
        : allowedDepartments === userDeptId;
      
      if (!hasAccess) {
        return res.status(403).json({ 
          error: 'Department access denied',
          requiredDepartments: allowedDepartments,
          userDepartment: userDeptId
        });
      }
      
      next();
    });
  };
};

// Company-based access control
const requireCompany = (allowedCompanies) => {
  return (req, res, next) => {
    validateSession(req, res, (err) => {
      if (err) return next(err);
      
      const userCompany = req.session?.user?.company;
      
      if (!userCompany) {
        return res.status(403).json({ error: 'User company not found' });
      }
      
      const hasAccess = Array.isArray(allowedCompanies) 
        ? allowedCompanies.includes(userCompany)
        : allowedCompanies === userCompany;
      
      if (!hasAccess) {
        return res.status(403).json({ 
          error: 'Company access denied',
          requiredCompanies: allowedCompanies,
          userCompany: userCompany
        });
      }
      
      next();
    });
  };
};

// Combined permission check
const requirePermissions = (requirements) => {
  return (req, res, next) => {
    validateSession(req, res, (err) => {
      if (err) return next(err);
      
      const user = req.session?.user;
      if (!user) {
        return res.status(401).json({ error: 'User not authenticated' });
      }
      
      // Check each requirement
      for (const requirement of requirements) {
        const { type, values } = requirement;
        
        switch (type) {
          case 'role':
            if (!values.includes(user.role_id)) {
              return res.status(403).json({ 
                error: 'Insufficient role permissions',
                requiredRoles: values,
                userRole: user.role_id
              });
            }
            break;
            
          case 'department':
            if (!values.includes(user.department_id)) {
              return res.status(403).json({ 
                error: 'Insufficient department permissions',
                requiredDepartments: values,
                userDepartment: user.department_id
              });
            }
            break;
            
          case 'company':
            if (!values.includes(user.company)) {
              return res.status(403).json({ 
                error: 'Insufficient company permissions',
                requiredCompanies: values,
                userCompany: user.company
              });
            }
            break;
            
          case 'module':
            const availableModules = process.env.AVAILABLE_MODULES?.split(",") || [];
            if (!values.every(module => availableModules.includes(module))) {
              return res.status(403).json({ 
                error: 'Module not available',
                requiredModules: values,
                availableModules: availableModules
              });
            }
            break;
        }
      }
      
      next();
    });
  };
};

module.exports = {
  requireRole,
  requireDepartment,
  requireCompany,
  requireModule,
  requirePermissions
}; 