import React, { useEffect, useRef } from "react";
import useOnScreen from "../../hooks/useOnScreen";
import TableColumnTh from "../TableColumnTh";
import TablePriceTd from "./TablePriceTd";

function TablePriceSection({
  currentData,
  rowHeight,
  noOfWeeks,
  checkedStates,
  columnRefs,
  columnWidths,
  currentStartWeek,
  currentWeek,
  calendarWeeks,
  setModal,
  currency,
  setIsPriceRendered,fiscalyear,currentFiscalYear
}) {
  const rowPriceRef = useRef();
  const isRowPriceVisible = useOnScreen(rowPriceRef);

  useEffect(() => {
    if (isRowPriceVisible) {
      setIsPriceRendered(true);
    }
  }, [isRowPriceVisible]);

  return (
    <>
      <tr
        style={{ top: `${rowHeight}px` }}
        className="sticky z-10 price"
        ref={rowPriceRef}
      >
        {!isRowPriceVisible && <td style={{ minHeight: '10px' }}></td>}
        {isRowPriceVisible && (
          <TableColumnTh
            rowId="sectionrow"
            rowTitle="UNIT PRICE"
            headHeight={rowHeight}
            noOfWeeks={noOfWeeks}
            checkedStates={checkedStates}
            columnRefs={columnRefs}
            columnWidths={columnWidths}
          />
        )}
      </tr>
      {isRowPriceVisible &&
        currentData?.map((row, index) => {
          const quarterData = [
            ...(row.quarters?.Q1 || []),
            ...(row.quarters?.Q2 || []),
            ...(row.quarters?.Q3 || []),
            ...(row.quarters?.Q4 || []),
            ...(row.quarters?.Q5 || []),
          ];

          return (
            <tr
              key={index}
              title={`${
                !!row.isLockedBy
                  ? `${row.isLockedBy} is currently working on this product`
                  : ""
              }`}
            >
              <TableColumnTh
                isTd={true}
                checkedStates={checkedStates}
                columnRefs={columnRefs}
                columnWidths={columnWidths}
                row={row}
                tdValue={row?.total_product_avg_unit_price?.toFixed(2)}
                isLockedBy={row.isLockedBy}
              />
              <TablePriceTd
                quarterData={quarterData}
                row={row}
                previousSunday={currentStartWeek}
                currentWeek={currentWeek}
                calendarWeeks={calendarWeeks}
                setModal={setModal}
                currency={currency}
                isLockedBy={row.isLockedBy}
                fiscalyear={fiscalyear}
                currentFiscalYear={currentFiscalYear}
              />
            </tr>
          );
        })}
    </>
  );
}

export default TablePriceSection;
