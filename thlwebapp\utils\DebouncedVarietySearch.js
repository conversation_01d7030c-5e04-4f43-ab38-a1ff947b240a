import React, { useState, useEffect } from "react";

const DebouncedVarietySearch = ({
  setIsDropdownOpen,
  varietySearchInput,
  handleVarietySearchInputChange,
  filteredItems,
  handleVarietyDataSelect,
  dropdownRef,
  isDropdownLoading,
  setFilteredItems,
  isDropdownOpen,
  isRequestInfoEnabled,
}) => {
  const handleBlur = () => {
    setFilteredItems([]);
    setIsDropdownOpen(false);
  };

  const handleClickOutside = (event) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setFilteredItems([]);
      setIsDropdownOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  return (
    <div className="flex flex-col relative w-full" ref={dropdownRef}>
      <input
        type="text"
        id="variety-search"
        maxLength={50}
        className="w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
        required
        onFocus={() => setIsDropdownOpen(true)}
        disabled={!isRequestInfoEnabled}
        value={varietySearchInput}
        onChange={(e) => {
          handleVarietySearchInputChange(e);
          setIsDropdownOpen(true);
        }}
      />

      {filteredItems && filteredItems.length > 0 && (
        <div>
          <ul
            className="mt-2 p-2 border border-gray-100 shadow absolute z-10 top left h-24 w-full"
            style={{
              backgroundColor: "white",
              borderRadius: "4px",
              height: "200px",
              overflow: "auto",
            }}
          >
            {filteredItems.map((item, key) => (
              <li
                key={key}
                className="py-3 flex justify-start cursor-pointer "
                onClick={() => {
                  handleVarietyDataSelect(item);
                }}
                onBlur={handleBlur}
                title="Variety data"
              >
                <span className="w-1/2 hover:bg-gray-100">
                  ({item.code})-{item.description}{" "}
                  {`${
                    item.prophet_name != null ? `-${item.prophet_name}` : ""
                  }`}
                </span>
                <span className="w-1/2" title="Master product code">
                  ({item.master_product_code})
                </span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {isDropdownOpen &&
        filteredItems.length === 0 &&
        varietySearchInput &&
        !isDropdownLoading && <div className="mt-2 mb-2">No Entries</div>}
    </div>
  );
};

export default DebouncedVarietySearch;
