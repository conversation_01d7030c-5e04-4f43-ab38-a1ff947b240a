import React, { useState, Fragment, useEffect, useRef, useMemo } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faInfo,
    faXmark
} from "@fortawesome/free-solid-svg-icons";
import { useRouter } from "next/router";

const ValidationAlertBox = ({isOpen, setIsOpen,navType ,onSubmit, step, setIsCancelled, setIsContinue}) => {
  const router = useRouter();
    const closeModal = () => {
        setIsCancelled(true);
        setIsOpen(false);
    }
    const handleSubmit=()=>{
      if(navType=="sae"){
        router.push({ pathname: "/suppliers" });
      } else {
        setIsContinue(true);
        setIsCancelled(false);
        //setIsOpen(true);
        onSubmit();
      }
    }

    return (
        <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-10" onClose={closeModal}>
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
              </Transition.Child>

              <div className="fixed inset-0 overflow-y-auto">
                <div className="flex items-center justify-center min-h-full p-4 text-center">
                  <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0 scale-95"
                    enterTo="opacity-100 scale-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100 scale-100"
                    leaveTo="opacity-0 scale-95"
                  >
                    <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                      {/* <!-- Modal content --> */}
                      <div className="relative bg-white rounded-lg shadow">
                        {/* <!-- Modal header --> */}
                        <div className="flex items-start justify-between p-8 rounded-t">
                          <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                            <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                              <FontAwesomeIcon
                                icon={faInfo}
                              />{" "}
                            </span>{" "}
                            Warning
                          </h3>
                          <button
                            onClick={closeModal}
                            type="button"
                            className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                            data-modal-hide="default-modal"
                          >
                            <FontAwesomeIcon
                              icon={faXmark}
                              className="text-skin-primary"
                            />{" "}
                          </button>
                        </div>
                        {/* <!-- Modal body --> */}
                        <div className="p-8 py-0 space-y-6">
                          <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                            Mandatory information missing. Do you want to continue?
                          </p>
                        </div>
                        {/* <!-- Modal footer --> */}
                        <div className="flex items-end p-6 space-x-2 justify-end">
                          <button
                            onClick={handleSubmit}
                            data-modal-hide="default-modal"
                            type="button"
                            className="text-skin-a11y bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                          >
                            Continue
                          </button>
                          <button
                            onClick={closeModal}
                            data-modal-hide="default-modal"
                            type="button"
                            className="border text-dark-gray focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    </Dialog.Panel>
                  </Transition.Child>
                </div>
              </div>
            </Dialog>
          </Transition>
    )
}

export default ValidationAlertBox;