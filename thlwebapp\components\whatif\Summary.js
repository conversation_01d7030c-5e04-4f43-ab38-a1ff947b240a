import React from 'react';
import styled from 'styled-components';
import Filter from './Filter';

const SummaryContainer = styled.div`
  display: flex;
  justify-content: space-between;
  background-color: #ecf0f1;
  padding: 10px;
  margin-bottom: 20px;
  border-radius: 5px;
`;

const SummaryItem = styled.div`
  flex: 1;
  text-align: center;
  padding: 10px;
  background-color: #ffffff;
  border: 1px solid #ddd;
  margin: 0 10px;
  border-radius: 5px;
`;

const Summary = ({ data }) => {
    return (
      <>
       <Filter />
            <SummaryContainer>
        {/* {data.map((row, index) => (
            <>
                <span>Quartor {index + 1}</span>
                <SummaryItem>TO: {row.to}</SummaryItem>
                <SummaryItem>GP: {row.gp}</SummaryItem>
                <SummaryItem>Difference: {row.difference}</SummaryItem>
            </>
        ))} */}
      </SummaryContainer>
      </>
    );
  };
  
export default Summary;