BEGIN TRANSACTION;

UPDATE [dbo].[products] 
    SET 
        [submitted_to_iss] = @submitted_to_iss,
        [status] = @status
where [id] = @id;

-- Insert new row into the [products_request_unblock] table
INSERT INTO [dbo].[products_request_unblock] 
    ([request_no], [unblocked_by], [unblocked_reason], [unblocked_at], [is_active])
VALUES 
    (@request_no, @unblocked_by, @unblocked_reason, @unblocked_at, 1);
SELECT * from [dbo].[products] where [id] = @id;

COMMIT TRANSACTION;