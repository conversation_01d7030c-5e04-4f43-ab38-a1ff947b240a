DECLARE @id INT;

INSERT INTO
      [dbo].[supplier] (
            [name],
            [emergency_request],
            [gdpr_compliant],
            [product_supplier],
            [created_date],
            [updated_date],
            [status],
            [requestor_name],
            [requestor_email],
            [company],
            [is_code_system_generated],
            [supplier_type]
      )
VALUES
      (
            @name,
            @emergency_request,
            @gdpr_compliant,
            @product_supplier,
            @created_date,
            @updated_date,
            @status,
            @requestor_name,
            @requestor_email,
            @company,
            @is_code_system_generated,
            @supplier_type
      );

SET
      @id = SCOPE_IDENTITY();

SELECT
      @id AS id,
      [name]
FROM
      [dbo].[supplier]
WHERE
      id = @id;