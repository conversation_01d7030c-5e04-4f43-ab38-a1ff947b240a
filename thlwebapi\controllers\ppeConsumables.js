"use strict";
const ppeConsumables = require("../data/ppeConsumables")


const allPpeRequests = async (req, res) => {
  try {
    const AllPpeRequests = req.params.id ? req.params.id :  55;
    const PpeRequests = await ppeConsumables.getOrderRequests(AllPpeRequests);
    res.send(PpeRequests);
  } catch (error) {
    console.error("error fetching packaging requests by id",error)
    res.status(400).send(error.message);
  }
};

const allProducts = async (req, res) => {
  try {
    const AllPpeRequests = req.params.id ? req.params.id :  55;
    const PpeRequests = await ppeConsumables.getProducts(AllPpeRequests);
    res.send(PpeRequests);
  } catch (error) {
    console.error("error fetching packaging requests by id",error)
    res.status(400).send(error.message);
  }
};


const addProduct = async (req, res) => {
  try {
    const result = await ppeConsumables.addProduct(req.body);
    res.status(200).json({
      msg: "Product added successfully",
      productId: result?.NewProductID
    });
  } catch (error) {
    console.error("Error adding product:", error);
    res.status(400).send(error.message);
  }
};


module.exports = {
  allPpeRequests,
  allProducts,
  addProduct
}