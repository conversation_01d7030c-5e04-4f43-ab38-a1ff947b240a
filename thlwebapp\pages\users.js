import Layout from "@/components/Layout";
import React, {
  useMemo,
  useState,
  useRef,
  useCallback,
  useEffect,
} from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faSearch,
  faTrash,
  faPenToSquare,
} from "@fortawesome/free-solid-svg-icons";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles//ag-grid.css";
import "ag-grid-community/styles//ag-theme-alpine.css";
import { useRouter } from "next/router";
import { apiConfig } from "@/services/apiConfig";
import roleRenderer from "@/utils/renderer/roleRenderer";
import departmentRenderer from "@/utils/renderer/departmentRenderer";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useLoading } from "@/utils/loaders/loadingContext";
import { logout } from "@/utils/secureStorage";

const users = ({ userData }) => {
  const { setIsLoading } = useLoading();
  const router = useRouter();
  const [pageSize, setPageSize] = useState(15);
  const [rowData, setRowData] = useState([]);
  const [email, setEmail] = useState("");
  const [role, setRole] = useState("");
  const [department, setDepartment] = useState("");
  const [editData, setEditData] = useState({});

  const [validEmail, setValidEmail] = useState(false);
  const [validRole, setValidRole] = useState(false);
  const [validDepartment, setValidDepartment] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [userId, setUserId] = useState(0);
  const [salesDepartment, setSalesDepartment] = useState(false);
  const [procurementDepartment, setProcurementDepartment] = useState(false);
  const [financialDepartment, setFinancialDepartment] = useState(false);
  const [technicalDepartment, setTechnicalDepartment] = useState(false);
  const [isCommonError, setCommonError] = useState("");
  const gridRef = useRef();

  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  const IconsRenderer = (props) => {
    let updatedData;

    const handleDelete = async (event) => {
      const allData = props.data;

      if (allData.userId == userData.user_id) {
        toast.error("Logged in user can't delete their own email", {
          position: "top-right",
        });
        return false;
      }

      try {
                 const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8081'}/api/users/delete-user/${allData.userId}`, {
          method: "DELETE",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          credentials: 'include', // Use session authentication
          body: JSON.stringify({
            username: userData.name,
            useremail: userData.email,
          }),
        });

        if (response.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (response.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async () => {
            await logout();
            router.push('/login');
          }, 3000);
        } else if (response.status === 200 || response.status === 409) {
          const json = await response.json();
          
          if (json.status === 200) {
            updatedData = [...rowData];
            const index = updatedData.indexOf(allData);
            updatedData.splice(index, 1);
            props.api.applyTransaction({ remove: updatedData });
            setRowData(updatedData);
            
            toast.success(json.message, {
              position: "top-right",
            });
          }
        } else {
          throw new Error("Failed to delete user");
        }
      } catch (error) {
        console.error("Delete user error:", error);
        toast.error("Failed to delete user", {
          position: "top-right",
        });
      }
    };

    const handleEdit = () => {
      const editedData = props.data;
      setEditData(editedData);

      updatedData = [...rowData];
      const index = updatedData.indexOf(editedData);
      updatedData.splice(index, 1);
      props.api.applyTransaction({ remove: updatedData });
      setRowData(updatedData);
      setEmail(editedData.email);
      setRole(editedData.role);
      setDepartment(editedData.department);
      setUserId(editedData.userId);
      setIsEdit(true);
    };

    const isEditDisabled =
      isEdit ||
      ((props.data.role === 6 || props.data.role === 5) &&
        props.data.userId !== userData.user_id);

    const isDeleteDisabled =
      isEdit ||
      props.data.role === 6 ||
      props.data.role === 5 ||
      props.data.userId === userData.user_id;

    return (
      <div className="flex flex-row gap-4 justify-center text-skin-primary">
        <button
          onClick={handleEdit}
          disabled={isEdit || (userData.role_id != 6 && isEditDisabled)}
        >
          <FontAwesomeIcon icon={faPenToSquare} />
        </button>
        <button
          onClick={handleDelete}
          disabled={userData.role_id != 6 && isDeleteDisabled}
          className="text-red-500"
        >
          <FontAwesomeIcon icon={faTrash} />
        </button>
      </div>
    );
  };
  // useEffect(() => {
  //   const roleIdFromCookies = cookies.role_id;
  //   if (roleIdFromCookies !== 1) {
  //     router.push('/unauthorized'); // Assuming you're using Next.js router
  //   }
  // }, []);

  const handleSelectDepartment = (e) => {
    if (e.target.value == 1) {
      if (e.target.checked) {
        setSalesDepartment(true);
        setProcurementDepartment(false);
        setFinancialDepartment(false);
        setTechnicalDepartment(false);
      } else {
        setSalesDepartment(false);
        setProcurementDepartment(false);
        setFinancialDepartment(false);
        setTechnicalDepartment(false);
      }
    } else if (e.target.value == 2) {
      if (e.target.checked) {
        setSalesDepartment(false);
        setProcurementDepartment(true);
        setFinancialDepartment(false);
        setTechnicalDepartment(false);
      } else {
        setSalesDepartment(false);
        setProcurementDepartment(false);
        setFinancialDepartment(false);
        setTechnicalDepartment(false);
      }
    } else if (e.target.value == 3) {
      if (e.target.checked) {
        setSalesDepartment(false);
        setProcurementDepartment(false);
        setFinancialDepartment(true);
        setTechnicalDepartment(false);
      } else {
        setSalesDepartment(false);
        setProcurementDepartment(false);
        setFinancialDepartment(false);
        setTechnicalDepartment(false);
      }
    } else if (e.target.value == 5) {
      if (e.target.checked) {
        setSalesDepartment(false);
        setProcurementDepartment(false);
        setFinancialDepartment(false);
        setTechnicalDepartment(true);
      } else {
        setSalesDepartment(false);
        setProcurementDepartment(false);
        setFinancialDepartment(false);
        setTechnicalDepartment(true);
      }
    } else {
      setSalesDepartment(false);
      setProcurementDepartment(false);
      setFinancialDepartment(false);
      setTechnicalDepartment(false);
    }
  };

  async function handleSubmit() {
    setIsEdit(false);
    if (email && role) {
      try {
        const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8081';
        const url = isEdit 
          ? `${apiBase}/api/users/update-user/${userId}`
          : `${apiBase}/api/users/add-user`;
        
        const method = isEdit ? "PUT" : "POST";
        
        const response = await fetch(url, {
          method: method,
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          credentials: 'include', // Use session authentication
          body: JSON.stringify({
            role_id: role,
            email: email?.trim(),
            user: {
              username: userData.name,
              useremail: userData.email,
            },
            department_id: department,
          }),
        });

        if (response.status === 400 || response.status === 401) {
          setCommonError("Session expired or invalid request");
          if (response.status === 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(async () => {
              await logout();
              router.push('/login');
            }, 3000);
          }
          return;
        }

        if (response.status === 200 || response.status === 409) {
          const json = await response.json();
          
          if (isEdit) {
            const newItem = {
              email: json[0].email,
              role: json[0].role_id,
              userId: json[0].user_id,
              department: json[0].department_id,
            };
            setRowData([...rowData, newItem]);
            setEmail("");
            setRole("");
            setDepartment("");
            toast.success("User successfully updated", {
              position: "top-right",
            });
          } else {
            if (json.data === "exists") {
              toast.error("User already exists.", {
                position: "top-right",
              });
              return;
            }
            
            if (json.length > 0) {
              const newItem = {
                email: email,
                role: role,
                userId: json[0].id,
                department: department,
              };
              setRowData([...rowData, newItem]);
              setEmail("");
              toast.success("User successfully created", {
                position: "top-right",
              });
            }
          }
        } else {
          throw new Error("Failed to save user");
        }
      } catch (error) {
        console.error("Save user error:", error);
        toast.error("Failed to save user", {
          position: "top-right",
        });
      }
    } else {
      setValidEmail(true);
      setValidRole(true);
      setValidDepartment(true);
    }
  }

  function handleCancel() {
    setIsEdit(false);
    if (isEdit) {
      const newItem = {
        email: editData.email,
        role: editData.role,
        userId: editData.userId,
        department: editData.department,
      };
      setRowData([...rowData, newItem]);
    }
    setEmail("");
    setRole("");
    setDepartment("");
    setEditData({});
    setValidDepartment(false);
    setValidRole(false);
    setValidEmail(false);
  }

  const handleUserValidation = () => {
    if (email && emailRegex.test(email)) {
      setValidEmail(false);
    } else {
      setValidEmail(true);
    }

    if (validRole) {
      setValidRole(true);
    } else {
      setValidRole(false);
    }

    if (validDepartment) {
      setValidDepartment(true);
    } else {
      setValidDepartment(false);
    }

    return true;
  };

  async function getData() {
    try {
             const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8081'}/api/users/get-users`, {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        credentials: 'include', // Use session authentication
      });

      if (response.status === 400 || response.status === 401) {
        setCommonError("Session expired");
        if (response.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async () => {
            await logout();
            router.push('/login');
          }, 3000);
        }
        return null;
      }

      if (response.status === 200) {
        return await response.json();
      }

      throw new Error("Failed to fetch data");
    } catch (error) {
      console.error("Get users error:", error);
      toast.error("Failed to fetch data", {
        position: "top-right",
      });
      return null;
    }
  }

  useEffect(() => {
    setIsLoading(false);
    if (typeof document !== "undefined") {
      document.title = "Users";
    }
    if (typeof window !== "undefined") {
      localStorage.removeItem("supplier_id");
    }

    getData().then((data) => {
      if (data) {
        const formattedData = data?.map((row) => ({
          email: row.email,
          role: row.role_id,
          userId: row.user_id,
          department: row.department_id,
        }));
        setRowData(formattedData);
      }
    });
  }, []);

  const handlePageSizeChange = (event) => {
    const newPageSize = parseInt(event.target.value, 15);
    setPageSize(newPageSize);
    gridRef.current.api.paginationSetPageSize(newPageSize);
  };

  const defaultColDef = useMemo(() => ({
    sortable: true,
    filter: false,
    resizable: true,
    flex: 1,
    suppressMenu: false,
  }));

  const columnDefs = [
    {
      headerName: "Email",
      field: "email",
      suppressMenu: true,
      suppressSizeToFit: true,
      flex: "6%",
    },
    {
      headerName: "Role",
      field: "role",
      cellRenderer: roleRenderer,
      flex: "4%",
      hide: false,
    },
    {
      headerName: "Department",
      field: "department",
      cellRenderer: departmentRenderer,
      flex: "4%",
      hide: false,
    },
    {
      field: "",
      cellRenderer: IconsRenderer,
      flex: "4%",
      cellStyle: () => ({}),
    },
    {
      field: "userId",
      //cellRenderer: IconsRenderer,
      flex: "4%",
      cellStyle: () => ({}),
      hide: true,
      suppressFiltersToolPanel: true,
    },
  ];

  const onFilterTextBoxChanged = useCallback(() => {
    gridRef.current.api.setQuickFilter(
      document.getElementById("filter-text-box").value
    );
  }, []);

  const handleGridReady = (params) => {
    params.api.setColumnDefs(columnDefs);
  };

  return (
    <>
      <ToastContainer />
      <Layout userData={userData}>
        <div className="flex flex-row justify-between w-[95%] gap-8 pe-1">
          <div className="w-[50%]">
            <div className="flex flex-row md:flex-col lg:flex-row items-end">
              <div className="flex gap-6 w-full justify-end">
                <label className="relative block w-[47vh] text-gray-400 focus-within:text-gray-600 mt-0 pt-1">
                  <span className="absolute px-4 py-1 text-black">
                    <FontAwesomeIcon icon={faSearch} className="fw-bold" />
                  </span>
                  <input
                    type="text"
                    id="filter-text-box"
                    placeholder="Search"
                    onInput={onFilterTextBoxChanged}
                    className="block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none"
                  />
                </label>
              </div>
            </div>
            <div className="my-5">
              <div
                className="relative ag-theme-alpine"
                style={{ height: "calc(100vh - 150px)" }}
              >
                <AgGridReact
                  rowData={rowData}
                  ref={gridRef}
                  columnDefs={columnDefs}
                  defaultColDef={defaultColDef}
                  suppressRowClickSelection
                  rowSelection="multiple"
                  pagination={true}
                  paginationPageSize={pageSize}
                  onPageSizeChanged={handlePageSizeChange}
                  tooltipShowDelay={0}
                  tooltipHideDelay={1000}
                  onGridReady={handleGridReady}
                />
                <div className="flex justify-start mt-2 pagination-style">
                  <label
                    htmlFor="page-size-select pagination"
                    className="inputs"
                  >
                    Show{" "}
                    <select
                      id="page-size-select"
                      onChange={handlePageSizeChange}
                      value={pageSize}
                      className="focus:outline-none"
                    >
                      <option value={10}>10</option>
                      <option value={15}>15</option>
                      <option value={25}>25</option>
                      <option value={50}>50</option>
                      <option value={100}>100</option>
                    </select>{" "}
                    entries
                  </label>
                </div>
              </div>
            </div>
          </div>
          <div className="w-[50%] mt-14">
            <div className="relative panel-container contentsectionbg rounded-lg w-full 2xl:w-[calc(100%-70px)] p-4 pb-0 shadow-lg">
              <div className="m-3 mb-0">
                <div className="mb-3">
                  <h4 className="formtitle pb-1">Assign user role </h4>
                </div>
                <div className="flex flex-col mb-6">
                  <label className="labels mb-1">
                    Email <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="email"
                    maxLength={80}
                    name="Email"
                    value={email}
                    onChange={(e) => {
                      setEmail(e.target.value), handleUserValidation;
                    }}
                    className="w-full px-2 2xl:px-3 border border-light-gray rounded-md searchbar"
                    onBlur={handleUserValidation}
                  />
                  {validEmail && (
                    <span className="text-red-500">
                      Please enter valid email address
                    </span>
                  )}
                </div>
                <div className="flex flex-col mb-6">
                  <label className="me-5 labels">
                    Role <span className="text-red-500">*</span>
                  </label>
                  <div className="flex mt-1">
                    {userData?.role_id == 6 && (
                      <>
                        <div className="flex">
                          <div className="flex items-center">
                            <input
                              id="superadmin-radio"
                              type="radio"
                              value="1"
                              checked={role == 6}
                              onChange={() => {
                                setRole(6), handleUserValidation;
                              }}
                              onBlur={handleUserValidation}
                              className="ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2"
                            />
                            <label
                              htmlFor="superadmin-radio"
                              className="p-0 ml-2 me-5 text-blackcolor"
                            >
                              Super Admin
                            </label>
                          </div>
                        </div>
                      </>
                    )}
                    {(userData?.role_id == 5 || userData?.role_id == 6) && (
                      <div className="flex">
                        <div className="flex items-center">
                          <input
                            id="thladmin-radio"
                            type="radio"
                            value="1"
                            checked={role == 5}
                            onChange={() => {
                              setRole(5), handleUserValidation;
                            }}
                            disabled={userData?.role_id ==5 ? true : false}
                            onBlur={handleUserValidation}
                            className="ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2"
                          />
                          <label
                            htmlFor="thladmin-radio"
                            className="p-0 ml-2 me-5 text-blackcolor"
                          >
                            THL Admin
                          </label>
                        </div>
                      </div>
                    )}
                    <div className="flex">
                      <div className="flex items-center">
                        <input
                          id="admin-radio"
                          type="radio"
                          value="1"
                          checked={role == 1}
                          onChange={() => {
                            setRole(1), handleUserValidation;
                          }}
                          onBlur={handleUserValidation}
                          className="ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer"
                        />
                        <label
                          htmlFor="admin-radio"
                          className="p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer"
                        >
                          Administrator
                        </label>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="approver-radio"
                        type="radio"
                        value="2"
                        checked={role == 2}
                        onChange={() => {
                          setRole(2), handleUserValidation;
                        }}
                        onBlur={handleUserValidation}
                        className="ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2"
                      />
                      <label
                        htmlFor="approver-radio"
                        className="p-0 ml-2 me-5 text-blackcolor"
                      >
                        Approver
                      </label>
                    </div>
                    <div className="flex">
                      <div className="flex items-center">
                        <input
                          id="user-radio"
                          type="radio"
                          value="4"
                          checked={role == 4}
                          onChange={() => {
                            setRole(4), handleUserValidation;
                          }}
                          onBlur={handleUserValidation}
                          className="ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer"
                        />
                        <label
                          htmlFor="user-radio"
                          className="p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer"
                        >
                          User
                        </label>
                      </div>
                    </div>
                  </div>
                  {validRole && (
                    <span className="text-red-500">Select role</span>
                  )}
                </div>
                <div className="flex flex-col mb-6">
                  <label className="me-5 labels">
                    Department <span className="text-red-500">*</span>
                  </label>
                  <div className="flex mt-1">
                    <div className="flex items-center">
                      <input
                        id="department-sales"
                        type="radio"
                        value={1}
                        checked={department == 1}
                        onChange={(e) => {
                          setDepartment(1);
                          handleSelectDepartment(e);
                        }}
                        onBlur={handleUserValidation}
                        className="ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer"
                      />
                      <label
                        htmlFor="department-sales"
                        className="p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer"
                      >
                        Sales
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="department-procurement"
                        type="radio"
                        value={2}
                        checked={department == 2}
                        onChange={(e) => {
                          setDepartment(2);
                          handleSelectDepartment(e);
                        }}
                        onBlur={handleUserValidation}
                        className="ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer"
                      />
                      <label
                        htmlFor="department-procurement"
                        className="p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer"
                      >
                        Procurement
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="department-financial"
                        type="radio"
                        value={3}
                        checked={department == 3}
                        onChange={(e) => {
                          setDepartment(3);
                          handleSelectDepartment(e);
                        }}
                        onBlur={handleUserValidation}
                        className="ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer"
                      />
                      <label
                        htmlFor="department-financial"
                        className="p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer"
                      >
                        Financial
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="department-technical"
                        type="radio"
                        value={5}
                        checked={department == 5}
                        onChange={(e) => {
                          setDepartment(5);
                          handleSelectDepartment(e);
                        }}
                        onBlur={handleUserValidation}
                        className="ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer"
                      />
                      <label
                        htmlFor="department-technical"
                        className="p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer"
                      >
                        Technical
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="department-logistics"
                        type="radio"
                        value={8}
                        checked={department == 8}
                        onChange={(e) => {
                          setDepartment(8);
                          handleSelectDepartment(e);
                        }}
                        onBlur={handleUserValidation}
                        className="ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer"
                      />
                      <label
                        htmlFor="department-logistics"
                        className="p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer"
                      >
                        Logistics
                      </label>
                    </div>
                    {/* <div className="flex items-center">
                      <input
                          id="department-checkbox-3"
                          type="radio"
                          value={4}
                          checked={department == 4}
                          onChange={(e) => {
                            setDepartment(4);
                            handleSelectDepartment(e)
                          }}
                          onBlur={handleUserValidation}
                          className="ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2"
                        />
                        <label
                          htmlFor="approver-radio"
                          className="p-0 ml-2 me-5 text-blackcolor"
                        >
                          None
                        </label>
                    </div> */}
                  </div>
                  {validDepartment && (
                    <span className="text-red-500">Select Departement</span>
                  )}
                </div>
                <div className="flex justify-end pb-4">
                  <button
                    className="border border-skin-primary text-skin-primary me-10 py-1 px-8 font-medium rounded-md"
                    onClick={handleCancel}
                  >
                    Cancel
                  </button>
                  <button
                    className="border border-skin-primary bg-skin-primary text-white rounded-md py-1 px-8 font-medium"
                    onClick={handleSubmit}
                    disabled={
                      email && emailRegex.test(email) && role && department
                        ? false
                        : true
                    }
                  >
                    Save
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default users;

export const getServerSideProps = async (context) => {
  try {
    // Use secure session validation
    const sessionId = context.req.cookies.thl_session;
    
    if (!sessionId) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
          permanent: false,
        },
      };
    }

    // Validate session with our backend API
    const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8081';
    
    try {
      const response = await fetch(`${apiBase}/api/auth/me`, {
        method: 'GET',
        headers: {
          'Cookie': `thl_session=${sessionId}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        // Session invalid or expired - redirect to login
        return {
          redirect: {
            destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
            permanent: false,
          },
        };
      }

      const { user } = await response.json();
      
      // Check if user has permission to access users page
      // Only role_id 1 (Admin), 5 (THL Admin), and 6 (Super Admin) can access users page
      const allowedRoles = [1, 5, 6];
      if (!allowedRoles.includes(user.role_id)) {
        return {
          redirect: {
            destination: '/unauthorized',
            permanent: false,
          },
        };
      }
      
      return {
        props: {
          userData: user,
        },
      };

    } catch (fetchError) {
      console.error('Session validation failed:', fetchError);
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
          permanent: false,
        },
      };
    }

  } catch (error) {
    console.error('Authentication error:', error);
    return {
      redirect: {
        destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
        permanent: false,
      },
    };
  }
};
