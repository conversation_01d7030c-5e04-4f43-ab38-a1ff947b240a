SELECT
      au.[id],
      au.[item_id],
      au.[date],
      au.[username],
      au.[description],
      au.[type],
      s.name,
      pm.[module_name]
FROM
      [audit_logs] au
      LEFT JOIN [supplier] s ON au.item_id = s.id
      LEFT JOIN [portal_modules] pm on au.module_id = pm.id
where
      au.module_id = 1
UNION ALL
SELECT
      au.[id],
      au.[item_id],
      au.[date],
      au.[username],
      au.[description],
      au.[type],
      p.[product_description] as name,
      pm.[module_name]
FROM
      [audit_logs] au
      LEFT JOIN [products] p ON au.item_id = p.id
      LEFT JOIN [portal_modules] pm ON au.module_id = pm.id
WHERE
      au.module_id = 2
UNION ALL
SELECT
      au.[id],
      au.[item_id],
      au.[date],
      au.[username],
      au.[description],
      au.[type],
      wi.[pkey] AS name,
      pm.[module_name]
FROM
      [audit_logs] au
      LEFT JOIN [whatif] wi ON au.item_id = wi.id
      LEFT JOIN [portal_modules] pm ON au.module_id = pm.id
WHERE
      au.module_id = 3
      UNION ALL
SELECT
      au.[id],
      au.[item_id],
      au.[date],
      au.[username],
      au.[description],
      au.[type],
      '-' AS name,
      pm.[module_name]
FROM
      [audit_logs] au
      LEFT JOIN [portal_modules] pm ON au.module_id = pm.id
WHERE
      au.module_id =4

      UNION ALL
SELECT
      au.[id],
      au.[item_id],
      au.[date],
      au.[username],
      au.[description],
      au.[type],
     '-' AS name,
      '-' AS module_name
FROM
      [audit_logs] au
WHERE
      au.module_id IS NULL

ORDER BY
      au.id DESC;