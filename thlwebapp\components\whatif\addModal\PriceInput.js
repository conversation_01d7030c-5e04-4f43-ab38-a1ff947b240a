import React, { useContext, useEffect, useState } from "react";
import { CurrencyContext } from "../providers/CurrencyProvider";

function PriceInput({ name, previous, disabled, whatIfType, onChange }) {
  const { currency } = useContext(CurrencyContext);
  const [value, setValue] = useState("$0.00");
  const [valueTemp, setValueTemp] = useState("$0.00");
  const [isFirstRender, setIsFirstRender] = useState(true);
  const [refresh, setRefresh] = useState(false);

  useEffect(() => {
    if (!isFirstRender) {
      onChange(value.replace(currency, ""));
    }
  }, [value]);

  useEffect(() => {
    setIsFirstRender(false);
    setValueTemp(formatValue(previous) ?? `${currency}0.00`);
  }, [whatIfType]);

  useEffect(() => {
    setValueTemp(formatValue(previous) ?? `${currency}0.00`);
  }, [previous]);

  const formatValue = (val) => {
    let formattedValue = parseFloat(val).toFixed(2);
    if (isNaN(formattedValue)) {
      formattedValue = "0.00";
    }
    return currency + formattedValue;
  };

  const handleInputChange = (e) => {
    let val = e.target.value.replace(`${currency}`, "");

    if (val !== "" && valueTemp.replace(`${currency}`, "") === "0.00") {
      val = val.slice(4);
    }

    if (/^-?\d*\.?\d*$/.test(val)) {
      if (val === "" || val === "-") {
        setValueTemp(val);
      } else {
        let numValue = parseFloat(val);
        if (numValue >= -10000 && numValue <= 10000) {
          setValueTemp(currency + val);
        }
      }
    }
  };

  const handleKeyDown = (e) => {
    let numValue = parseFloat(valueTemp.slice(1));

    if (e.key === "ArrowUp") {
      numValue = Math.min(numValue + 0.01, 10000);
      setValueTemp(formatValue(numValue));
    } else if (e.key === "ArrowDown") {
      numValue = Math.max(numValue - 0.01, -10000);
      setValueTemp(formatValue(numValue));
    }
  };

  const handleBlur = (e) => {
    const valueTempCal =
      +e.target.value.replace(`${currency}`, "") < 0
        ? `-${e.target.value.slice(2)}`
        : e.target.value.slice(1);

    const newValue = formatValue(valueTempCal);
    if (!valueTemp) {
      setValueTemp(newValue);
    }
    setValue(newValue);
  };

  return (
    <input
      type="text"
      name={name}
      className={`px-2 2xl:px-3 border rounded-md w-full`}
      maxLength={10}
      disabled={disabled}
      value={valueTemp.replace(`${currency}-`, `-${currency}`)}
      onChange={handleInputChange}
      onKeyDown={handleKeyDown}
      onBlur={handleBlur}
    />
  );
}

export default PriceInput;
