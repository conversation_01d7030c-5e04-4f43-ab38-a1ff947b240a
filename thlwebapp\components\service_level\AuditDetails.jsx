import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>layDraw<PERSON>,
  <PERSON><PERSON>,
  Badge,
} from "@fluentui/react-components";
import { Dismiss24Regular } from "@fluentui/react-icons";
import { apiConfig } from "@/services/apiConfig";
import { getCookieData } from "@/utils/getCookieData";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { logout } from "@/utils/secureStorage";
import { useRouter } from "next/router";

const AuditDetails = ({
  orderId,
  isAuditDetailsOpen,
  setIsAuditDetailsOpen,
}) => {
  const router = useRouter();
  const [reasonsAuditDetails, setReasonsAuditDetails] = useState([]);

  const fetchAuditReasonData = async (orderId) => {
    setReasonsAuditDetails([]);
    const serverAddress = apiConfig.serverAddress;
    try {
      await fetch(
        `${serverAddress}servicelevel/get-service-level-audit-reasons/${orderId}`,
        {
          method: "GET",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      )
        .then((res) => {
          if (res.status == 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(async() => {
              await logout();
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              router.push(redirectUrl);
            }, 3000);
            return null;
          } else if (res.status == 400) {
            toast.error(
              "There was an error with your request. Please check your data and try again."
            );
            return null;
          } else {
            return res.json(); // Ensure you parse the JSON
          }
        })
        .then((data) => {
          if(data.length>0){
            setReasonsAuditDetails(data);
          }
        });
    } catch (error) {
      console.log("error in fetching", error);
    }
  };

  const formatDate = (timestamp) => {
    const date = new Date(timestamp);

    const formattedDate = date.toLocaleString("en-GB", {
      day: "numeric",
      month: "2-digit",
      year: "numeric",
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    });

    // Format the time to use ".00" for the minutes if needed
    const finalFormattedDate = formattedDate
      .replace(":", ".")
      .replace(" AM", "am")
      .replace(" PM", "pm");

    return finalFormattedDate;
  };

  useEffect(() => {
    if (isAuditDetailsOpen) {
      fetchAuditReasonData(orderId);
    }
  }, [isAuditDetailsOpen]);

  useEffect(() => {
    if (isAuditDetailsOpen) {
      fetchAuditReasonData(orderId);
    }
  }, [isAuditDetailsOpen, orderId]);

  return (
    <div>
      <ToastContainer position="top-left" limit={1}/>
      <OverlayDrawer
        as="aside"
        open={isAuditDetailsOpen}
        position="end"
        onOpenChange={(_, { open }) => setIsAuditDetailsOpen(open)}
        style={{ fontFamily: "poppinsregular" }}
      >
        <DrawerHeader className="!p-3 !gap-0">
          <DrawerHeaderTitle
            action={
              <Button
                appearance="subtle"
                aria-label="Close"
                icon={<Dismiss24Regular />}
                onClick={() => setIsAuditDetailsOpen(false)}
              />
            }
          >
            <span className="text-sm " style={{ fontFamily: "poppinsregular" }}>
              Audit details of the order
            </span>
          </DrawerHeaderTitle>
          <hr className="border-b border-gray-100" />
        </DrawerHeader>

        <DrawerBody
          className="!px-3 flex flex-col gap-3 !pb-3"
          style={{ fontFamily: "poppinsregular" }}
        >
          {reasonsAuditDetails.length > 0 &&
            reasonsAuditDetails.map((reason) => (
              <div
                key={reason.id}
                className="rounded-lg bg-blue-50 border border-gray-200 p-3 flex flex-col gap-1"
              >
                <div className="flex flex-row justify-between">
                  <span>Qty: {reason.quantity}</span>
                  <span>
                    <Badge
                      appearance="filled"
                      color={`${
                        reason.action_type == "INSERT"
                          ? "success"
                          : reason.action_type == "UPDATE" &&
                            reason.is_deleted == 1
                          ? "danger"
                          : "warning"
                      }`}
                    >
                      {`${
                        reason.action_type == "INSERT"
                          ? "Added"
                          : reason.action_type == "UPDATE" &&
                            reason.is_deleted == 1
                          ? "Deleted"
                          : "Updated"
                      }`}
                    </Badge>
                  </span>
                </div>
                <span>Reason: {reason.reason}</span>
                <span>Sub-reason: {reason.sub_reason}</span>
                <span>Comment: {reason.comment}</span>
                <div className="flex flex-col justify-between text-gray-500 text-sm pt-2">
                  <span className="text-base">
                    By {reason.updated_by ? reason.updated_by : reason.added_by}
                  </span>
                  <span>{formatDate(reason.timestamp)}</span>
                </div>
              </div>
            ))}
        </DrawerBody>
      </OverlayDrawer>
    </div>
  );
};

export default AuditDetails;
