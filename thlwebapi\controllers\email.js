"use strict";

const multer = require("multer");
const verifyToken = require("../auth");
const { sendEmail } = require("../utils/email"); // Import your existing sendEmail function
const sql = require("mssql");
const config = require("../config");
// Configure multer for file upload
const storage = multer.memoryStorage();
const upload = multer({ storage: storage }).single("file");
const utils = require("../data/utils");

const getEmailBody = (suppliers, emergencySuppliers) => {
  return `<div class="container">        
        ${
          suppliers && suppliers.length > 0
            ? `
        <div>
        <table style="border-collapse: collapse; width: 100%;">
                <thead>
                    <tr>
                    <th style="border: 1px solid #000; padding: 8px; text-align: left;">Name</th>
                    <th style="border: 1px solid #000; padding: 8px; text-align: left;">Code</th>
                    </tr>
                </thead>
                <tbody>
                    ${arrayToTable(suppliers, emergencySuppliers)}
                </tbody>
            </table>
        </div>`
            : ""
        }
        
        
    </div>`;
};

const arrayToTable = (suppliers, emergencySuppliers) => {
  let trString = "";

  suppliers.forEach(({ supplierName, supplierCode }) => {
    const isEmergency = emergencySuppliers.includes(supplierName);
    trString += `<tr>
                    <td style="border: 1px solid #000; padding: 8px; ${
                      isEmergency ? "color: red;" : ""
                    }">${supplierName}</td>
                    <td style="border: 1px solid #000; padding: 8px; ${
                      isEmergency ? "color: red;" : ""
                    }">${supplierCode}</td>
                </tr>`;
  });

  return trString;
};

const sendEmailWithAttachment = async (req, res, next) => {
  try {
      // Handle file upload
      upload(req, res, async function (err) {
        if (err) {
          return res.status(400).send("Error uploading file.");
        }

        const file = req.file;

        if (!file) {
          return res.status(400).send("No file uploaded.");
        }

        let pool = await sql.connect(config.sql);
        const sqlQueries = await utils.loadSqlQueries("suppliers");

        const prophet_id = req.body.prophet_id;
        const isInternal = req.body.isInternal;
        const isPackagingRequest = req.body.isPackagingRequest;
        const exporterEmail = req.body.exporterEmail;
        const requestorEmail = req.body.requestorEmail;
        const isProductRequest = req.body.isProductRequest;
        const onProductSubmit = req.body.onProductSubmit;
        const request_no = req.body.request_no;
        const productEmailCommentPlaceholder =
          req.body.productEmailCommentPlaceholder;
        const productEmailParagraph = req.body.productEmailParagraph;
        const { supplierNames, isEmergencyAndFinanceNotCompleteObj } = req.body;

        const suppliersArray = JSON.parse(supplierNames);
        const emergencySuppliersArray = JSON.parse(
          isEmergencyAndFinanceNotCompleteObj
        ).map((supplier) => supplier.supplierName?.trim());
        let isHighImportance = false;

        const uniqueSuppliersSet = new Set();
        emergencySuppliersArray.forEach((name) => uniqueSuppliersSet.add(name));
        suppliersArray.forEach(({ supplierName }) =>
          uniqueSuppliersSet.add(supplierName)
        );
        const uniqueSuppliersArray = Array.from(uniqueSuppliersSet)
          .map((name) => {
            return suppliersArray.find(
              (supplier) => supplier.supplierName === name
            );
          })
          .filter(Boolean);
        const suppliersTable = getEmailBody(
          uniqueSuppliersArray,
          emergencySuppliersArray
        );

        let recipientEmails;
        let ccEmail;
        console.log("requestor email", requestorEmail);
        console.log("exporter email", exporterEmail);

        if (requestorEmail == exporterEmail) {
          ccEmail = `${requestorEmail};<EMAIL>`;
        } else {
          if (requestorEmail) {
            ccEmail = `${requestorEmail};<EMAIL>`;
          } else if (exporterEmail) {
            ccEmail = `${exporterEmail};<EMAIL>`;
          } else {
            ccEmail = `${requestorEmail};${exporterEmail};<EMAIL>`;
          }
        }

        if (isProductRequest == "true" || onProductSubmit == "true") {
          // let recipient = await pool
          //   .request()
          //   .input("prophet_id", sql.Int, 5)
          //   .input("section_id", sql.Int, 5)
          //   .query(sqlQueries.getEmailRecipients);
          // recipientEmails = recipient.recordset[0]?.recipients;
          recipientEmails = "<EMAIL>";
        } else if (isInternal == "true") {
          // let recipient = await pool
          //   .request()
          //   .input("prophet_id", sql.Int, prophet_id)
          //   .input("section_id", sql.Int, 5)
          //   .query(sqlQueries.getEmailRecipients);

          // recipientEmails = recipient.recordset[0]?.recipients;
          recipientEmails = "<EMAIL>";
        } else if (isInternal == "false") {
          // let recipient = await pool
          //   .request()
          //   .input("prophet_id", sql.Int, 5)
          //   .input("section_id", sql.Int, 5)
          //   .query(sqlQueries.getEmailRecipients);
          // recipientEmails = recipient.recordset[0]?.recipients;
          recipientEmails = "<EMAIL>";
        } else if (isPackagingRequest || isPackagingRequest == "true") {
          recipientEmails = exporterEmail;
        }

        const company = req.body.company;
        const name = req.body.name;

        let placeholders = {
          User: name || "User",
          SuppliersTable: suppliersTable,
        };

        if (emergencySuppliersArray.length > 0) {
          isHighImportance = true;
          placeholders.emergencyMessage = `
            <p style='color: red;'>*Suppliers highlighted in red are urgent requests with financial information approved/unapproved</p>
          `;
        } else {
          placeholders.emergencyMessage = "";
        }

        let emailType="exportedSupplier";
        if (onProductSubmit == "true" && isPackagingRequest!="true") {
          emailType = "newRawMaterialRequest";
          placeholders.paragraph = productEmailParagraph;
          placeholders.comment = productEmailCommentPlaceholder;
        } else if (isPackagingRequest==true || isPackagingRequest == "true") {
          emailType = "exportedPackagingRequest";
          placeholders.paragraph = productEmailParagraph;
          placeholders.comment = productEmailCommentPlaceholder;
        }
        let companyKey = company;

        const result = await sendEmail({
          placeholders,
          emailType,
          recipientEmails,
          ccEmail,
          companyKey,
          isHighImportance,
          attachment: {
            filename: file.originalname,
            content: file.buffer,
          },
          request_no,
        });

        res.status(200).send(result);
      });
  } catch (error) {
    console.log("email code: ERROR: ", error);
    res.status(400).send(error.message);
  }
};

module.exports = {
  sendEmailWithAttachment,
};
