import FinishedProductRequest from "@/components/FinishedProductRequest";
import { apiConfig } from "@/services/apiConfig";
import React, { useEffect, useState } from "react";
import { ThreeCircles } from "react-loader-spinner";

const index = ({ userData }) => {
  const [dropdowns, setDropdowns] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      const serverAddress = apiConfig.serverAddress;

      try {
        const allDropDowns = [
          "reasonForRequest",
          "masterProductCode",
          "markVariety",
          "brand",
          "endCustomer",
          "classifiedAllergicTypes",
          "machineFormat",
          "newOuterBoxType",
          "temperatureGrade",
          "packagingTypes",
        ];

        const res = await fetch(
          `${serverAddress}products/get-products-dropdowns-list`,
          {
            method: "POST",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `Bearer ${userData.token}`,
            },
            body: JSON.stringify(allDropDowns),
          }
        );

        const allDropdownsList = await res.json();
        setDropdowns(allDropdownsList);
      } catch (error) {
        console.error("Error fetching data", error);
      }
    };

    fetchData();
  }, []);

  return (
    <>
      {!dropdowns ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "100vh",
          }}
        >
          <ThreeCircles
            color="#002D73"
            height={100}
            width={100}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      ) : (
        <FinishedProductRequest
          dropdowns={dropdowns}
          userData={userData}
          pageType={"add"}
        />
      )}
    </>
  );
};

export default index;

export const getServerSideProps = async (context) => {
  const userData = context.req.cookies.user; // Access the "user" cookie

  try {
    return {
      props: {
        userData: JSON.parse(userData),
      },
    };
  } catch (error) {
    console.error("Error fetching data", error);
    return {
      props: {
        userData: null,
      },
    };
  }
};
