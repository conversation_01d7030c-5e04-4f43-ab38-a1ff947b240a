IF @type_id = 3 BEGIN;

WITH LatestActionDraft AS (
  SELECT
    id AS latest_id,
    variety_id,
    action_id AS action_id,
    actioned_by_name AS actioned_by_name,
    actioned_by_email AS actioned_by_email,
    actioned_at AS actioned_at,
    Comment AS comment,
    ROW_NUMBER() OVER (
      PARTITION BY variety_id
      ORDER BY
        id DESC
    ) AS row_num
  FROM
    product_nvariety_status
  WHERE
    action_id = 1
    AND is_active = 1
),
LatestActionSubmit AS (
  SELECT
    id AS latest_id,
    variety_id,
    action_id AS action_id,
    actioned_by_name AS actioned_by_name,
    actioned_by_email AS actioned_by_email,
    actioned_at AS actioned_at,
    Comment AS comment,
    ROW_NUMBER() OVER (
      PARTITION BY variety_id
      ORDER BY
        id DESC
    ) AS row_num
  FROM
    product_nvariety_status
  WHERE
    action_id = 2
    AND is_active = 1
)
SELECT
  PNV.id,
  PNVS.action_id,
  PNV.code,
  COALESCE(
    LASubmit.actioned_by_name,
    LADraft.actioned_by_name
  ) AS originator_name,
  COALESCE(
    LASubmit.actioned_by_email,
    LADraft.actioned_by_email
  ) AS originator_email,
  COALESCE(LASubmit.actioned_at, LADraft.actioned_at) AS delivery_date,
  PRT.[type] AS product_type,
  PRT.id AS [type_id],
  PNV.[description] AS product_name,
  MP.id AS master_product_id,
  MP.is_new AS mp_is_new,
  MP.[name] AS master_product_code,
  PNV.[description] as product_description,
  CONCAT('NV', PRN.request_no) AS request_no,
  S.[label] AS status_label,
  PRN.company AS company_name
FROM
  product_nvariety PNV
  JOIN product_request_type PRT ON PRT.id = PNV.type
  JOIN master_products MP ON MP.code = PNV.master_product_code
  and PNV.prophet_id = MP.prophet_id
  JOIN product_request_no PRN ON PRN.id = PNV.request_no
  JOIN product_nvariety_status PNVS ON PNVS.variety_id = PNV.id
  AND PNVS.is_latest = 1
  JOIN product_nvariety_actions PNVA ON PNVA.id = PNVS.action_id
  JOIN [status] S ON S.id = PNVA.status_id
  LEFT JOIN LatestActionDraft LADraft ON LADraft.variety_id = PNV.id
  AND LADraft.row_num = 1
  LEFT JOIN LatestActionSubmit LASubmit ON LASubmit.variety_id = PNV.id
  AND LASubmit.row_num = 1
WHERE
  PNV.is_active = 1
  AND MP.is_active = 1
  AND PNVA.is_active = 1
  AND (
    PRN.company = @company
    OR @company IN ('flrs', 'thl', 'issproduce')
  )
  AND PNV.type = @type_id
ORDER BY
  PNV.id DESC;

END
ELSE IF @type_id = 4 BEGIN WITH LatestActionDraft AS (
  SELECT
    id AS latest_id,
    packaging_request_id,
    action_id AS action_id,
    actioned_by_name AS actioned_by_name,
    actioned_by_email AS actioned_by_email,
    actioned_at AS actioned_at,
    Comment AS comment,
    ROW_NUMBER() OVER (
      PARTITION BY packaging_request_id
      ORDER BY
        id DESC
    ) AS row_num
  FROM
    product_packaging_status
  WHERE
    action_id = 1
    AND is_active = 1
),
LatestActionSubmit AS (
  SELECT
    id AS latest_id,
    packaging_request_id,
    action_id AS action_id,
    actioned_by_name AS actioned_by_name,
    actioned_by_email AS actioned_by_email,
    actioned_at AS actioned_at,
    Comment AS comment,
    ROW_NUMBER() OVER (
      PARTITION BY packaging_request_id
      ORDER BY
        actioned_at ASC, -- Order by timestamp ascending for the first submit
        id ASC           -- Then by ID ascending for tie-breaking (earliest ID if timestamps are same)
    ) AS row_num
  FROM
    product_packaging_status
  WHERE
    action_id = 2
    AND is_active = 1
),
LatestActionIss AS (
  SELECT
    id AS latest_id,
    packaging_request_id,
    action_id AS action_id,
    actioned_by_name AS actioned_by_name,
    actioned_by_email AS actioned_by_email,
    actioned_at AS actioned_at,
    Comment AS comment,
    ROW_NUMBER() OVER (
      PARTITION BY packaging_request_id
      ORDER BY
        id DESC
    ) AS row_num
  FROM
    product_packaging_status
  WHERE
    action_id = 3
    AND is_active = 1
)
SELECT
  PPR.id,
  PPR.change_launch_date,
  PPR.colour_of_material,
  PPR.[component_weight(g)],
  PPR.[dimension_size(mm)],
  pRes.id as reason_id,
  pRes.reason as reason_description,
  PPR.end_customer,
  PPR.existing_packaging_code,
  PPR.packaging_name as product_description,
  PPR.paper_from_sustainable_forestry,
  PPR.recyclable_content,
  PPR.recyclable_to_oprl,
  PPR.supplier,
  PPR.trading_business,
  PPR.type_of_material,
  PPR.type_of_packaging,
  CONCAT('PK', PRN.request_no) AS request_no,
  MP.id AS master_product_id,
  MP.name AS master_product_name,
  MP.code AS master_product_code,
  CASE
    WHEN S.[label] = 'Pending Review' THEN 'To be Setup'
    ELSE S.[label]
  END AS status_label,
  -- S.[label] AS status_label,
  COALESCE(
    LASubmit.actioned_by_name,
    LADraft.actioned_by_name
  ) AS originator_name,
  COALESCE(
    LASubmit.actioned_by_email,
    LADraft.actioned_by_email
  ) AS originator_email,
  COALESCE(LASubmit.comment, LADraft.comment) AS originator_comment,
  COALESCE(LASubmit.actioned_at, LADraft.actioned_at) AS delivery_date,
  LAIss.actioned_by_name AS completed_by_name,
  LAIss.actioned_by_email AS completed_by_email,
  LAIss.comment AS completed_by_comment,
  LAIss.actioned_at AS completed_at,
  PRN.company AS company_name,
  PRN.id AS request_id,
  PRT.[type] AS product_type,
  PRT.id AS type_id,
  PPS.action_id -- ,S.[label] AS reason_description
FROM
  product_packaging_request PPR
  LEFT JOIN product_request_type PRT ON PRT.id = 4
  LEFT JOIN master_products MP ON MP.id = PPR.master_product_code_id
  AND MP.prophet_id = 5
  AND MP.is_active = 1
  LEFT JOIN product_request_no PRN ON PRN.id = PPR.request_no
  LEFT JOIN product_packaging_reason pRes ON pRes.id = PPR.reason
  LEFT JOIN product_packaging_status PPS ON PPS.packaging_request_id = PPR.id
  AND PPS.is_latest = 1
  LEFT JOIN product_packaging_actions PPA ON PPA.id = PPS.action_id AND PPA.is_active = 1
  LEFT JOIN [status] S ON S.id = PPA.status_id
  LEFT JOIN LatestActionDraft LADraft ON LADraft.packaging_request_id = PPR.id
  AND LADraft.row_num = 1
  LEFT JOIN LatestActionSubmit LASubmit ON LASubmit.packaging_request_id = PPR.id
  AND LASubmit.row_num = 1
  LEFT JOIN LatestActionIss LAIss ON LAIss.packaging_request_id = PPR.id
  AND LAIss.row_num = 1
WHERE
  (
    PRN.company = @company
    OR @company IN ('flrs', 'thl', 'issproduce')
  )
  AND PRT.id = @type_id
ORDER BY
  PPR.id DESC;

END
ELSE BEGIN
SELECT
  p.id,
  p.originator,
  p.originator_email,
  pt.type AS product_type,
  pt.id AS type_id,
  mp.name AS product_name,
  mp.id AS master_product_id,
  mp.is_new AS mp_is_new,
  mp.code AS master_product_code,
  pg.group_name AS group_name,
  pg.is_new AS group_is_new,
  pg.group_id As group_id,
  b.name AS brand_name,
  b.id AS brand_id,
  b.is_new AS b_is_new,
  b.code AS brand_code,
  cs.name AS caliber_size_name,
  cs.id AS caliber_size_id,
  cs.is_new AS cs_is_new,
  cs.code AS caliber_size_code,
  ec.name AS end_customer_name,
  ec.id AS end_customer_id,
  ec.is_new AS ec_is_new,
  ec.code AS ec_code,
  v.description,
  v.id AS variety_id,
  v.is_new AS v_is_new,
  v.code AS v_code,
  p.product_description,
  p.suppliers_description,
  p.count_or_size,
  p.units_in_outer,
  p.cases_per_pallet,
  p.outer_net_weight,
  p.outer_gross_weight,
  p.sub_product_code,
  p.temperature_grade,
  p.class_required,
  p.intrastat_commodity_code,
  p.organic_certificate,
  p.is_classified_allergic_fsa14,
  coc.name as coo,
  coc.id as coc_id,
  coc.is_new as coc_is_new,
  coc.code as coc_code,
  p.reason as reason_id,
  r.reason as reason_description,
  tg.name as temperature_grade_name,
  tg.id as temperature_grade_id,
  icc.name as intrastat_commodity_code_name,
  icc.code as intrastat_commodity_code_id,
  icc.is_new as intrastat_commodity_code_is_new,
  icc.User_text_4 as userText4,
  icc.User_text_5 as userText5,
  icc.User_text_6 as userText6,
  p.requestor,
  p.[email_comment],
  mv.id AS mark_variety_id,
  mv.name AS mark_variety_name,
  mv.is_new AS mark_variety_is_new,
  p.request_no,
  p.delivery_date,
  p.submitted_to_iss,
  p.status,
  p.cancelled_date,
  p.cancelled_reason,
  p.cancelled_by,
  s.label AS status_label,
  pdt.value as product_type_id,
  pdt.label as product_type_label,
  p.company_name
FROM
  [dbo].[products] p
  LEFT JOIN product_request_type pt ON p.type = pt.id
  LEFT JOIN master_products mp ON p.product_code = mp.id
  LEFT JOIN brands b ON p.brand = b.id
  LEFT JOIN caliber_size cs ON p.caliber_size = cs.id
  LEFT JOIN end_customers ec ON p.end_customer = ec.id
  LEFT JOIN product_nvariety v ON p.variety = v.id
  LEFT JOIN mark_variety mv ON p.mark_variaty = mv.id
  LEFT JOIN reason r ON p.reason = r.id
  LEFT JOIN temperature_grade tg ON p.temperature_grade = tg.id
  LEFT JOIN commodity_codes icc ON p.intrastat_commodity_code = icc.id
  LEFT JOIN country_of_origin coc ON p.coo = coc.id
  LEFT JOIN product_groups pg ON pg.group_id = p.group_id
  AND pg.type = p.type
  LEFT JOIN status s ON p.status = s.id
  LEFT JOIN product_type pdt ON p.product_type = pdt.id
WHERE
  (
    @fetch_all = 1
    OR (
      @fetch_all != 1
      and p.company_name = @company
    )
  )
  AND p.type = @type_id
ORDER BY
  p.[id] desc;

END