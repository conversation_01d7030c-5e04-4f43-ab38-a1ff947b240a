SELECT
    s.[id],
    s.[name],
    c.[symbol] As currency,
    s.[requestor_name],
    s.[requestor_email],
    s.[compliance],
    s.[financial],
    s.[technical],
    s.[procurement],
    s.[emergency_request],
    s.[gdpr_compliant],
    s.[product_supplier],
    s.[trading_name],
    s.[email_id],
    s.[facsimile],
    s.[telephone],
    s.[address_line_1],
    s.[address_line_2],
    s.[address_line_3],
    s.[address_line_4],
    s.[country],
    s.[postal_code],
    s.[vat_number],
    s.[company_registration],
    s.[country_code],
    s.[payment_terms],
    s.[payment_type],
    s.[validated_procurement_team],
    s.[validated_date],
    s.[validated_by],
    s.[finance_authorization],
    s.[finance_authorization_date],
    s.[finance_authorization_by],
    s.[rejected_reason],
    s.[red_tractor],
    s.[puc_code],
    s.[chile_certificate_number],
    s.[organic_certificate_number],
    s.[global_gap_number],
    s.[customer_site_code],
    s.[created_date],
    s.[updated_date],
    s.[status],
    ss.[label],
    STUFF(
        (
            SELECT
                ', ' + spp_inner.[prophet_name]
            FROM
                [dbo].[supplier_prophets] sp_inner
                JOIN [dbo].[prophet_system] spp_inner ON sp_inner.prophet_id = spp_inner.id
            WHERE
                sp_inner.supplier_id = s.id FOR XML PATH(''),
                TYPE
        ).value('.', 'NVARCHAR(MAX)'),
        1,
        2,
        ''
    ) AS prophet_names,
    STUFF(
        (
            SELECT
                ', ' + srr_inner.[role_name]
            FROM
                [dbo].[supplier_roles] sr_inner
                JOIN [dbo].[supplier_role] srr_inner ON sr_inner.role_id = srr_inner.id
            WHERE
                sr_inner.supplier_id = s.id FOR XML PATH(''),
                TYPE
        ).value('.', 'NVARCHAR(MAX)'),
        1,
        2,
        ''
    ) AS role_names,
    STUFF(
        (
            SELECT
                ', ' + CAST(srr_inner.[role_num] AS VARCHAR(255)) -- Adjust the length based on your actual data
            FROM
                [dbo].[supplier_roles] sr_inner
                JOIN [dbo].[supplier_role] srr_inner ON sr_inner.role_id = srr_inner.id
            WHERE
                sr_inner.supplier_id = s.id FOR XML PATH(''),
                TYPE
        ).value('.', 'NVARCHAR(MAX)'),
        1,
        2,
        ''
    ) AS role_nums,
    (
        SELECT
            sr_inner.[role_id]
        FROM
            [dbo].[supplier_roles] sr_inner
        WHERE
            s.id = sr_inner.supplier_id FOR JSON PATH
    ) AS role_ids,
    (
        SELECT
            sp_inner.[prophet_id],
            sp_inner.[prophet_code]
        FROM
            [dbo].[supplier_prophets] sp_inner
        WHERE
            s.id = sp_inner.supplier_id FOR JSON PATH
    ) AS prophet_ids,
    (
        SELECT
            ssg.[supplier_id],
            (
                SELECT
                    s.[name]
                FROM
                    [dbo].[supplier] s
                WHERE
                    ssg.supplier_id = s.id
            ) AS name,
            srs.[role_id],
            STUFF(
                (
                    SELECT
                        ', ' + CAST(spr.role_name AS VARCHAR(100)) [text()]
                    FROM
                        [dbo].[supplier_role] spr
                    WHERE
                        spr.id = srs.role_id
                        and ssg.supplier_id = srs.supplier_id FOR XML PATH(''),
                        TYPE
                ).value('.', 'NVARCHAR(MAX)'),
                1,
                2,
                ' '
            ) role_names
        FROM
            [dbo].[supplier_sendac_group] ssg
            LEFT JOIN [dbo].[supplier_roles] srs ON ssg.supplier_id = srs.supplier_id
        WHERE
            ssg.group_id = (
                SELECT
                    distinct group_id
                FROM
                    [dbo].[supplier_sendac_group]
                WHERE
                    supplier_id = s.id
            ) FOR JSON PATH
    ) AS supplier_links_json,
    (
        SELECT
            spgs.[group_id],
            sg.[label]
        FROM
            [dbo].[supplier_sendac_group] spgs
            LEFT JOIN [dbo].[sendac_groups] sg ON sg.id = spgs.group_id
        WHERE
            s.id = spgs.supplier_id FOR JSON PATH
    ) AS sendac_groups_json
FROM
    [dbo].[Supplier] s
    LEFT JOIN [dbo].[default_terms_agreed_yield] dtay ON s.id = dtay.supplier_id
    LEFT JOIN [dbo].[status] ss ON s.status = ss.id
    LEFT JOIN [dbo].[currencies] c ON c.id = s.currency
WHERE
    EXISTS (
        SELECT
            1
        FROM
            supplier_sendac_group
        WHERE
            group_id = @linkedSupId
            AND (
                s.id = supplier_sendac_group.supplier_id
                OR s.id = supplier_sendac_group.supplier_id
            )
    )
GROUP BY
    s.[id],
    s.[name],
    s.[currency],
    s.[compliance],
    s.[requestor_name],
    s.[requestor_email],
    s.[financial],
    s.[technical],
    s.[procurement],
    s.[emergency_request],
    s.[gdpr_compliant],
    s.[product_supplier],
    s.[trading_name],
    s.[email_id],
    s.[facsimile],
    s.[telephone],
    s.[address_line_1],
    s.[address_line_2],
    s.[address_line_3],
    s.[address_line_4],
    s.[country],
    s.[postal_code],
    s.[vat_number],
    s.[company_registration],
    s.[country_code],
    s.[payment_terms],
    s.[payment_type],
    s.[validated_procurement_team],
    s.[validated_date],
    s.[validated_by],
    s.[finance_authorization],
    s.[finance_authorization_date],
    s.[finance_authorization_by],
    s.[rejected_reason],
    s.[red_tractor],
    s.[puc_code],
    s.[chile_certificate_number],
    s.[organic_certificate_number],
    s.[global_gap_number],
    s.[customer_site_code],
    s.[created_date],
    s.[updated_date],
    s.[status],
    ss.[label],
    c.[symbol]
ORDER BY
    s.id DESC;