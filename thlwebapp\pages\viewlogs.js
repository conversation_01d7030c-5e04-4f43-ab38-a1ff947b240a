import Layout from "@/components/Layout";
import React, {
  useMemo,
  useState,
  useRef,
  useCallback,
  useEffect,
  Fragment,
} from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles//ag-grid.css";
import "ag-grid-community/styles//ag-theme-alpine.css";
import { useRouter } from "next/router";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useLoading } from "@/utils/loaders/loadingContext";
import { logout } from "@/utils/secureStorage";
import { format } from "date-fns";
import { faSearch } from "@fortawesome/free-solid-svg-icons";

const viewlogs = ({ userData }) => {
  const router = useRouter();
  const [pageSize, setPageSize] = useState(15);
  const [rowData, setRowData] = useState([]);
  const gridRef = useRef();
  const { setIsLoading } = useLoading();
  const [isCommonError, setCommonError] = useState("");
  const [company, setCompany] = useState("");
  const [modules, setModules] = useState([]);
  const [selectedModule, setSelectedModule] = useState("");
  const [originalRowData, setOriginalRowData] = useState([]);

  const onFilterTextBoxChanged = useCallback(() => {
    gridRef.current.api.setQuickFilter(
      document.getElementById("filter-text-box").value
    );
  }, []);

  const handleModuleChange = (e) => {
    const selectedModule = e.target.value;
    setSelectedModule(selectedModule);

    if (selectedModule === "All") {
      setRowData(originalRowData);
    } else {
      const filteredData = originalRowData.filter(
        (row) => row.module === selectedModule
      );
      setRowData(filteredData);
    }
  };

  useEffect(() => {
    setIsLoading(false);
    // Set company from session data instead of cookies
    setCompany(userData?.company || "");
    
    getData().then((data) => {
      if (data) {
        const formattedData = data?.logs?.map((row) => ({
          username: row.username,
          description: row.description,
          date_time: new Date(row.date)
            .toISOString()
            .replace("Z", "")
            .replace("T", " "),
          type: row.type,
          item_id: row.item_id,
          module: row.module_name,
          name: row.name,
        }));
        setRowData(formattedData);
        setOriginalRowData(formattedData);
        let modulesList = [{ id: 0, module_name: "All" }, ...data?.modules];
        setModules(modulesList);
      }
    });
  }, []);

  async function getData() {
    try {
      const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8081';
      
      const response = await fetch(`${apiBase}/api/logs/get-logs`, {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        credentials: 'include', // Use session authentication
      });

      if (response.status === 400 || response.status === 401) {
        toast.error("Your session has expired. Please log in again.");
        setTimeout(async () => {
          await logout();
          router.push('/login');
        }, 3000);
        return null;
      }

      if (response.status === 200) {
        return await response.json();
      }

      throw new Error("Failed to fetch data");
    } catch (error) {
      console.error("Get logs error:", error);
      setCommonError(error.message);
      toast.error("Failed to fetch logs", {
        position: "top-right",
      });
      return null;
    }
  }

  const handlePageSizeChange = (event) => {
    const newPageSize = parseInt(event.target.value, 15);
    setPageSize(newPageSize);
    gridRef.current.api.paginationSetPageSize(newPageSize);
  };

  const defaultColDef = useMemo(() => ({
    sortable: true,
    filter: false,
    resizable: true,
    flex: 1,
    suppressMenu: false,
  }));
  
  const columnDefs = [
    {
      headerName: "Username",
      field: "username",
      flex: "2%",
    },
    {
      headerName: "Item",
      field: "name",
      flex: "2%",
    },
    {
      headerName: "Module",
      field: "module",
      flex: "2%",
    },
    {
      headerName: "Description",
      field: "description",
      flex: "8%",
      wrapText: true,
      autoHeight: true,
      cellRenderer: (params) => {
        return <div style={{ wordBreak: "break-word" }}> {params.value} </div>;
      },
    },
    {
      headerName: "Date & time",
      field: "date_time",
      flex: "3%",
      cellRenderer: (params) => {
        const dateTime = new Date(params.value);
        const convertDate = format(new Date(dateTime), "yyyy/MM/dd HH:mm:ss");
        return convertDate;
      },
    },
    {
      headerName: "Type",
      field: "type",
      flex: "2%",
      cellRenderer: (params) => {
        const type = params.value;
        let color = "";
        if (type == "success") {
          color = "bg-complete-status p-2 rounded-md text-[#ffffff] capitalize";
        } else {
          color = "bg-red-500 p-2 rounded-md text-[#ffffff] capitalize";
        }
        return <span className={color}>{type}</span>;
      },
    },
    {
      field: "item_id",
      hide: true,
    },
  ];

  const handleSupplierChange = (data) => {
    gridRef.current.api.setQuickFilter(data ? data.value.toString() : "");
  };

  return (
    <>
      <ToastContainer limit={1} />
      <Layout userData={userData}>
        <div className="mr-20 md:mr-12 lg:mr-14">
          <div className="flex w-1/2 space-x-2">
            <label className="relative block w-[47vh] text-gray-400 mt-0 pt-0 mb-2">
              <span className="absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black">
                <FontAwesomeIcon icon={faSearch} className="fw-bold" />
              </span>
              <input
                type="text"
                id="filter-text-box"
                placeholder="Search"
                onInput={onFilterTextBoxChanged}
                className="block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none"
              />
            </label>
            <label className="">
              Module:{" "}
              <select
                id="page-size-select"
                onChange={handleModuleChange}
                value={selectedModule}
                className="focus:outline-none"
              >
                {modules.map((module, index) => (
                  <option key={index} value={module.module_name}>
                    {module.module_name}
                  </option>
                ))}
              </select>{" "}
            </label>
          </div>
          <div
            className="relative ag-theme-alpine !rounded-md viewlog"
            style={{ height: "calc(100vh - 145px)" }}
          >
            <AgGridReact
              rowData={rowData}
              ref={gridRef}
              columnDefs={columnDefs}
              defaultColDef={defaultColDef}
              suppressRowClickSelection
              pagination={true}
              paginationPageSize={pageSize}
              onPageSizeChanged={handlePageSizeChange}
              includeHiddenColumnsInQuickFilter={true}
            />
            <div className="flex justify-start mt-2 pagination-style">
              <label htmlFor="page-size-select pagination" className="inputs">
                Show{" "}
                <select
                  id="page-size-select"
                  onChange={handlePageSizeChange}
                  value={pageSize}
                  className="focus:outline-none"
                >
                  <option value={10}>10</option>
                  <option value={15}>15</option>
                  <option value={25}>25</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>{" "}
                entries
              </label>
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default viewlogs;

export const getServerSideProps = async (context) => {
  try {
    // Use secure session validation
    const sessionId = context.req.cookies.thl_session;
    
    if (!sessionId) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
          permanent: false,
        },
      };
    }

    // Validate session with our backend API
    const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8081';
    
    try {
      const response = await fetch(`${apiBase}/api/auth/me`, {
        method: 'GET',
        headers: {
          'Cookie': `thl_session=${sessionId}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        // Session invalid or expired - redirect to login
        return {
          redirect: {
            destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
            permanent: false,
          },
        };
      }

      const { user } = await response.json();
      
      // Check if user has permission to access viewlogs page
      // Typically all authenticated users can view logs, but you can add role restrictions here if needed
      // For now, allowing all authenticated users
      const allowedRoles = [1, 5, 6];
      if (!allowedRoles.includes(user.role_id)) {
        return {
          redirect: {
            destination: '/unauthorized',
            permanent: false,
          },
        };
      }
      return {
        props: {
          userData: user,
        },
      };

    } catch (fetchError) {
      console.error('Session validation failed:', fetchError);
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
          permanent: false,
        },
      };
    }

  } catch (error) {
    console.error('Authentication error:', error);
    return {
      redirect: {
        destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
        permanent: false,
      },
    };
  }
};
