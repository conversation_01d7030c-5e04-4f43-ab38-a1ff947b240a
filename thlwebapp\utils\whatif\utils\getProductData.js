import { apiConfig } from "@/services/apiConfig";
import { logout } from "@/utils/secureStorage";
import { toast } from "react-toastify";

export async function getData(url, token) {
  return await fetch(`${apiConfig.serverAddress}whatif/${url}`, {
    method: "GET",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },
    credentials: "include",
  })
    .then(async (res) => {
      if (res.status === 400) {
        toast.error(
          "There was an error with your request. Please check your data and try again."
        );
        return [];
      } else if (res.status === 401) {
        return null;
      }
      if (res.status === 200) {
        return res.json();
      }
      throw new Error("Failed to fetch data");
    })
    .catch((error) => {
      console.log(error);
    });
}
