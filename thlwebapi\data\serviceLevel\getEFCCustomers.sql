select
    distinct cust_customer.hocustcode as [value],
    cust_customer.hocustcode as [label]
from
    [FLR_DEV_TEST_off_BI_lookup].[dbo].dm_sales_totals sales
    join [FLR_DEV_TEST_off_BI_lookup].[dbo].dm_customer cust_customer on cust_customer.custcode = sales.custcode
    join [FLR_DEV_TEST_off_BI_lookup].[dbo].dm_customer delcust_customer on sales.delcustcode = delcust_customer.custcode
WHERE
    case
        when cust_customer.custcode <> 'OFF' then cust_customer.hocustcode
        else delcust_customer.hocustcode
    end NOT IN ('PPACK', 'REJISS', 'SMOVE')
    AND altfilid > 0
    AND delivery_date >= @start_date
    AND delivery_date <= @end_date
    AND cust_customer.category_no = 1
    AND (
        (
            delivery_date between '********'
            and '********'
            AND cust_customer.hocustcode NOT IN ('ASSAP', 'CWSUK', 'NISAUK')
        )
        OR (
            delivery_date between '********'
            and @end_date
            AND cust_customer.hocustcode NOT IN ('CWSUK', 'NISAUK')
        )
    ) -- shortage accounts for october 2023 to september 2024
order by
    cust_customer.hocustcode