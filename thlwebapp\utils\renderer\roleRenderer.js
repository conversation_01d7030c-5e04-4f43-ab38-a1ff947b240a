import React, { useMemo } from "react";

const colorMap = {
  1: {
    background: "rgba(62, 171, 88, 0.2)",
    text: "#3EAB58",
  },
  2: {
    background: "rgba(0, 102, 255, 0.2)",
    text: "#0066FF",
  },
  4: {
    background: "rgb(211, 211, 211, 0.7)",
    text: "#000000",
  },
  5: {
    background: "rgba(62, 171, 88, 0.2)",
    text: "#3EAB58",
  },
  6: {
    background: "rgba(62, 171, 88, 0.2)",
    text: "#3EAB58",
  },
  default: {
    background: "rgba(154, 154, 154, 0.2)",
    text: "#9A9A9A",
  },
};

const roleRenderer = (params) => {
  const role = params.value;
  const { background, text } = useMemo(
    () => colorMap[role] || colorMap.default,
    [role]
  );
  // if (role === 4) {
  //   return null; // Return null to hide the component
  // }

  const spanStyle = {
    backgroundColor: background,
    width: "95px",
    textAlign: "center",
    display: "inline-block",
    verticalAlign: "middle",
    lineHeight: "24px",
    height: "32px",
    color: text,
    padding: "6px",
    borderRadius: "10px",
  };

  return (
    <span style={spanStyle}>
      {role == 1
        ? "Administrator"
        : role === 2
        ? "Approver"
        : role === 5
        ? "THL Admin"
        : role === 6
        ? "Super Admin"
        : "User"}
    </span>
  );
};

export default roleRenderer;
