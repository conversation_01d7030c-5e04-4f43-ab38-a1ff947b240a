import { useMemo } from "react";

const colorMap = {
  reason: {
    New: "#3255F4",
    Change: "#49BB7F",
    Replacement: "#ff7f00",
    Contingency: "#49B47F",
    default: "#9A9A9A",
  },
  type: {
    RM: "#0066FF",
    FG: "#25AE65",
    PK: "#49BB7F",
    default: "#9A9A9A",
  },
  status: {
    Complete: "#fff",
    New: "#fff",
    Cancelled: "#fff",
    Contingency: "#fff",
    default: "#fff",
  },
};


  const statusColumnStyles = {
    Completed: {
      width: "140px",
      backgroundColor: "#25AE65",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
      color: "white",
    },
    Replacement: {
      width: "190px",
      color: "white",
      backgroundColor: "#ff7f00", // Orange
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
    },
    Submitted: {
      width: "140px",
      backgroundColor: "#25AE65",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
      color: "white",
    },
    Updated: {
      width: "140px",
      color: "white",
      backgroundColor: "#0066FF",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
    },
    New: {
      width: "140px",
      backgroundColor: "#54C5ED",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
    },
    Cancelled: {
      width: "140px",
      backgroundColor: "#FF6D29",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
    },
    Draft: {
      width: "140px",
      backgroundColor: "#54C5ED",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
      color: "white",
    },
    "Pending Review": {
      backgroundColor: "#0066FF",
      width: "140px",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
      color: "white",
    },
    "ISS to Setup": {
      width: "140px",
      color: "white",
      backgroundColor: "#6E3EAB",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
    },
    "Prophet to Setup": {
      width: "140px",
      color: "white",
      backgroundColor: "#AB6E3E",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
    },
    Rejected: {
      width: "140px",
      color: "white",
      backgroundColor: "#F93647",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
    },
    "Prophet Setup Completed": {
      width: "140px",
      color: "white",
      backgroundColor: "#25AE65",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
    },
    Contingency: {
      width: "140px",
      color: "white",
      backgroundColor: "#F93647",
      borderRadius: "5px",
      padding: "10px",
      textAlign: "center",
    },
    "To be Setup": {
      width: "140px",
      color: "white",
      backgroundColor: "#6E3EAB",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
    },
    "Setup Complete": {
      width: "140px",
      color: "white",
      backgroundColor: "#F93647",
      borderRadius: "5px",
      padding: "5px",
      textAlign: "center",
    },
  };

const productStatusRenderer = (params) => {
const isStatusField = params.colDef.field === "status";
const isReasonField = params.colDef.field === "reason";

const color = useMemo(() => {
  const fieldMap = colorMap[params.colDef.field];
  const normalizedValue = params.value?.trim?.();
  return fieldMap ? fieldMap[normalizedValue] || fieldMap.default : undefined;
}, [params.colDef.field, params.value]);

let valueToDisplay;

if (params.value === "Prophet Setup Completed") {
  if (params.data.company === "dpsltd" || params.data.company === "iss") {
    valueToDisplay = "ISS Setup Completed";
  } else if (params.data.company === "efcltd") {
    valueToDisplay = "EFC Setup Completed";
  } else if (params.data.company === "fpp-ltd") {
    valueToDisplay = "FPP Setup Completed";
  } else {
    valueToDisplay = "Setup Completed";
  }
} else if (params.value === "Prophet to Setup") {
  if (params.data.company === "efcltd") {
    valueToDisplay = "EFC to Setup";
  } else if (params.data.company === "fpp-ltd") {
    valueToDisplay = "FPP to Setup";
  } else {
    valueToDisplay = "ISS to Setup";
  }
} else {
  valueToDisplay = params.value;
}

  const spanStyle = {
    width: "90px",
    textAlign: "left",
    display: "inline-block",
    verticalAlign: "middle",
    lineHeight: "24px",
    height: "32px",
    ...(isStatusField ? statusColumnStyles[params.value] || {} : { color }),
    ...(isReasonField && {
      border: `1px solid ${color}`,
      borderRadius: "0.375rem",
      textAlign: "center",
    }),
  };

  return <span style={spanStyle}>{valueToDisplay}</span>;
};

export default productStatusRenderer;
