import React, { useEffect, useState } from "react";
import Layout from "@/components/Layout";
import {
  FluentProvider,
  SSRProvider,
  webLightTheme,
} from "@fluentui/react-components";
import { ModalProvider } from "../../components/whatif/providers/ModalProvider";
import { CurrencyProvider } from "../../components/whatif/providers/CurrencyProvider";
import { getData } from "@/utils/whatif/utils/getProductData";
import Root from "../../components/whatif/Root";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Cookies from "js-cookie";
import { useLoading } from "@/utils/loaders/loadingContext";
import { logout } from "@/utils/secureStorage";
import { useRouter } from "next/router";

const whatif = ({
  userData,
  isTokenExpired,
  requestError,
  customerList,
  productList,
  currentQuarterCalendarData,
  customerDataTotals,
  currentQuarterStdCalendarData,
}) => {
  const router = useRouter();
  const { setIsLoading } = useLoading();

  useEffect(() => {
    if (currentQuarterCalendarData.length > 0) {
      Cookies.set("currentWeek", currentQuarterCalendarData[0].currentWeek);
    }
  }, [currentQuarterCalendarData]);

  useEffect(() => {
    if (typeof document !== "undefined") {
      document.title = "What If";
    }
    if (isTokenExpired) {
      toast.error("Your session has expired. Please log in again.");
      setTimeout(async () => {
        await logout();
        const redirectUrl = `/login?redirect=${encodeURIComponent(
          window.location.pathname
        )}`;
        router.push(redirectUrl);
      }, 3000);
    } else if (requestError) {
      toast.error(
        "There was an error with your request. Please check your data and try again."
      );
    }
  }, [isTokenExpired]);

  return (
    <Layout userData={userData}>
      <ToastContainer limit={1} />
      <SSRProvider>
        <FluentProvider
          theme={webLightTheme}
          className="!bg-transparent"
          style={{ fontFamily: "poppinsregular" }}
        >
          <CurrencyProvider>
            <ModalProvider userData={userData}>
              <Root
                userData={userData}
                customerList={customerList}
                productList={[]} // This was missing from the original getServerSideProps
                calenderData={currentQuarterCalendarData}
                customerDataTotals={customerDataTotals}
                currentQuarterStdCalendarData={currentQuarterStdCalendarData}
              />
            </ModalProvider>
          </CurrencyProvider>
        </FluentProvider>
      </SSRProvider>
    </Layout>
  );
};

export default whatif;

export const getServerSideProps = async (context) => {
  try {
    const sessionId = context.req.cookies.thl_session;
    const filters = context.req.cookies.filters
      ? JSON.parse(context.req.cookies.filters)
      : null;
    const cust_code = filters?.customer || null;

    if (!sessionId) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(
            context.resolvedUrl
          )}`,
          permanent: false,
        },
      };
    }

    // Validate session with our backend API
    const apiBase =
      process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8081";

    const response = await fetch(`${apiBase}/api/auth/me`, {
      method: "GET",
      headers: {
        Cookie: `thl_session=${sessionId}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      // Session invalid or expired - redirect to login
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(
            context.resolvedUrl
          )}`,
          permanent: false,
        },
      };
    }

    const { user } = await response.json();

    // // Check if user has permission to access users page
    const allowedDept = [1];
    const allowedCompanies = ["efcltd", "flrs", "thl"];
    if (!allowedDept.includes(user.department_id) || !allowedCompanies.includes(user.company)) {
      return {
        redirect: {
          destination: '/unauthorized',
          permanent: false,
        },
      };
    }

    let customerList = [];

    let currentQuarterCalendarData = [];
    let currentQuarterStdCalendarData = [];
    let customerDataTotals = [];
    let isTokenExpired = false;
    let requestError = false;
    const data = await fetch(
      `${apiBase}/api/whatif/get-initial-data/${cust_code}`,
      {
        method: "GET",
        headers: {
          Cookie: `thl_session=${sessionId}`,
          "Content-Type": "application/json",
        },
      }
    )
      .then(async (res) => {
        if (res.status === 401) {
          return null;
        }
        if (res.status === 400) {
          return [];
        }
        if (res.status === 200) {
          return res.json();
        }
        throw new Error("Failed to fetch data");
      })
      .catch((error) => {
        console.log(error);
        return null;
      });

    if (!data) {
      console.log("error 401");
      isTokenExpired = true;
    }
    if (data?.length == 0) {
      console.log("error 400");
      requestError = true;
    }

    if (data) {
      currentQuarterCalendarData = data?.currentQuarterCalendarData;
      currentQuarterStdCalendarData = data?.currentQuarterStdCalendarData;
      customerList = data?.whatifCustomers;
      customerDataTotals = data?.whatifTotalsByCustomers;
    }

    return {
      props: {
        isTokenExpired,
        requestError,
        userData: user,
        customerList,
        currentQuarterCalendarData,
        customerDataTotals,
        currentQuarterStdCalendarData,
      },
    };
  } catch (error) {
    console.error("Error in getServerSideProps:", error);
    return {
      redirect: {
        destination: "/login",
        permanent: false,
      },
    };
  }
};
