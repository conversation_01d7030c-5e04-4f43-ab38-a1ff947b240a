import React, { useEffect, useState } from "react";
import Select from "react-select";
import { useLoading } from "@/utils/loaders/loadingContext";
import { toast } from "react-toastify";
import { useRouter } from "next/router";
import Cookies from "js-cookie";


// ...existing code...

// Dummy site options
const sites11 = [
  { value: "site1", label: "ISS1-Teynham" },
  { value: "site2", label: "ISS2-Linton" },
  { value: "site3", label: "ISS3-Sittingbourne" },
];

// Dummy product options
const products11 = [
  {
    value: "gloves",
    label: "Nitrile Gloves",
    size_required: true,
    sizes: [
      { value: "small", label: "Small" },
      { value: "medium", label: "Medium" },
      { value: "large", label: "Large" },
    ],
  },
  {
    value: "mask",
    label: "Surgical Mask",
    size_required: false,
    sizes: [],
  },
  {
    value: "apron",
    label: "Disposable Apron",
    size_required: false,
    sizes: [],
  },
  {
    value: "boots",
    label: "Safety Boots",
    size_required: true,
    sizes: [
      { value: "size8", label: "Size 8" },
      { value: "size9", label: "Size 9" },
      { value: "size10", label: "Size 10" },
    ],
  },
];

// ...existing code...



const customSelectStyles = {
  control: (base) => ({
    ...base,
    height: "28px",
    minHeight: "28px",
  }),
  valueContainer: (provided) => ({
    ...provided,
    height: "28px",
    padding: "0 6px",
  }),
  input: (provided) => ({
    ...provided,
    margin: "0px",
  }),
  indicatorSeparator: () => ({
    display: "none",
  }),
  indicatorsContainer: (provided) => ({
    ...provided,
    height: "28px",
  }),
};

const emptyItem = {
  product: null,
  size: null,
  quantity: 1,
  nameForPrinting: "",
  comments: "",
};
const PpeConsumable = ({
  dropdowns,
  userData,
  pageType,
  user,
  products,
  sites,
}) => {
  console.log("prechant",userData);

  const [ppeType, setPpeType] = useState("");
  const [ppeCategory, setPpeCategory] = useState("");
  const [ppeSite, setPpeSite] = useState("");
  const [ppeStatus, setPpeStatus] = useState("");
  const [ppeComment, setPpeComment] = useState("");
  const [nameOfOriginator, setNameOfOriginator] = useState('')

  const [items, setItems] = useState([{ ...emptyItem }]);
  const [requiredDate, setRequiredDate] = useState("");
  const [site, setSite] = useState(sites?.[0] || null);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  // Helper to handle item changes
  const handleItemChange = (idx, field, value) => {
    setItems((prev) =>
      prev.map((item, i) => (i === idx ? { ...item, [field]: value } : item))
    );
  };
  // Add another item row
  const addItem = () => setItems((prev) => [...prev, { ...emptyItem }]);

  // Remove item row
  const removeItem = (idx) =>
    setItems((prev) => prev.filter((_, i) => i !== idx));

  // Validation (basic)
  const validate = () => {
    if (!requiredDate) return false;
    for (const item of items) {
      if (!item.product || !item.quantity || item.quantity < 1) return false;
      if (item.product.size_required && !item.size) return false;
      if (item.quantity === 1 && !item.nameForPrinting) return false;
    }
    return true;
  };

  // Submit handler
  const handleSubmit = () => {
    if (!validate()) {
      toast.error("Please fill all required fields.");
      return;
    }
    setLoading(true);
    // Submit logic here
    setTimeout(() => {
      setLoading(false);
      toast.success("Request submitted!");
      router.push("/requests");
    }, 1000);
  };

  // Clear form
  const handleClear = () => {
    setItems([{ ...emptyItem }]);
    setRequiredDate("");
    setSite(sites?.[0] || null);
  };

  // const handleSubmit = (e) => {
  //   e.preventDefault();
  //   // Submit logic here
  //   alert("Submitted PPE Consumable!");
  // };

  
    useEffect(() => {
      // setIsLoading(false);
      setTimeout(function () {
        Cookies.set("rawWarning", true, { expires: 365 });
        Cookies.remove("finishWarning");
      }, 2000);
  
      // setAlldropDown(dropdowns);
      if (pageType == "update") {
        setIsEdit(true);
      }
      if (pageType == "add" && userData) {
        setNameOfOriginator(userData.name);
      }
    }, [0]);//,reload

  return (
    <form
      onSubmit={handleSubmit}
      className="flex flex-col justify-start max-w-full  mx-auto mr-14"
    >
      <div className="">
        <h2 className="text-lg font-semibold tracking-wide mb-4 pl-2">Add PPE/Consumable Order</h2>
        <div className="flex flex-row gap-8 bg-white p-6 py-12 rounded-lg shadow-[0_0_1px_rgba(0,0,0,0.1)]">
          <div className="w-full">
            <label className="labels mb-2">Full Name</label>
            <input
              type="text"
              value={nameOfOriginator || ""}
              disabled
              className="block w-full px-4 text-gray-500 border rounded-lg form-input"
            />
          </div>
          <div className="w-full">
            <label className="labels mb-2">Email</label>
            <input
              type="text"
              value={userData?.email || ""}
              disabled
              className="block w-full px-4 text-gray-500 border rounded-lg form-input"
            />
          </div>
          {/* </div> */}
          {/* <div className="flex flex-row gap-8 mb-6"> */}
          <div className="w-full">
            <label className="labels mb-2">Created Date</label>
            <input
              type="text"
              value={new Date().toLocaleDateString("en-CA")}
              disabled
              className="block w-full px-4 text-gray-500 border rounded-lg form-input"
            />
          </div>
          <div className="w-full">
            <label className="labels mb-2">Site</label>
            <Select
              value={site}
              onChange={setSite}
              options={sites11}//{sites}
              isDisabled={sites?.length === 1}
              styles={customSelectStyles}
              className="reactSelectCustom w-full"
            />
          </div>
          {/* </div> */}
          {/* <div className="flex flex-row gap-8 mb-6"> */}
          <div className="w-full">
            <label className="labels mb-2">
              Required Date <span className="text-red-500">*</span>
            </label>
            <input
              type="date"
              min={new Date().toISOString().split("T")[0]}
              value={requiredDate}
              onChange={(e) => setRequiredDate(e.target.value)}
              className="block w-full px-4 border rounded-lg form-input"
            />
          </div>
        </div>
      </div>

      {/* Meta Header Section 
        //  #region items 
        */}
      <div className="mt-3 ">
        <div className="flex flex-row justify-between items-end mt-8 mb-4">
          <h3 className="flex mb-1 font-semibold tracking-wider text-base pl-2">Items</h3>
          <button
            type="button"
            onClick={addItem}
            className="flex justify-center items-center border border-skin-primary text-skin-primary   rounded-md pt-1 px-6 mb-3"
          >
            Add Item
          </button>
        </div>
        {items.map((item, idx) => (
          <div
            key={idx}
            className="flex flex-row gap-4 mb-4 items-end bg-white p-6 rounded-lg py-8 shadow-[0_0_2px_rgba(0,0,0,0.2)]"
          >
            <div className="w-1/4">
              <label className="labels mb-2">
                Product <span className="text-red-500">*</span>
              </label>
              <Select
                value={item.product}
                onChange={(val) => handleItemChange(idx, "product", val)}
                options={products11}//products}
                styles={customSelectStyles}
                className="reactSelectCustom w-full"
                isSearchable
                isClearable
              />
            </div>
            <div className="w-1/6">
              <label className="labels mb-2">Size</label>
              <Select
                value={item.size}
                onChange={(val) => handleItemChange(idx, "size", val)}
                options={item.product?.sizes || []}
                styles={customSelectStyles}
                className="reactSelectCustom w-full"
                isSearchable
                isClearable
                isDisabled={!item.product?.size_required}
              />
            </div>
            <div className="w-1/6">
              <label className="labels mb-2">
                Quantity <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                min={1}
                value={item.quantity}
                onChange={(e) =>
                  handleItemChange(
                    idx,
                    "quantity",
                    Math.max(1, Number(e.target.value))
                  )
                }
                className="block w-full px-4 border rounded-lg form-input"
              />
            </div>
            <div className="w-1/4">
              <label className="labels mb-2">Name for Printing</label>
              <input
                type="text"
                value={item.nameForPrinting}
                onChange={(e) =>
                  handleItemChange(idx, "nameForPrinting", e.target.value)
                }
                disabled={item.quantity !== 1}
                className="block w-full px-4 border rounded-lg form-input"
                placeholder="Only if quantity = 1"
              />
            </div>
            <div className="w-1/4">
              <label className="labels mb-2">Comments</label>
              <textarea
                value={item.comments}
                onChange={(e) =>
                  handleItemChange(idx, "comments", e.target.value)
                }
                className="block w-full px-4 border rounded-lg form-input resize-none"
                rows={1}
              />
            </div>
            {items.length > 1 && (
              <button
                type="button"
                onClick={() => removeItem(idx)}
                className="ml-2 text-red-500 font-bold"
                title="Remove item"
              >
                X
              </button>
            )}
          </div>
        ))}
      </div>

      <div className="flex flex-row justify-end gap-4 rounded-lg border border-dashed border-skin-primary p-4 mt-4">
        <button
          type="button"
          onClick={handleClear}
          className="border border-skin-primary text-skin-primary rounded-md pt-1 px-6"
          disabled={loading}
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={handleSubmit}
          className="border border-skin-primary bg-skin-primary text-white rounded-md pt-1 px-6 font-medium"
          disabled={loading}
        >
          Submit
        </button>
      </div>

      {/* <div className="mb-4">
        <label className="block mb-1">PPE Type</label>
        <select
          value={ppeType}
          onChange={(e) => setPpeType(e.target.value)}
          className="border rounded px-3 py-2 w-full"
        >
          <option value="">Select...</option>
          {dropdowns?.ppeType?.map((type) => (
            <option key={type.value} value={type.value}>
              {type.label}
            </option>
          ))}
        </select>
      </div>
      <div className="mb-4">
        <label className="block mb-1">PPE Category</label>
        <select
          value={ppeCategory}
          onChange={(e) => setPpeCategory(e.target.value)}
          className="border rounded px-3 py-2 w-full"
        >
          <option value="">Select...</option>
          {dropdowns?.ppeCategory?.map((cat) => (
            <option key={cat.value} value={cat.value}>
              {cat.label}
            </option>
          ))}
        </select>
      </div>
      <div className="mb-4">
        <label className="block mb-1">Site</label>
        <select
          value={ppeSite}
          onChange={(e) => setPpeSite(e.target.value)}
          className="border rounded px-3 py-2 w-full"
        >
          <option value="">Select...</option>
          {dropdowns?.ppeSite?.map((site) => (
            <option key={site.value} value={site.value}>
              {site.label}
            </option>
          ))}
        </select>
      </div>
      <div className="mb-4">
        <label className="block mb-1">Status</label>
        <select
          value={ppeStatus}
          onChange={(e) => setPpeStatus(e.target.value)}
          className="border rounded px-3 py-2 w-full"
        >
          <option value="">Select...</option>
          {dropdowns?.ppeStatus?.map((status) => (
            <option key={status.value} value={status.value}>
              {status.label}
            </option>
          ))}
        </select>
      </div>
      <div className="mb-4">
        <label className="block mb-1">Comment</label>
        <textarea
          value={ppeComment}
          onChange={(e) => setPpeComment(e.target.value)}
          className="border rounded px-3 py-2 w-full"
          rows={3}
        />
      </div>
      <button
        type="submit"
        className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700"
      >
        Submit
      </button> */}
    </form>
  );
};

export default PpeConsumable;
