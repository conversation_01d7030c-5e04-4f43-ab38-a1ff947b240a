DECLARE @CurrentDate DATE;

DECLARE @QuarterList TABLE (FiscalQuarter INT);

DECLARE @CurrentQuarter INT;

DECLARE @currentWeek INT;

DECLARE @currentStartWeek DATE;

DECLARE @currentCalendarYear INT;

DECLARE @includesQuarterFour BIT = 0;

DECLARE @CurrentFinancialYear INT;

SET
    @CurrentFinancialYear = CASE
        WHEN MONTH(GETDATE()) >= 10 THEN YEAR(GETDATE()) + 1
        ELSE YEAR(GETDATE())
    END;

SET
    @currentCalendarYear = YEAR(GETDATE());

SET
    @CurrentDate = CASE
        WHEN @financialYear = @CurrentFinancialYear THEN GETDATE() -- Use current date if year is current
        ELSE CONVERT(
            DATE,
            CAST(@financialYear - 1 AS CHAR(4)) + '1001',
            112
        ) -- 1 Oct of the input year if it's a past year
    END;

SELECT
    @currentWeek = fiscalweek
FROM
    FLR_DEV_TEST_off_BI_lookup.dbo.off_cal_start_end_week
WHERE
    calendar_name = 'OFF Financial Calendar'
    AND fiscalyear = @CurrentFinancialYear
    AND GETDATE() BETWEEN startweek
    AND endweek;

SELECT
    @CurrentQuarter = fiscalquarter,
    @currentStartWeek = startweek
FROM
    FLR_DEV_TEST_off_BI_lookup.dbo.off_cal_start_end_week
WHERE
    calendar_name = 'OFF Financial Calendar'
    AND fiscalyear = @financialYear
    AND @CurrentDate BETWEEN startweek
    AND endweek;

DECLARE @Quarters VARCHAR(MAX);

SET
    @Quarters = @quartersSelected;

INSERT INTO
    @QuarterList
SELECT
    value
FROM
    STRING_SPLIT(@Quarters, ',');

IF EXISTS (
    SELECT
        1
    FROM
        @QuarterList
    WHERE
        FiscalQuarter = 4
) BEGIN
SET
    @includesQuarterFour = 1;

END
select
    [fiscalyear],
    [fiscalquarter],
    [fiscalweek],
    FORMAT(startweek, 'yyyy-MM-dd') AS startweek,
    FORMAT(endweek, 'yyyy-MM-dd') AS endweek,
    @CurrentQuarter AS currentQuarter,
    @currentWeek AS currentWeek,
    @currentCalendarYear AS currentCalendarYear,
    FORMAT(@currentStartWeek, 'yyyy-MM-dd') AS currentStartWeek
from
    [FLR_DEV_TEST_off_BI_lookup].[dbo].[off_cal_start_end_week]
where
    calendar_name = 'OFF Financial Calendar'
    and (
        (
            fiscalyear = @financialYear
            and fiscalquarter in (
                SELECT
                    FiscalQuarter
                FROM
                    @QuarterList
            )
        )
        OR (
            @includesQuarterFour = 1
            AND fiscalyear = @financialYear + 1
            AND fiscalquarter = '1'
        )
    )
order by
    startweek