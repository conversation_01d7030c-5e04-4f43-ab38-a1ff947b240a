WITH sales AS (
    SELECT
        orddetid AS [OrdDetID],
        ordnum AS [Ord Num],
        ordstatus AS ord_status_id,
        ordhed_type AS OrdTypeID,
        delivery_date AS [Delivery Date],
        canc_flag AS IS_CANCELLED,
        ds.new_line_flag,
        ds.altfilid,
        ds.custcode,
        cust_customer.hocustcode [HO Cust Code],
        COALESCE(CAST(orig_qty AS INT), 0) AS [Order Cases (orig)],
        COALESCE(ord_qty, 0) AS [Order Cases (amended)],
        COALESCE(shipped_qty_cases, 0) [Shipped Cases],
        ds.unit_price
    FROM
        [FLR_DEV_TEST_off_BI_lookup].[dbo].dm_sales_totals ds
        JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].dm_altfil ALT ON ALT.altfilid = ds.altfilid
        join [FLR_DEV_TEST_off_BI_lookup].[dbo].dm_customer cust_customer on cust_customer.custcode = ds.custcode
        join [FLR_DEV_TEST_off_BI_lookup].[dbo].dm_customer delcust_customer on ds.delcustcode = delcust_customer.custcode
    WHERE
        case
            when cust_customer.custcode <> 'OFF' then cust_customer.hocustcode
            else delcust_customer.hocustcode
        end NOT IN ('PPACK', 'REJISS', 'SMOVE') -- deptcode = 9 
        AND ds.altfilid > 0
        AND Prov_Order_Flag = 0
        AND delivery_date >= @start_date
        AND delivery_date <= @end_date
        AND cust_customer.category_no = 1 -- AND canc_flag = 0 cancelled products to be shown in the portal to add cancellation reasons
        -- AND cust_customer.hocustcode NOT IN ('ASSAP', 'CWSUK', 'NISAUK') -- shortage accounts for october 2023 to september 2024
        AND (
            (
                delivery_date between '********'
                and '********'
                AND cust_customer.hocustcode NOT IN ('ASSAP', 'CWSUK', 'NISAUK')
            )
            OR (
                delivery_date between '********'
                and @end_date
                AND cust_customer.hocustcode NOT IN ('CWSUK', 'NISAUK')
            )
        ) -- shortage accounts for october 2023 to september 2024
),
allproducts AS (
    SELECT
        DISTINCT alt.product_number AS [Product Number],
        alt.countsize,
        alt.product_type,
        CASE
            WHEN vwMG.finance_master_code_grouping LIKE 'kiwi%' THEN 'Exotics'
            ELSE SPGD.business_unit
        END AS [Business Unit],
        COALESCE(
            NULLIF(TRIM(vwMG.finance_master_code_grouping), ''),
            'Unknown'
        ) AS [Master Code Group],
        SPGD.mascode_desc AS [Master Code],
        ds.altfilid,
        alt.tpnd AS [Alternate Product Number],
        COALESCE(NULLIF(TRIM(alt.product_desc), ''), 'Unknown') AS [Product Desc],
        COALESCE(NULLIF(TRIM(alt.segment1), ''), 'Unknown') AS [End Customer],
        COALESCE(NULLIF(TRIM(alt.segment2), ''), 'Unknown') AS [Brand]
    FROM
        [FLR_DEV_TEST_off_BI_lookup].[dbo].dm_prod_group_desc SPGD
        JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].dm_product_lvl_group SPLG ON SPGD.mascode = SPLG.mascode
        JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].dm_sales_totals ds ON SPLG.prodnum = ds.product_number
        JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].dm_altfil ALT ON ALT.altfilid = ds.altfilid
        LEFT JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].vw_finance_master_code_group vwMG ON vwMG.mascode = SPLG.mascode
    WHERE
        -- ds.deptcode = 9
        ds.altfilid > 0
        AND Prov_Order_Flag = 0
        AND delivery_date >= @start_date
        AND delivery_date <= @end_date
),
cte_rno AS (
    SELECT
        *,
        ROW_NUMBER() OVER (
            PARTITION BY altfilid
            ORDER BY
                altfilid
        ) AS rno
    FROM
        allproducts
),
products AS (
    SELECT
        *
    FROM
        cte_rno
    WHERE
        rno = 1
),
CTE_Cases_Difference AS (
    SELECT
        s.[OrdDetID],
        s.[Order Cases (orig)],
        s.[Order Cases (amended)],
        s.[Shipped Cases],
        p.altfilid AS ALTFILID,
        p.product_type AS CATEGORY,
        p.countsize AS CASE_SIZE,
        p.[Product Desc] AS PRODUCT_DESCRIPTION,
        -- abs(s.[Order Cases (orig)] - s.[Shipped Cases]) AS CASES_DIFFERENCE,
        CASE
            WHEN s.new_line_flag = 0 THEN abs(s.[Order Cases (orig)] - s.[Shipped Cases])
            WHEN s.[Order Cases (orig)] = s.[Shipped Cases] THEN s.[Shipped Cases]
            ELSE abs(s.[Order Cases (orig)] - s.[Shipped Cases])
        END AS CASES_DIFFERENCE,
        p.[Master Code] AS MASTER_PRODUCT_CODE
    FROM
        sales s
        JOIN products p ON p.altfilid = s.altfilid
    WHERE
        @cust_code = 'All Customers'
        OR s.[HO Cust Code] = @cust_code
),
CTE_Cases_Added_Reasons AS (
    SELECT
        s.[OrdDetID],
        COALESCE(
            SUM(slr.quantity),
            0
        ) AS CASES_ADDED_REASONS
    FROM
        sales s
        LEFT JOIN [THL_Webapp_Portal].[dbo].[sl_reasons] slr ON slr.order_id = CAST(s.[OrdDetID] AS VARCHAR)
        AND slr.is_deleted = 0
    WHERE
        (
            @cust_code = 'All Customers'
            OR s.[HO Cust Code] = @cust_code
        )
        AND NOT (
            (
                s.[Order Cases (orig)] = s.[Shipped Cases]
                AND s.new_line_flag = 0
            )
            OR (
                s.[Order Cases (orig)] = s.[Order Cases (amended)]
                AND s.[Order Cases (amended)] = s.[Shipped Cases]
                AND s.new_line_flag = 0
            ) --OR
            --(s.[Order Cases (amended)]>[Order Cases (orig)] and [Order Cases (amended)]=[Shipped Cases])
            OR (
                s.[Order Cases (orig)] = 0
                AND s.[Shipped Cases] = 0
            )
        )
    GROUP BY
        s.[OrdDetID],
        s.[Order Cases (orig)],
        s.[Order Cases (amended)],
        s.[Shipped Cases]
),
CTE_locks AS (
    SELECT
        DISTINCT custcode,
        order_id,
        [user_name],
        lock_attained_at
    FROM
        sl_locks
    WHERE
        lock_active = 1
),
CTE_calender AS (
    SELECT
        off_fiscal_wk,
        startweek,
        endweek
    FROM
        [FLR_DEV_TEST_off_BI_lookup].[dbo].off_cal_start_end_week
    WHERE
        calendar_name = 'OFF Financial Calendar' --AND startweek >= @start_date AND endweek <= @end_date
),
CTE_Canc_Ord_Price as (
    SELECT
        DISTINCT dup_chk.orddetid,
        od.altfilid,
        dup_chk.uomnprice
    FROM
        (
            SELECT
                DISTINCT [orddetid],
                lag(orddetid) over (
                    order by
                        orddetid
                ) prev_value,
                case
                    when [orddetid] = lag(orddetid) over (
                        order by
                            orddetid
                    ) then 1
                    else 0
                end duplicate_chk,
                [altfilid],
                max([uomnprice]) uomnprice
            FROM
                [flr_dev_test_off_bi_lookup].[dbo].dm_sales_totals_audit_price
            GROUP BY
                [orddetid],
                [altfilid]
            HAVING
                max([uomnprice]) > '0.02'
        ) AS dup_chk
        inner join [flr_dev_test_off_bi_lookup].[dbo].off_procure_fact_orddet od on dup_chk.orddetid = od.orddetid
        and dup_chk.altfilid = od.altfilid
)
SELECT
    s.IS_CANCELLED,
    s.new_line_flag,
    s.[OrdDetID] AS ORD_ID,
    s.[Ord Num] AS ORD_NUMBER,
    S.[custcode] AS CUSTOMER,
    s.[Delivery Date] AS DEPOT_DATE,
    cal.off_fiscal_wk AS FISCAL_WEEK,
    --(
    --    CASE
    --        WHEN s.IS_CANCELLED = 1 THEN 'Cancelled'
    --        ELSE sm.descr
    --    END
    --) AS ORD_STATUS,
    sm.descr AS ORD_STATUS,
    s.[OrdTypeID] AS ORD_TYPE_ID,
    ordhed_type_desc AS ORDER_TYPE,
    ctd.ALTFILID,
    ctd.CATEGORY,
    ctd.CASE_SIZE,
    ctd.PRODUCT_DESCRIPTION,
    ctd.MASTER_PRODUCT_CODE AS MASTER_PRODUCT_CODE,
    s.[Order Cases (orig)] AS CASES_ORIGINAL,
    s.[Order Cases (amended)] AS CASES_AMENDED,
    s.[Shipped Cases] AS CASES_DELIVERED,
    ctd.CASES_DIFFERENCE,
    CASE
        WHEN s.[Order Cases (orig)] > 0 THEN (
            (
                CAST(s.[Shipped Cases] AS DECIMAL) / s.[Order Cases (orig)]
            ) * 100
        )
        ELSE 0
    END AS SERVICE_LEVEL_PERCENT,
    cnr.CASES_ADDED_REASONS AS CASES_ADDED_REASONS,
    case
        when IS_CANCELLED = 1 then coalesce(uomnprice, 0)
        else unit_price
    end as UNIT_PRICE,
    --unit_price as UNIT_PRICE,
    CASE
        WHEN IS_CANCELLED = 1 THEN coalesce(uomnprice, 0)
        ELSE unit_price
    END * ctd.CASES_DIFFERENCE AS TOTAL_VALUE,
    slr.id AS REASON_ID,
    slr.reason_id AS MAIN_REASON_ID,
    rmm.reason AS MAIN_REASON,
    slr.subreason_id AS SUB_REASON_ID,
    rms.reason AS SUB_REASON,
    slr.added_by AS REASON_ADDED_BY,
    slr.quantity AS REASON_QTY,
    slr.comment AS REASON_COMMENT,
    slr.updated_by AS REASON_UPDATED_BY,
    slr.[added_timestamp] AS REASON_ADDED_TIMESTAMP,
    slr.[updated_timestamp] as REASON_UPDATED_TIMESTAMP,
    l.[user_name] AS LOCKED_BY
FROM
    sales s
    JOIN CTE_Cases_Difference ctd ON ctd.[OrdDetID] = s.[OrdDetID]
    JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_orderstatusdesc] sm ON sm.[status] = s.ord_status_id
    JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_order_type] ot ON ot.ordhed_type = s.[OrdTypeID] --LEFT JOIN cte_customer cust on cust.[Cust Code] = s.custcode
    LEFT JOIN CTE_calender cal ON s.[Delivery Date] >= cal.startweek
    AND s.[Delivery Date] <= cal.endweek
    LEFT JOIN CTE_Cases_Added_Reasons cnr ON cnr.[OrdDetID] = s.[OrdDetID]
    LEFT JOIN [THL_Webapp_Portal].[dbo].[sl_reasons] slr ON slr.order_id = CAST(s.[OrdDetID] AS VARCHAR)
    AND slr.is_deleted = 0
    LEFT JOIN CTE_locks l ON l.custcode COLLATE DATABASE_DEFAULT = s.custcode COLLATE DATABASE_DEFAULT
    AND l.order_id = CAST(s.[OrdDetID] AS VARCHAR)
    LEFT JOIN [THL_Webapp_Portal].[dbo].[sl_reasons_master] rmm ON rmm.id = slr.reason_id
    LEFT JOIN [THL_Webapp_Portal].[dbo].[sl_reasons_master] rms ON rms.id = slr.subreason_id
    -- left join [FLR_DEV_TEST_off_BI_lookup].[dbo].dm_sales_totals_audit_price ap on s.OrdDetID = ap.orddetid
    left join CTE_Canc_Ord_Price cop on s.OrdDetID = cop.orddetid
WHERE
    (
        @cust_code = 'All Customers'
        OR s.[HO Cust Code] = @cust_code
    )
    AND NOT (
        (
            s.[Order Cases (orig)] = s.[Shipped Cases]
            AND s.new_line_flag = 0 --AND s.[Order Cases (amended)] = 0 
        )
        OR (
            s.[Order Cases (orig)] = s.[Order Cases (amended)]
            AND s.[Order Cases (amended)] = s.[Shipped Cases]
            AND s.new_line_flag = 0
        ) --OR
        --(s.[Order Cases (amended)]>[Order Cases (orig)] and [Order Cases (amended)]=[Shipped Cases])
        OR (
            s.[Order Cases (orig)] = 0
            AND s.[Shipped Cases] = 0
        )
    )
    AND (
        @get_all = 1
        OR (
            @get_all = 0
            AND (
                ctd.CASES_DIFFERENCE - cnr.CASES_ADDED_REASONS <> 0
                or s.new_line_flag = 1
            )
        )
    )
    AND s.[OrdTypeID] IN (3)
    AND (
        @orderId is NULL
        AND 1 = 1
        OR (
            @orderId is not NULL
            AND s.[OrdDetID] = @orderId
        )
    )
    -- and (
    --     uomnprice > 0
    --     or uomnprice is null
    -- )
    AND S.[custcode] NOT LIKE 'SAS%'
ORDER BY
    s.[Delivery Date],
    s.[custcode],
    ctd.PRODUCT_DESCRIPTION;