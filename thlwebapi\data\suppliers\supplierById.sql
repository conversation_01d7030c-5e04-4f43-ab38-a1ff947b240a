SELECT
    s.[id],
    s.[name],
    s.[currency],
    cur.[name] as currency_name,
    cur.[symbol] as symbol,
    cur.[code] as currency_code,
    s.[compliance],
    s.[vatable],
    s.[financial],
    s.[technical],
    s.[procurement],
    s.[emergency_request],
    s.[gdpr_compliant],
    s.[product_supplier],
    s.[trading_name],
    s.[email_id],
    s.[facsimile],
    s.[telephone],
    s.[address_line_1],
    s.[address_line_2],
    s.[address_line_3],
    s.[address_line_4],
    c.[id] AS country_id,
    c.[name] AS country_name,
    c.[code] AS country_code,
    s.[postal_code],
    s.[vat_number],
    s.[company_registration],
    s.[payment_terms],
    s.[country_code] as country_code_id,
    cc.[code] as country_code_name,
    pt.[name] as payment_type,
    pt.[id] as paymentTypeId,
    s.[validated_procurement_team],
    s.[validated_date],
    s.[validated_by],
    s.[finance_authorization],
    s.[finance_authorization_date],
    s.[finance_authorization_by],
    s.[rejected_reason],
    s.[red_tractor],
    s.[puc_code],
    s.[chile_certificate_number],
    s.[organic_certificate_number],
    s.[global_gap_number],
    s.[customer_site_code],
    s.[created_date],
    s.[updated_date],
    s.[status],
    s.[requestor_email],
    s.[requestor_name],
    s.[is_code_system_generated],
    STUFF(
        (
            SELECT
                ', ' + spp_inner.[prophet_name]
            FROM
                [dbo].[supplier_prophets] sp_inner
                JOIN [dbo].[prophet_system] spp_inner ON sp_inner.prophet_id = spp_inner.id
            WHERE
                sp_inner.supplier_id = s.id FOR XML PATH(''),
                TYPE
        ).value('.', 'NVARCHAR(MAX)'),
        1,
        2,
        ''
    ) AS prophet_names,
    STUFF(
        (
            SELECT
                ', ' + srr_inner.[role_name]
            FROM
                [dbo].[supplier_roles] sr_inner
                JOIN [dbo].[supplier_role] srr_inner ON sr_inner.role_id = srr_inner.id
            WHERE
                sr_inner.supplier_id = s.id FOR XML PATH(''),
                TYPE
        ).value('.', 'NVARCHAR(MAX)'),
        1,
        2,
        ''
    ) AS role_names,
    (
        SELECT
            dtay_inner.[id],
            dtay_inner.[product_number],
            dtay_inner.[description],
            dtay_inner.[brand],
            dtay_inner.[end_customer],
            dtay_inner.[agreed_terms],
            ag.[terms] as agreed_terms_name,
            dtay_inner.[pricing],
            dtay_inner.[yields],
            dtay_inner.[start_date],
            dtay_inner.[end_date]
        FROM
            [dbo].[default_terms_agreed_yield] dtay_inner
            LEFT JOIN agreed_terms ag on ag.id = dtay_inner.[agreed_terms]
        WHERE
            s.id = dtay_inner.supplier_id FOR JSON PATH
    ) AS dtay_data_json,
(
    SELECT
        sp_inner.[prophet_id],
        sp_inner.[prophet_code], (
        SELECT COUNT(*)
        FROM [dbo].[supplier_prophets] sp_all
        WHERE sp_all.[prophet_code] = sp_inner.[prophet_code] and in_prophet=0
    ) + (
        SELECT COUNT(*)
        FROM [dbo].[supplier_iss] si_all
        WHERE si_all.[supcode] = sp_inner.[prophet_code] and prophet_id in (1,3,4,5)
    ) AS code_count
    FROM
        [dbo].[supplier_prophets] sp_inner
    WHERE
        s.id = sp_inner.supplier_id
    FOR JSON PATH
) AS prophets_id_code,
    (
        SELECT
            sr_inner.[role_id]
        FROM
            [dbo].[supplier_roles] sr_inner
        WHERE
            s.id = sr_inner.supplier_id FOR JSON PATH
    ) AS role_ids,
    (
        SELECT
            dp.[id],
            dp.[from_dp],
            dp.[name] as distribution_name,
            dp.[isActive] as dpIsActive,
            sdp.[distribution_point_id],
            sdp.[haulier_id],
            sdp.[direct_dp],
            sdp.[isActive] as sdpIsActive,
            ps.[prophet_name],
            h.[name] as haulier_name
        FROM
            [distribution_points] dp
            LEFT JOIN [supplier_distribution_point] sdp ON sdp.distribution_point_id = dp.id
            LEFT JOIN [prophet_system] ps ON ps.id = dp.prophet_id
            LEFT JOIN [hauliers] h ON sdp.haulier_id = h.id
        WHERE
            sdp.[isActive] = 1
            AND dp.[isActive] = 1
            AND sdp.supplier_id = @id FOR JSON PATH
    ) AS distribution_points_json,
    (
        SELECT
            dt.name AS delivery_terms,
            mt.name AS mode_of_transport,
            d_inner.id AS id
        FROM
            [dbo].[delivery] d_inner
            LEFT JOIN [dbo].[delivery_terms] dt ON d_inner.delivery_terms = dt.id
            LEFT JOIN [dbo].[mode_of_transports] mt ON d_inner.mode_of_transport = mt.id
        WHERE
            s.id = d_inner.supplier_id FOR JSON PATH
    ) AS delivery_json,
    (
        SELECT
            spsg.[group_id],
            sg.[label],
            sg.[value],
            sg.[prophet_id]
        FROM
            [dbo].[supplier_sendac_group] spsg
            LEFT JOIN [dbo].[sendac_groups] sg ON sg.id = spsg.group_id
        WHERE
            s.id = spsg.supplier_id FOR JSON PATH
    ) AS supplier_sendac_group_json,
    (
        SELECT
            ssg.[supplier_id],
            (
                SELECT
                    s.[name]
                FROM
                    [dbo].[supplier] s
                WHERE
                    ssg.supplier_id = s.id
            ) AS name,
            srs.[role_id],
            STUFF(
                (
                    SELECT
                        ', ' + CAST(spr.role_name AS VARCHAR(100)) [text()]
                    FROM
                        [dbo].[supplier_role] spr
                    WHERE
                        spr.id = srs.role_id
                        and ssg.supplier_id = srs.supplier_id FOR XML PATH(''),
                        TYPE
                ).value('.', 'NVARCHAR(MAX)'),
                1,
                2,
                ' '
            ) role_names
        FROM
            [dbo].[supplier_sendac_group] ssg
            LEFT JOIN [dbo].[supplier_roles] srs ON ssg.supplier_id = srs.supplier_id
        WHERE
            ssg.group_id = (
                SELECT
                    group_id
                FROM
                    [dbo].[supplier_sendac_group]
                WHERE
                    supplier_id = @id
            ) FOR JSON PATH
    ) AS supplier_links_json,
    (
        SELECT
            ct.[id] as id,
            ct.[name],
            ct.[email_id],
            ct.[telephone],
            toc.[name] AS type_of_contact
        FROM
            [dbo].[supplier_contacts] ct
            INNER JOIN [dbo].[type_of_contacts] toc ON ct.type_of_contact = toc.id
        WHERE
            s.id = ct.supplier_id FOR JSON PATH
    ) AS contacts_json,
    ba.[sort_bic],
    ba.[name_branch],
    ba.[account_number],
    ba.[intermediatery_account_number],
    ba.[id] AS bank_account_id,
    ba.has_iban,
    st.[supplier_type] AS supplier_type_label,
    s.supplier_type AS supplier_type_id,
    s.edi
FROM
    [dbo].[Supplier] s
    LEFT JOIN [dbo].[default_terms_agreed_yield] dtay ON s.id = dtay.supplier_id
    LEFT JOIN [dbo].[delivery] d ON s.id = d.supplier_id
    LEFT JOIN [dbo].[supplier_sendac_group] ssg ON s.id = ssg.supplier_id
    LEFT JOIN [dbo].[bank_accounts] ba ON s.id = ba.supplier_id
    LEFT JOIN [dbo].[countries] c ON s.country = c.id
    LEFT JOIN [dbo].[countries] cc ON s.country_code = cc.id
    LEFT JOIN [dbo].[currencies] cur ON s.currency = cur.id
    LEFT JOIN [dbo].[payment_types] pt ON s.payment_type = pt.id
    LEFT JOIN [dbo].[supplier_type] st ON st.id = s.supplier_type
WHERE
    s.[id] = @id
GROUP BY
    s.[id],
    s.[name],
    s.[requestor_email],
    s.[requestor_name],
    s.[currency],
    s.[compliance],
    s.[financial],
    s.[technical],
    s.[procurement],
    s.[emergency_request],
    s.[gdpr_compliant],
    s.[product_supplier],
    s.[trading_name],
    s.[email_id],
    s.[facsimile],
    s.[telephone],
    s.[address_line_1],
    s.[address_line_2],
    s.[address_line_3],
    s.[address_line_4],
    s.[country],
    s.[postal_code],
    s.[vat_number],
    s.[company_registration],
    s.[country_code],
    s.[payment_terms],
    s.[payment_type],
    s.[validated_procurement_team],
    s.[validated_date],
    s.[validated_by],
    s.[finance_authorization],
    s.[finance_authorization_date],
    s.[finance_authorization_by],
    s.[rejected_reason],
    s.[red_tractor],
    s.[puc_code],
    s.[chile_certificate_number],
    s.[organic_certificate_number],
    s.[global_gap_number],
    s.[customer_site_code],
    s.[created_date],
    s.[updated_date],
    s.[status],
    ba.[sort_bic],
    ba.[name_branch],
    ba.[account_number],
    ba.[intermediatery_account_number],
    ba.[id],
    ba.[has_iban],
    c.[id],
    c.[name],
    c.[code],
    cc.[code],
    cur.[name],
    cur.[symbol],
    cur.[code],
    pt.[name],  s.[vatable],[is_code_system_generated],
    pt.[id],
    s.[supplier_type],
    st.[supplier_type],
    s.edi;