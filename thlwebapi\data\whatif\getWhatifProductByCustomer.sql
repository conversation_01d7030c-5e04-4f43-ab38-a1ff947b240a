-- select 
--     atl.product_number as value,
--     atl.product_desc as label
-- from [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_altfil] atl inner join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_customer] ct on atl.gencode=ct.gencode where ct.hocustcode=@cust_code and atl.active_flag=1

SELECT Distinct
    -- ff.hocustcode_customer AS cust_code, 
    dm.product_number AS value, 
    dm.product_desc AS label
    -- dm.altfilid
FROM 
    FLR_DEV_TEST_off_BI_lookup.dbo.finance_fact ff 
JOIN 
    FLR_DEV_TEST_off_BI_lookup.dbo.dm_altfil dm 
ON 
    dm.altfilid = ff.altfilid 
WHERE 
    ff.hocustcode_customer = @cust_code 
    AND dm.active_flag = 1
ORDER BY dm.product_desc ASC