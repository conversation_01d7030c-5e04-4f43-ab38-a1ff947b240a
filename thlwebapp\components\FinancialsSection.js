import React, { useState, Fragment, useEffect, useRef, useMemo } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faInfo,
  faXmark,
  faFloppyDisk,
  faPenToSquare,
  faTrash,
  faChevronDown,
} from "@fortawesome/free-solid-svg-icons";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles//ag-grid.css";
import "ag-grid-community/styles//ag-theme-alpine.css";
import { useRouter } from "next/router";
import { ThreeCircles } from "react-loader-spinner";
import { Disclosure } from "@headlessui/react";
import { getCookieData } from "@/utils/getCookieData";
import { addProphetAjaxCall } from "@/utils/ajaxHandler";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useMsal } from "@azure/msal-react";
import DebouncedAutocompleteDistribution from "@/utils/DebouncedAutocompleteDistribution";
import Cookies from "js-cookie";
import { useLoading } from "@/utils/loaders/loadingContext";
import { logout } from "@/utils/secureStorage";
import { apiConfig } from "@/services/apiConfig";

const FinancialsSection = ({
  data,
  setNavType,
  onSubmit,
  onPrev,
  isEdit,
  userData,
  dropdowns,
  navType,
  company,
}) => {
  const router = useRouter();
  const { supplierId } = router.query;
  const intermediaryBankAccountRef = useRef(null);
  const vatNumberRef = useRef(null);
  const companyRegRef = useRef(null);
  const currencyRef = useRef(null);
  const countryCodeRef = useRef(null);
  const paymentTermsRef = useRef(null);
  const paymentTypeRef = useRef(null);
  const vatableRef = useRef(null);
  const sortBicRef = useRef(null);
  const bankNameRef = useRef(null);
  const accountNumberRef = useRef(null);
  const gridRefDistribution = useRef(null);
  const gridRefDelivery = useRef(null);
  const [isOpen, setIsOpen] = useState(false);
  const [vatNumber, setVatNumber] = useState("");
  const [companyRegistration, setCompanyRegistration] = useState("");
  const [currency, setCurrency] = useState("");
  const [currencyName, setCurrencyName] = useState("");
  const [countryCode, setCountryCode] = useState("");
  const [countryCodeName, setCountryCodeName] = useState("");
  const [paymentTerms, setPaymentTerms] = useState("");
  const [paymentType, setPaymentType] = useState("");
  const [paymentTypeId, setPaymentTypeId] = useState("");
  const [sortBic, setSortBic] = useState("");
  const [bankName, setBankName] = useState("");
  const [accountNumber, setAccountNumber] = useState("");
  const [intermediaryBankAccount, setIntermediaryBankAccount] = useState("");
  const [delivery_terms, setDeliveryTerms] = useState("");
  const [deliveryId, setDeliveryId] = useState(null);
  const [mode_of_transport, setModeOfTransport] = useState("");
  const [isValidVatNumber, setIsValidVatNumber] = useState(true);
  const [isValidCompanyRegistration, setIsValidCompanyRegistration] =
    useState(true);
  const [isValidCurrency, setIsValidCurrency] = useState(true);
  const [isValidCountryCode, setIsValidCountryCode] = useState(true);
  const [isValidPaymentTerms, setIsValidPaymentTerms] = useState(true);
  const [isValidPaymentType, setIsValidPaymentType] = useState(true);
  const [isValidSortBic, setIsValidSortBic] = useState(true);
  const [isValidBankName, setIsValidBankName] = useState(true);
  const [isValidAccountNumber, setIsValidAccountNumber] = useState(true);
  const [isValidIntermediaryBankAccount, setIsValidIntermediaryBankAccount] =
    useState(true);
  const [supplierData, setSupplierData] = useState({});
  const [financialApproval, setFinancialApproval] = useState(2);
  const [hasIban, setHasIban] = useState(true);
  const [hasIbanManualChange, setHasIbanManualChange] = useState(false);
  const [dbFinancialApproval, setdbFinancialApproval] = useState(2);

  const [distributionPointsJson, setDistributionDataJson] = useState([]);
  const [distributionPoint, setDistributionPoint] = useState("");
  const [directDP, setDirectDP] = useState(false);
  const [validated, setValidated] = useState(false);
  const [validatedDate, setValidatedDate] = useState("");
  const [rejectedReason, setRejectedReason] = useState("");
  const [financeApprovalQue, setFinanceApprovalQue] = useState(false);
  const [rejectedReasonapi, setRejectedReasonapi] = useState("");
  const [loading, setLoading] = useState(false);
  const [deliveryJson, setDeliveryJson] = useState([]);
  const [vatNumberErrorMessage, setVatNumberErrorMessage] = useState("");
  const [sortBicErrorMessage, setSortBicErrorMessage] = useState("");
  const [accountNumberErrorMessage, setAccountNumberErrorMessage] =
    useState("");
  const [paymentTermsErrorMessage, setPaymentTermsErrorMessage] = useState("");
  const [
    intermediaryBankAccountErrorMessage,
    setIntermediaryBankAccountErrorMessage,
  ] = useState("");
  const [isValidDelivery, setIsValidDelivery] = useState(true);
  const [isValidModeOfTransport, setIsValidModeOfTransport] = useState(true);
  const [isValidDistribution, setIsValidDistribution] = useState(true);
  const [companyRegistrationErrorMessage, setCompanyRegistrationErrorMessage] =
    useState("");
  const [validatedBy, setValidatedBy] = useState("");
  const [financeApprovedBy, setFinanceApprovedBy] = useState("");
  const [isValidateTeam, setIsValidateTeam] = useState(false);
  const [prophets, setProphets] = useState([]);
  const [prophetsIds, setProphetIds] = useState(null);
  const [validatedData, setValidatedData] = useState("");
  const alphaNumericRegex = /^([a-zA-Z0-9 _&'-.]+)$/;
  const [isCommonError, setCommonError] = useState("");
  const [distriutionErrorMessage, setDistributionErrorMessage] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [isCancelled, setIsCancelled] = useState(false);
  const [isContinue, setIsContinue] = useState(true);
  const [formChange, setFormChange] = useState(false);
  const [isDistributionDisabled, setDistributionDisabled] = useState(false);
  const [status, setStatus] = useState("");
  const [genCountryCode, setGenCountryCode] = useState("");
  const [allowedSections, setAllowedSections] = useState([]);
  const [vatable, setVatable] = useState("");
  const [isValidVatable, setIsValidVatable] = useState(true);
  const [isFinanceMandatory, setIsFinanceMandatory] = useState(true);
  const [isVatNumberMandatory, setIsVatNumberMandatory] = useState(false);
  const { setIsLoading } = useLoading();
  const [isCodeSystemGenerated, setIsCodeSystemGenerated] = useState(true);
  const [isDPRequired, setIsDPRequired] = useState(false);
  const [isExpenseRole, setIsExpenseRole] = useState(false);
  const [isSwiftBICCode, setIsSwiftBICCode] = useState(false);
  const [roleIds, setRoleIds] = useState([]);

  const handleVatableChange = (e) => {
    setFormChange(true);
    if (e.target.value == "true") {
      setIsVatNumberMandatory(true);
    } else {
      setIsVatNumberMandatory(false);
    }
    setVatable(e.target.value);
    setIsValidVatable(true);
  };

  useEffect(() => {
    if (typeof window !== "undefined") {
      const sectionsString = localStorage.getItem("allowedSections");
      if (sectionsString) {
        const parsedSections = sectionsString.split(",");
        setAllowedSections(parsedSections);
      }
    }
    setLoading(false);
    setIsLoading(false);
  }, []);

  const DeliveryIconsRenderer = (props) => {
    let updatedData;

    const handleDelete = (e) => {
      setFormChange(true);
      e.preventDefault();
      const rowData = props.data;

      updatedData = [...deliveryJson];

      const index = updatedData?.indexOf(rowData);
      updatedData?.splice(index, 1);

      props.api.applyTransaction({ remove: updatedData });
      setDeliveryJson(updatedData);
    };

    const handleEdit = (e) => {
      setFormChange(true);
      e.preventDefault();

      const rowData = props?.data;

      updatedData = [...deliveryJson];

      const index = updatedData?.indexOf(rowData);
      updatedData?.splice(index, 1);

      props.api.applyTransaction({ remove: updatedData });

      setDeliveryJson(updatedData);
      setDeliveryTerms(rowData?.delivery_terms);
      setDeliveryId(rowData?.id);
      setModeOfTransport(rowData?.mode_of_transport);
      if (!isValidDelivery) {
        setIsValidDelivery(true);
      }
      if (!isValidModeOfTransport) {
        setIsValidModeOfTransport(true);
      }
    };

    return (
      <div className="flex flex-row gap-4 justify-center text-skin-primary">
        <button onClick={handleEdit}>
          <FontAwesomeIcon icon={faPenToSquare} />
        </button>
        <button onClick={handleDelete} className="text-red-500">
          <FontAwesomeIcon icon={faTrash} />
        </button>
      </div>
    );
  };

  const DistributionIconsRenderer = (props) => {
    let updatedData;

    const handleDelete = (e) => {
      setFormChange(true);
      e.preventDefault();
      const rowData = props.data;

      updatedData = [...distributionPointsJson];

      const index = updatedData.indexOf(rowData);
      updatedData.splice(index, 1);

      props.api.applyTransaction({ remove: updatedData });

      setDistributionDataJson(updatedData);
    };

    const handleEdit = (e) => {
      setFormChange(true);
      e.preventDefault();
      setIsEditing(true);
      const rowData = props.data;

      updatedData = [...distributionPointsJson];

      const index = updatedData.indexOf(rowData);
      updatedData.splice(index, 1);

      props.api.applyTransaction({ remove: updatedData });

      setDistributionDataJson(updatedData);
      setDistributionPoint(rowData.distributionPoint);
      setDirectDP(rowData.directDP);
      if (!isValidDistribution) {
        setIsValidDistribution(true);
      }
    };

    return (
      <div className="flex flex-row gap-4 justify-start text-skin-primary">
        <button onClick={handleEdit}>
          <FontAwesomeIcon icon={faPenToSquare} />
        </button>
        <button onClick={handleDelete} className="text-red-500">
          <FontAwesomeIcon icon={faTrash} />
        </button>
      </div>
    );
  };

  const saveData = (e) => {
    setFormChange(true);
    e.preventDefault();
    const newItem = {
      delivery_terms,
      mode_of_transport,
      id: deliveryId,
    };

    if (
      Object.values(newItem)?.some((value) => {
        if (typeof value === "number") {
          // Check if the number is not null or undefined
          return value == null;
        }
        // For non-number values, check if they are empty strings
        return value?.trim() === "";
      })
    ) {
      if (delivery_terms == "") {
        setIsValidDelivery(false);
      }
      if (mode_of_transport == "") {
        setIsValidModeOfTransport(false);
      }
      return;
    }

    setIsValidDelivery(true);
    setIsValidModeOfTransport(true);
    setDeliveryJson((prevDeliveryJson) => {
      if (!Array.isArray(prevDeliveryJson)) {
        return [newItem];
      }

      return [...prevDeliveryJson, newItem];
    });

    setDeliveryTerms("");
    setModeOfTransport("");
    setDeliveryId(null);
  };
  const saveDistributionData = (e) => {
    if (isDistributionDisabled) {
      return;
    }
    const directDPvalue = directDP ? "Yes" : "No";
    setFormChange(true);
    e.preventDefault();
    const newItem = {
      distributionPoint,
      directDP,
      directDPvalue,
    };
    for (const key in newItem) {
      if (typeof newItem[key] === "string") {
        newItem[key] = newItem[key].trim();
      }
    }
    if (Object.values(newItem)?.some((value) => value === "")) {
      if (distributionPoint == "") {
        setIsValidDistribution(false);
        setDistributionErrorMessage(
          "Please enter a valid distribution point with max 50 characters"
        );
      }
      return;
    } else if (distributionPoint.length > 50) {
      setIsValidDistribution(false);
      setDistributionErrorMessage(
        "Please enter a valid distribution point with max 50 characters"
      );
      return;
    }

    const distributionPointExists = distributionPointsJson?.some(
      (item) => item?.distributionPoint === newItem?.distributionPoint
    );

    if (distributionPointExists) {
      setIsValidDistribution(false);
      setDistributionErrorMessage("Distribution point already exist");
      return;
    }

    let serverAddress = apiConfig.serverAddress;

    fetch(`${serverAddress}suppliers/check-distributions`, {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify({
        newItem,
        prophetId: parseInt(prophetsIds),
        supplierId,
        isEditing: isEditing,
      }),
    })
      .then((res) => {
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async () => {
            await logout();
            router.push("/login");
          }, 3000);
        }
        if (res.status === 201) {
          return res.json();
        }
        return Promise.reject(res);
      })
      .then((data) => {
        if (data?.exist && !isEditing) {
          setIsValidDistribution(false);
          setDistributionErrorMessage("Distribution data already exist");
        } else {
          if (data?.isOld) {
            newItem.isOld = true;
            newItem.dpId = data?.data[0]?.id;
            setDistributionDataJson((prevDistributionPoints) => {
              if (!Array.isArray(prevDistributionPoints)) {
                return [newItem];
              }
              return [...prevDistributionPoints, newItem];
            });
          } else {
            newItem.isOld = false;
            setDistributionDataJson((prevDistributionPoints) => {
              if (!Array.isArray(prevDistributionPoints)) {
                return [newItem];
              }
              return [...prevDistributionPoints, newItem];
            });
            // toast.warn("Please select a distribution point from dropdown");
          }
          setDirectDP(false);
          setDistributionPoint("");
          setIsEditing(false);
        }
      })
      .catch((error) => {
        console.error("Error checking distribution data:", error);
      });
  };

  useEffect(() => {
    setDistributionDisabled(distributionPointsJson?.length >= 1);
  }, [distributionPointsJson]);
  useEffect(() => {
    if (!isVatNumberMandatory) {
      setIsValidVatNumber(true);
      setVatNumberErrorMessage("");
    }
  }, [isVatNumberMandatory]);

  let errorCount = 0;

  function validate(step, e, isContinue) {
    const roles = JSON.parse(data[0]?.role_ids ?? "[]");

    const role_ids = roles?.map((item) => item?.role_id);
    if (role_ids?.includes(5) && role_ids.length == 1) {
      errorCount = 0;
    } else {
      if (!vatable && !currency && !isExpenseRole) {
        setIsValidVatable(false);
        setIsValidCurrency(false);
        toast.error(
          "Cannot proceed without selecting an option for both 'Vatable' (either 'Vatable' or 'Non-Vatable') and 'Currency'."
        );
        return;
      } else if (!vatable && !isExpenseRole) {
        setIsValidVatable(false);
        toast.error(
          "Cannot proceed without selecting either 'Vatable' or 'Non-Vatable'."
        );
        return;
      } else if (!currency) {
        setIsValidCurrency(false);
        toast.error("Cannot proceed without selecting a Currency.");
        return;
      }

      if (isDPRequired && distributionPointsJson?.length === 0) {
        setIsValidDistribution(false);
        setDistributionErrorMessage("Please add a distribution point.");
        errorCount++;
      }

      if (
        isVatNumberMandatory &&
        (!vatNumber || !isValidVatNumber) &&
        !isExpenseRole
      ) {
        setIsValidVatNumber(false);
        setVatNumberErrorMessage("Please enter valid VAT number.");
        errorCount++;
      }
      if (
        (!companyRegistration || !isValidCompanyRegistration) &&
        !isExpenseRole
      ) {
        setIsValidCompanyRegistration(false);
        setCompanyRegistrationErrorMessage(
          "Please enter valid company registration of max 50 chars."
        );
        errorCount++;
      }

      if (!paymentType) {
        setIsValidPaymentType(false);
        setPaymentTermsErrorMessage("Please select payment type.");
        errorCount++;
      }
      if (!paymentTerms || !isValidPaymentTerms) {
        errorCount++;
      }
      if (!countryCode) {
        setIsValidCountryCode(false);
        errorCount++;
      }
      if (!paymentTerms || !isValidPaymentTerms) {
        setIsValidPaymentTerms(false);
        errorCount++;
      }
      if (!sortBic || !isValidSortBic) {
        if (data[0]?.country_code == "UK") {
          setIsValidSortBic(false);
          setSortBicErrorMessage("Please enter a Sort code.");
        } else {
          setIsValidSortBic(false);
          setSortBicErrorMessage("Please enter a Swift/Bic number.");
        }
        errorCount++;
      }

      if (!bankName || !isValidBankName) {
        setIsValidBankName(false);
        errorCount++;
      }
      if (!accountNumber || !isValidAccountNumber) {
        setIsValidAccountNumber(false);
        setAccountNumberErrorMessage("Please enter a valid Account Number.");
        errorCount++;
      }
      // if (deliveryJson?.length === 0) {
      //   setIsValidDelivery(false);
      //   setIsValidModeOfTransport(false);
      // }
      // if (distributionPointsJson?.length === 0) {
      //   setIsValidDistribution(false);
      //   setIsValidHaulier(false);
      // }
    }
    if (errorCount > 0) {
      e.preventDefault();
      setNavType(step);
      setIsPopupOpen(true);
    } else {
      handleSubmit(step);
    }

    if (isCancelled) {
      setIsCancelled(false);
      return;
    }

    if (isContinue) {
      handleSubmit(step);
    }
  }

  const handleSubmit = (step) => {
    setLoading(true);
    let serverAddress = apiConfig.serverAddress;

    if (formChange) {
      let currentStatus;
      if (status == 3 || status == 4 || status == 1) {
        currentStatus = 4;
      } else if (status == 2) {
        currentStatus = 2;
      } else {
        currentStatus = 3;
      }
      fetch(`${serverAddress}suppliers/update-supplier/${supplierId}`, {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          sectionName: "financialsSection",
          vat_number: isValidVatNumber ? vatNumber?.trim() : "",
          company_registration: isValidCompanyRegistration
            ? companyRegistration?.trim()
            : "",
          currency: currency,
          currency_name: currencyName ? currencyName : "",
          payment_terms: isValidPaymentTerms ? paymentTerms?.trim() : "",
          country_code: countryCode,
          country_code_name: countryCodeName ? countryCodeName : "",
          payment_type: paymentTypeId,
          payment_type_name: paymentType ? paymentType : "",
          finance_authorization: financialApproval,
          validated_procurement_team: validated,
          delivery_json: deliveryJson,
          distribution_points_json: distributionPointsJson,
          sort_Bic: isValidSortBic ? sortBic?.trim() : "",
          account_number: isValidAccountNumber ? accountNumber?.trim() : "",
          intermediatery_account_number: isValidIntermediaryBankAccount
            ? intermediaryBankAccount?.trim()
            : "",
          name_branch: isValidBankName ? bankName?.trim() : "",
          rejected_reason:
            financialApproval == 0 || financialApproval == 2
              ? ""
              : rejectedReason?.trim(),
          financial:
            financialApproval == 0 && errorCount == 0
              ? "Complete"
              : financialApproval == 1
              ? "Rejected"
              : validated && errorCount == 0
              ? "Verified"
              : "Incomplete",
          technical: data[0].technical,
          compliance: data[0].compliance,
          procurement: data[0].procurement,
          updated_date: new Date().toISOString(),
          finance_authorization_date:
            dbFinancialApproval != financialApproval && financialApproval == 0
              ? new Date().toISOString()
              : null,
          validatedBy:
            data[0]?.requestor_email == userData?.email
              ? {
                  email: userData?.email,
                  name: userData?.name,
                }
              : { email: data[0]?.requestor_email },
          financeApprovedBy:
            (dbFinancialApproval != financialApproval &&
              financialApproval == 0) ||
            financialApproval == 1
              ? { user_id: userData?.user_id, email: userData?.email, name: userData?.name }
              : null,
          prophet_id: prophetsIds,
          requestor_email: userData?.requestor_email,
          supplierName: supplierData?.name,
          status: currentStatus,
          requestor_email: data[0]?.requestor_email,
          requestor_name: data[0]?.requestor_name,
          allowedSections: allowedSections,
          vatable: vatable,
          has_iban: hasIban,
          roleIds: roleIds,
        }),
      })
        .then(async (res) => {
          if (res.status === 400) {
            toast.error(
              "There was an error with your request. Please check your data and try again."
            );
          } else if (res.status === 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(async () => {
              await logout();
              router.push("/login");
            }, 3000);
          }
          if (res.status === 200) {
            return res.json();
          }
          return Promise.reject(res);
        })
        .then((json) => {
          if (step == "sap") {
            if (isEdit) {
              if (currency) {
                const result = addProphetAjaxCall(prophets, userData);
                result.then((data) => {
                  if (data?.data) {
                    localStorage.removeItem("isEdit");
                    router.back();
                  }
                });
              } else {
                localStorage.removeItem("isEdit");
                router.back();
              }
            } else {
              if (currency) {
                const result = addProphetAjaxCall(prophets, userData);
                result.then((data) => {
                  if (data?.data) {
                    onSubmit();
                  }
                });
              } else {
                onSubmit();
              }
            }
          } else {
            if (currency) {
              const result = addProphetAjaxCall(prophets, userData);
              result.then((data) => {
                if (data?.data) {
                  router?.push({ pathname: "/suppliers" });
                }
              });
            } else {
              router?.push({ pathname: "/suppliers" });
            }
          }
        })
        .catch((err) => {
          console.log("error", err);
          setLoading(false);
          toast.error(
            `Error saving data in financial forms file: ${err.statusText}`,
            {
              position: "top-right",
            }
          );
          return;
        });
    } else {
      if (step == "sap") {
        if (isEdit) {
          localStorage.removeItem("isEdit");
          router.back();
        } else {
          onSubmit();
        }
      } else {
        router.push({ pathname: "/suppliers" });
      }
    }
  };

  const closePopupModal = (e) => {
    e.preventDefault();
    setIsCancelled(true);
    setIsPopupOpen(false);
  };

  const handleContinueSubmit = (e) => {
    e.preventDefault();
    validate(navType, e, isContinue);
  };

  const vatRegex = {
    "United Arab Emirates": /^[A-Z0-9]{1,15}$/,
    Argentina: /^AR\d{11}$/,
    Australia: /^AU\d{9}$/,
    Austria: /^ATU\d{8}$/,
    Belgium: /^BE\d{10}$/,
    "Burkina Faso": /^[A-Z0-9]{1,15}$/,
    Bulgaria: /^BG\d{9,10}$/,
    "Bosnia & Herzegovina": /^BA\d{8}$/,
    Brazil: /^\d{14}$/,
    Canada: /^\d{9}$/,
    Switzerland: /^CHE\d{9}(MWST|TVA|IVA)$/,
    "Ivory Coast": /^[A-Z0-9]{1,15}$/,
    Chile: /^CL\d{9}$/,
    China: /^\d{15}$/,
    Colombia: /^\d{9,10}$/,
    "Costa Rica": /^\d{9}$/,
    Cyprus: /^CY\d{8}[A-Z]$/,
    "Czech Republic": /^CZ\d{8,10}$/,
    Germany: /^DE\d{9}$/,
    Denmark: /^DK\d{8}$/,
    "Dominican Republic": /^\d{9}$/,
    Ecuador: /^\d{13}$/,
    Egypt: /^\d{9}$/,
    Spain: /^ES[A-Z]\d{8}$/,
    Ethiopia: /^\d{10}$/,
    France: /^FR[A-HJ-NP-Z0-9]{2}\d{9}$/,
    "Great Britain": /^GB\d{9}$/,
    Ghana: /^[A-Z0-9]{1,15}$/,
    Guineau: /^[A-Z0-9]{1,15}$/,
    Greece: /^EL\d{9}$/,
    Guatemala: /^\d{8}$/,
    Honduras: /^\d{14}$/,
    Hungary: /^HU\d{8}$/,
    "Canary Islands": /^[A-Z0-9]{1,15}$/,
    Ireland: /^IE\d{7}[A-W][A-I|JZ]$/,
    Israel: /^\d{9}$/,
    India: /^IN\d{11}$/,
    Italy: /^IT\d{11}$/,
    Jordan: /^\d{9}$/,
    Kenya: /^\d{10}$/,
    Malaysia: /^\d{12}$/,
    Madagascar: /^[A-Z0-9]{1,15}$/,
    Macedonia: /^MK\d{13}$/,
    Mali: /^[A-Z0-9]{1,15}$/,
    Morocco: /^\d{8}$/,
    Mauritius: /^\d{8}$/,
    Mexico: /^\d{13}$/,
    Namibia: /^\d{9}$/,
    Netherlands: /^NL\d{9}B\d{2}$/,
    Norway: /^NO\d{9}MVA$/,
    "New Zealand": /^NZ\d{9}$/,
    Oman: /^\d{9}$/,
    Peru: /^\d{11}$/,
    Poland: /^PL\d{10}$/,
    "Puerto Rico": /^\d{9}$/,
    Portugal: /^PT\d{9}$/,
    Romania: /^RO\d{2,10}$/,
    Serbia: /^\d{9}$/,
    Slovenia: /^SI\d{8}$/,
    "Slovak Republic": /^SK\d{10}$/,
    Senegal: /^[A-Z0-9]{1,15}$/,
    Thailand: /^\d{13}$/,
    Tunisia: /^\d{7,8}$/,
    Turkey: /^\d{10}$/,
    Ukraine: /^\d{12}$/,
    "United Kingdom": /^(GB)?\d{9}$/,
    Uruguay: /^\d{12}$/,
    "United States": /^\d{9}$/,
    "Virgin Islands (U.S.)": /^\d{9}$/,
    Vietnam: /^\d{10,13}$/,
    "South Africa": /^\d{10}$/,
    Zimbabwe: /^\d{11}$/,
    Zambia: /^\d{9}$/,
  };

  function handleVatNumberChange(e) {
    setFormChange(true);
    setVatNumber(e.target.value.toUpperCase());

    const regex = vatRegex[data[0].country_name];

    if (!regex.test(e.target.value.toUpperCase().trim())) {
      setIsValidVatNumber(false);
      setVatNumberErrorMessage("Invalid VAT number format.");
      return;
    }

    setIsValidVatNumber(true);
    setVatNumberErrorMessage("");
  }

  function handleCompanyRegistrationChange(e) {
    setFormChange(true);
    setCompanyRegistration(e.target.value);

    // const CRNRegex = /^(L|U)(\d{5}|[A-Za-z]{2}\d{4})(\d{2})(\d{4})(PLC|PTC|FTC)$/;
    // if (e.target.value.length > 50 || !CRNRegex.test(e.target.value)) {
    if (e.target.value.length > 50) {
      setIsValidCompanyRegistration(false);
      // setCompanyRegistrationErrorMessage(
      //  "Please enter a valid company registration number CRN format: (L/U) + (5 digits OR 2 letters + 4 digits) + (2 digits) + (4 digits) + (PLC/PTC/FTC)"
      // );
      setCompanyRegistrationErrorMessage(
        "Please enter a valid Company Registration number"
      );
    } else {
      setIsValidCompanyRegistration(true);
    }
  }

  function handleCurrencyChange(e) {
    setFormChange(true);
    const selectedCurrencyName = e.target.options[e.target.selectedIndex].text;
    setCurrencyName(selectedCurrencyName);

    // Assuming the currency object has properties 'id', 'name', and 'symbol'
    const currency = dropdowns?.currencies?.find(
      (curr) => curr?.id == e.target.value
    );

    const supplierName = data[0]?.name;
    const prophetIds = data[0]?.prophets_id_code;
    if (isCodeSystemGenerated && prophetIds) {
      const prophet = JSON.parse(prophetIds ?? "[]");
      const supplierNameWithoutSpaces = supplierName
        .replace(/\s/g, "")
        .toUpperCase();
      const supplierPrefix = supplierNameWithoutSpaces?.substring(0, 4);

      const prophet_map = prophet?.map((item) => {
        let generatePhophetCode = "";
        if (item?.prophet_id == 1) {
          const supplierPrefixDPS = supplierNameWithoutSpaces?.substring(0, 5);

          generatePhophetCode = `${supplierPrefixDPS?.padEnd(5, "X")}${
            currency.symbol
          }`;
        } else if (item?.prophet_id == 2) {
          generatePhophetCode = `${supplierPrefix?.padEnd(4, "X")}9${
            currency.symbol
          }`;
        } else if (item?.prophet_id == 3) {
          generatePhophetCode = (supplierPrefix + "3").padEnd(6, "X");
        } else {
          generatePhophetCode = `${supplierPrefix}2${
            currency?.symbol ? currency?.symbol : ""
          }`;
          generatePhophetCode = generatePhophetCode.padEnd(6, "X");
        }

        return {
          prophet_id: item?.prophet_id,
          prophet_code: generatePhophetCode?.trim(),
          supplier_id: parseInt(supplierId),
        };
      });

      setProphets(prophet_map);
    }

    setCurrency(e.target.value);

    setIsValidCurrency(true);
  }

  function handleCountryCodeChange(e) {
    setFormChange(true);
    setCountryCode(e.target.value);
    const selectedCountryCodeName =
      e.target.options[e.target.selectedIndex].text;
    const countryCode = selectedCountryCodeName?.split("-")[0]?.trim();

    setCountryCodeName(countryCode);
    setIsValidCountryCode(true);
  }

  function handlePaymentTypeChange(e) {
    setFormChange(true);
    // Assuming e.target.value contains both the id and name separated by a delimiter like ':'
    setPaymentTypeId(e.target.value);
    const selectedPaymentTypeName =
      e.target.options[e.target.selectedIndex].text;

    // Set the payment type id and name in state
    setPaymentType(selectedPaymentTypeName);

    // Set any additional state variables as needed
    setPaymentTermsErrorMessage(
      "Please enter a valid payment term of max 50 chars."
    );
    setIsValidPaymentType(true);
  }

  function handlePaymentTermsChange(e) {
    setFormChange(true);
    setPaymentTerms(e.target.value);

    const lettersNumbersSpacesRegex = /^[a-zA-Z0-9\s-]+$/;

    if (
      e.target.value.length > 50 ||
      !lettersNumbersSpacesRegex.test(e.target.value)
    ) {
      setIsValidPaymentTerms(false);
    } else {
      setIsValidPaymentTerms(true);
    }
  }
  const [accIbanLabel, setAccIbanLabel] = useState("Account Number/IBAN");
  const [intAccIbanLabel, setIntAccIbanLabel] = useState(
    "Intermediary Account Number/Intermediary IBAN"
  );

  function handleSortBICChange(processedValue, isFormChange, has_iban) {
    if (isFormChange) {
      setFormChange(true);
    }

    const sortCodeRegex = /^([A-Z0-9]{6})$/;
    const swiftBicRegex =
      /^([A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}|[A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}[A-Z0-9]{3})$/;

    setSortBic(processedValue);

    if (!isFormChange) {
      if (has_iban == null) {
        setAccIbanLabel("Account Number/IBAN");
        setIntAccIbanLabel("Intermediary Account Number/Intermediary IBAN");
        setHasIban(true);
      } else if (has_iban == true) {
        setAccIbanLabel("IBAN");
        setIntAccIbanLabel("Intermediary IBAN");
        setHasIban(true);
      } else {
        setAccIbanLabel("Account Number");
        setIntAccIbanLabel("Intermediary Account Number");
        setHasIban(false);
      }

      if (sortCodeRegex.test(processedValue)) {
        setIsSwiftBICCode(false);
      } else if (swiftBicRegex.test(processedValue)) {
        setIsSwiftBICCode(true);
      } else {
        setIsSwiftBICCode(false);
      }
    } else {
      if (sortCodeRegex.test(processedValue)) {
        setAccIbanLabel("Account Number");
        setIntAccIbanLabel("Intermediary Account Number");
        setHasIban(false);
        setIsSwiftBICCode(false);
      } else if (swiftBicRegex.test(processedValue)) {
        setAccIbanLabel("IBAN");
        setIntAccIbanLabel("Intermediary IBAN");
        setHasIban(true);
        setIsSwiftBICCode(true);
      } else {
        setAccIbanLabel("Account Number/IBAN");
        setIntAccIbanLabel("Intermediary Account Number/Intermediary IBAN");
        setHasIban(true);
        setIsSwiftBICCode(false);
      }
    }

    setIsValidSortBic(true);
    setSortBicErrorMessage("");
    setIsValidateTeam(false);
  }

  useEffect(() => {
    if (
      (accIbanLabel == "Account Number" || accIbanLabel == "IBAN") &&
      accountNumber != "" &&
      accountNumber != undefined &&
      accountNumber != null
    ) {
      handleAccountNumberChange(accountNumber);
    } else {
      setAccountNumberErrorMessage("");
      setIsValidAccountNumber(true);
      setAccountNumber("");
    }
  }, [accIbanLabel]);

  useEffect(() => {
    if (
      (intAccIbanLabel == "Intermediary Account Number" ||
        intAccIbanLabel == "Intermediary IBAN") &&
      intermediaryBankAccount != "" &&
      intermediaryBankAccount != undefined &&
      intermediaryBankAccount != null
    ) {
      handleIntermediaryBankAccountNumberChange(intermediaryBankAccount);
    } else {
      setIntermediaryBankAccountErrorMessage("");
      setIsValidIntermediaryBankAccount(true);
      setIntermediaryBankAccount("");
    }
  }, [intAccIbanLabel]);

  function handleBankNameChange(e) {
    setFormChange(true);
    setBankName(e.target.value);
    if (
      e.target.value?.length > 50 ||
      !alphaNumericRegex.test(e.target.value)
    ) {
      setIsValidBankName(false);
    } else {
      setIsValidBankName(true);
      setIsValidateTeam(false);
    }
  }

  const handleDistributionChange = (value) => {
    setDistributionPoint(value);
  };

  function handleDeliveryTermsChange(e) {
    setFormChange(true);
    setDeliveryTerms(e.target.value);
    setIsValidDelivery(true);
  }
  function handleModeOfTransport(e) {
    setFormChange(true);
    setModeOfTransport(e.target.value);
    setIsValidModeOfTransport(true);
  }

  function handleAccountNumberChange(trimmedValue) {
    setFormChange(true);
    setAccountNumber(trimmedValue);
    const ibanNumberRegex = /^[A-Z]{2}\d{2}[A-Z0-9]{1,30}$/;
    const accountNumberRegex = /^\d{4,30}(?:-\d+)?$/;

    if (
      accIbanLabel == "Account Number" &&
      !accountNumberRegex.test(trimmedValue)
    ) {
      setIsValidAccountNumber(false);
      setAccountNumberErrorMessage("Invalid account number format.");
      return;
    } else if (accIbanLabel == "IBAN" && !ibanNumberRegex.test(trimmedValue)) {
      setIsValidAccountNumber(false);
      setAccountNumberErrorMessage("Invalid IBAN number format.");
      return;
    } else {
      setIsValidAccountNumber(true);
    }

    setAccountNumberErrorMessage("");
    setIsValidateTeam(false);
  }

  function handleValidate() {
    setFormChange(true);
    if (
      (sortBic && bankName && accountNumber) ||
      (!isValidAccountNumber &&
        !isValidBankName &&
        !isValidSortBic &&
        !isValidIntermediaryBankAccount)
    ) {
      setValidated(!validated);
      if (dbFinancialApproval == 1) {
        setFinancialApproval(2);
      }
      setIsValidateTeam(false);
      const date = new Date();
      const formattedDate = date.toLocaleDateString("en-GB", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
      });

      const validatedDataString = userData?.email + " " + formattedDate;
      setValidatedData(validatedDataString);
    } else {
      if (!accountNumber) {
        setIsValidAccountNumber(false);
        setAccountNumberErrorMessage("Please enter a valid account number");
      }
      if (!bankName) {
        setIsValidBankName(false);
      }
      if (!sortBic) {
        setIsValidSortBic(false);
        setSortBicErrorMessage("SORT BIC must be 8 or 11 characters long.");
      }
      setIsValidateTeam(true);
    }
  }

  function handleIntermediaryBankAccountNumberChange(trimmedValue) {
    setFormChange(true);

    setIntermediaryBankAccount(trimmedValue);
    const ibanNumberRegex = /^[A-Z]{2}\d{2}[A-Z0-9]{1,30}$/;
    const accountNumberRegex = /^\d{4,30}(?:-\d+)?$/;

    // if (
    //   data[0].country_code === "UK" &&
    //   !accountNumberRegex.test(trimmedValue)
    // ) {
    //   setIsValidIntermediaryBankAccount(false);
    //   setIntermediaryBankAccountErrorMessage(
    //     "Invalid account number format. Example 'XXXXXXXX'"
    //   );
    //   return;
    // } else if (
    //   data[0]?.country_code !== "UK" &&
    //   !ibanNumberRegex.test(trimmedValue)
    // ) {
    //   setIsValidIntermediaryBankAccount(false);
    //   setIntermediaryBankAccountErrorMessage("Invalid IBAN number format.");
    //   return;
    // } else {
    //   setIsValidIntermediaryBankAccount(true);
    // }

    if (
      intAccIbanLabel == "Intermediary Account Number" &&
      !accountNumberRegex.test(trimmedValue)
    ) {
      setIsValidIntermediaryBankAccount(false);
      setIntermediaryBankAccountErrorMessage("Invalid account number format.");
      return;
    } else if (
      intAccIbanLabel == "Intermediary IBAN" &&
      !ibanNumberRegex.test(trimmedValue)
    ) {
      setIsValidIntermediaryBankAccount(false);
      setIntermediaryBankAccountErrorMessage("Invalid IBAN number format.");
      return;
    } else {
      setIsValidIntermediaryBankAccount(true);
    }

    setIntermediaryBankAccountErrorMessage("");
    setIsValidateTeam(false);
  }
  useEffect(() => {
    const prophetsIdsLocalStorage = localStorage.getItem("prophet");
    const prophetsIds = prophetsIdsLocalStorage != null
      ?prophetsIdsLocalStorage
      : null;
    setProphetIds(prophetsIds);
    setSupplierData(data[0]);
    setVatNumber(data[0]?.vat_number);
    if (
      data[0].vat_number &&
      !vatRegex[data[0].country_name].test(data[0].vat_number)
    ) {
      setFormChange(true);
      setIsValidVatNumber(false);
      setVatNumberErrorMessage(
        "Please correct the vat number according to country"
      );
    }
    setVatable(data[0]?.vatable?.toString());
    setIsCodeSystemGenerated(data[0]?.is_code_system_generated);
    setCompanyRegistration(data[0]?.company_registration);
    setStatus(data[0]?.status ?? "");
    setCurrency(data[0]?.currency);
    setCurrencyName(data[0]?.currency_name);
    setCountryCode(data[0]?.country_code_id);
    setCountryCodeName(data[0]?.country_code_name);
    setGenCountryCode(data[0]?.country_code);
    setPaymentTerms(data[0]?.payment_terms);
    setPaymentType(data[0]?.payment_type);
    setPaymentTypeId(data[0]?.paymentTypeId);
    setDirectDP(data[0]?.direct_dp);
    setSortBic(data[0]?.decryptedSort_Bic);
    handleSortBICChange(data[0]?.decryptedSort_Bic, false, data[0].has_iban);
    setBankName(data[0]?.decryptedName_branch);
    setAccountNumber(data[0]?.decryptedAccountNumber);
    setIntermediaryBankAccount(data[0]?.decryptedIntermediatery_account_number);
    setFinancialApproval(data[0]?.finance_authorization);
    setdbFinancialApproval(data[0]?.finance_authorization);
    setRejectedReason(data[0]?.rejected_reason);
    setRejectedReasonapi(data[0]?.rejected_reason);
    setValidated(data[0]?.validated_procurement_team);
    setValidatedDate(data[0]?.validated_date);
    // if (data[0].has_iban != null) {
    //   setHasIban(data[0].has_iban);
    // }
    // console.log("data", data[0].has_iban);

    const roles = JSON.parse(data[0]?.role_ids ?? "[]");
    const role_ids = roles?.map((item) => item?.role_id);
    setRoleIds(role_ids);
    if (role_ids?.includes(5) && role_ids.length == 1) {
      setIsFinanceMandatory(false);
    }
    if (role_ids?.includes(6) && role_ids.length == 1) {
      setIsExpenseRole(true);
    }
    let validatedDataString;
    if (data[0]?.validated_date) {
      const date = new Date(data[0]?.validated_date);
      const formattedDate = date.toLocaleDateString("en-GB", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });

      validatedDataString = data[0]?.validated_by + " " + formattedDate;
    } else {
      validatedDataString = data[0]?.validated_by;
    }
    setValidatedData(validatedDataString);

    const distributionPointsJson = JSON.parse(
      data[0]?.distribution_points_json ?? "[]"
    );
    const deliveryJson = JSON.parse(data[0]?.delivery_json ?? "[]");

    const formattedDeliveryData = deliveryJson?.map((row) => ({
      delivery_terms: row?.delivery_terms,
      mode_of_transport: row?.mode_of_transport,
      id: row?.id,
    }));
    setDeliveryJson(formattedDeliveryData);

    const formattedDistributionData = distributionPointsJson?.map((row) => ({
      distributionPoint: row?.distribution_name,
      isOld: true,
      directDPvalue: row.direct_dp ? "Yes" : "No",
      directDP: row.direct_dp,
      dpId: row?.id,
    }));
    if (company == "dpsltd") {
      setIsDPRequired(formattedDistributionData.length > 0);
    }
    setDistributionDataJson(formattedDistributionData);

    const supplierName = data[0]?.name;
    const prophetIds = data[0]?.prophets_id_code;

    if (prophetIds) {
      const prophet = JSON.parse(prophetIds ?? "[]");

      const prophet_map = prophet?.map((item) => {

        return {
          prophet_id: item?.prophet_id,
          prophet_code: item?.prophet_code,
          supplier_id: parseInt(supplierId),
        };
      });
      setProphets(prophet_map);
    }
  }, [supplierId]);

  function closeModal(e) {
    if (e) {
      e.preventDefault();
    }
    setRejectedReason("");
    setIsOpen(false);
  }

  function saveModalData(e) {
    setFormChange(true);
    e.preventDefault();
    setRejectedReason(rejectedReasonapi);
    setIsOpen(false);
    setFinanceApprovalQue(true);
  }

  const defaultColDef = useMemo(() => ({
    //sortable: true,
    filter: false,
    resizable: true,
    flex: 1,
    suppressMenu: false,
  }));

  const deliveryColDef = [
    {
      field: "tableId",
      value: "delivery",
      hide: true,
    },
    {
      headerName: "Delivery Terms",
      field: "delivery_terms",
      headerClass: "header-with-border",
      flex: "1%",
      cellStyle: { display: "flex", alignItems: "center" },
    },
    {
      headerName: "Mode of Transport",
      field: "mode_of_transport",
      headerClass: "header-with-border",
      cellStyle: { display: "flex", alignItems: "center" },
    },
    {
      field: "",
      cellRenderer: DeliveryIconsRenderer,
      cellStyle: { display: "flex", justifyContent: "end" },
      headerClass: "header-with-border",
    },
  ];

  const distributionColDef = [
    {
      headerName: "Distribution Point",
      field: "distributionPoint",
      headerClass: "header-with-border",
      cellStyle: { display: "flex", alignItems: "center" },
    },
    {
      headerName: "Direct DP",
      field: "directDPvalue",
      headerClass: "header-with-border",
      cellStyle: { display: "flex", alignItems: "center" },
    },
    {
      field: "",
      cellRenderer: DistributionIconsRenderer,
      cellStyle: { display: "flex", justifyContent: "end" },
      headerClass: "header-with-border",
    },
    {
      field: "tableId",
      value: "distribution",
      hide: true,
    },
  ];

  const handleFinancialApprovalChange = (value) => {
    setFinancialApproval(value);
    if (value == 1) {
      setIsOpen(true);
    }
  };

  const handlehasIbanChange = (value) => {
    setHasIban(value);
    setHasIbanManualChange(true);
    if (value == false) {
      setAccIbanLabel("Account Number");
      setIntAccIbanLabel("Intermediary Account Number");
    } else {
      setAccIbanLabel("IBAN");
      setIntAccIbanLabel("Intermediary IBAN");
    }
  };

  return (
    <>
      <ToastContainer limit={1} />
      {loading ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "100vh",
          }}
        >
          <ThreeCircles
            color="#002D73"
            height={50}
            width={50}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      ) : (
        <>
          <div className="relative panel-container bg-white rounded-lg w-[94%] lg:w-[95%] 2xl:w-[calc(100%-70px)] mb-10 p-4 pb-0">
            <div className="m-3 mb-0">
              <div className="flex xl:flex-row flex-col mb-4">
                <div className="flex flex-col xl:w-1/2 xl:pe-8 xl:border-e-[1px]">
                  <div className="mb-6">
                    <div className="mb-3">
                      <h4 className="formtitle pb-1 border-b border-light-gray">
                        Billing
                      </h4>
                    </div>

                    <div className="grid lg:grid-cols-2 grid-cols-1 gap-4">
                      <div className="flex flex-col">
                        <div className="mb-1">
                          <label className="labels mb-1 ">Vatable?</label>{" "}
                          {isFinanceMandatory && !isExpenseRole && (
                            <span className="text-red-500">*</span>
                          )}
                        </div>

                        <select
                          name="Vatable"
                          value={vatable}
                          onChange={handleVatableChange}
                          className={`w-full px-2 2xl:px-3 border ${
                            !isValidVatable ? "!border-bright-red" : ""
                          } rounded-md`}
                        >
                          <option value="" disabled selected>
                            Select...
                          </option>
                          <option value="true">Vatable</option>
                          <option value="false">None vatable</option>
                        </select>
                        {!isValidVatable && (
                          <span className="text-red-500">
                            Please select if Vatable or not
                          </span>
                        )}
                      </div>
                      <div className="flex flex-col">
                        <div className="mb-1">
                          <label className="labels mb-1">
                            VAT Number{" "}
                            {isVatNumberMandatory && !isExpenseRole && (
                              <span className="text-red-500">*</span>
                            )}
                          </label>{" "}
                        </div>
                        <input
                          type="text"
                          name="vat_number"
                          maxLength={15}
                          value={vatNumber || ""}
                          ref={vatNumberRef}
                          onChange={handleVatNumberChange}
                          className={`w-full px-2 2xl:px-3 border ${
                            isValidVatNumber
                              ? "border-light-gray"
                              : "!border-bright-red"
                          } rounded-md inputs`}
                          style={{ textTransform: "uppercase" }}
                        />
                        {!isValidVatNumber && (
                          <span className="text-red-500">
                            {vatNumberErrorMessage}
                          </span>
                        )}
                      </div>

                      <div className="flex flex-col">
                        <div className="mb-1">
                          <label className="labels mb-1 ">
                            Company Registration{" "}
                            {isFinanceMandatory && !isExpenseRole && (
                              <span className="text-red-500">*</span>
                            )}
                          </label>
                        </div>
                        <input
                          type="text"
                          name="company_registration"
                          maxLength={50}
                          value={companyRegistration || ""}
                          ref={companyRegRef}
                          onChange={handleCompanyRegistrationChange}
                          className={`w-full px-2 2xl:px-3 border ${
                            isValidCompanyRegistration
                              ? "border-light-gray"
                              : "!border-bright-red"
                          }  rounded-md inputs`}
                          style={{ textTransform: "uppercase" }}
                        />
                        {!isValidCompanyRegistration && (
                          <span className="text-red-500">
                            {companyRegistrationErrorMessage}
                          </span>
                        )}
                      </div>

                      <div className="flex flex-col">
                        <div className="mb-1">
                          <label className="labels mb-2 ">Currency</label>{" "}
                          {isFinanceMandatory && (
                            <span className="text-red-500">*</span>
                          )}
                        </div>
                        <select
                          name="currency"
                          value={currency}
                          ref={currencyRef}
                          onChange={handleCurrencyChange}
                          className={`w-full px-2 2xl:px-3 border ${
                            !isValidCurrency ? "!border-bright-red" : ""
                          } rounded-md`}
                        >
                          <option value="" disabled>
                            Select...
                          </option>
                          {dropdowns?.currencies &&
                            dropdowns?.currencies.map((currency, key) => {
                              let isCodeExist = currency.internal_ledger_code
                                ? true
                                : false;
                              return (
                                <option key={key} value={currency.id}>
                                  {currency.name}{" "}
                                  {isCodeExist
                                    ? "(" + currency.internal_ledger_code + ")"
                                    : ""}
                                </option>
                              );
                            })}
                        </select>
                        {!isValidCurrency ? (
                          <span className="text-red-500">
                            Please select a currency.
                          </span>
                        ) : (
                          ""
                        )}
                      </div>

                      <div className="flex flex-col">
                        <div className="mb-1">
                          <label className="labels mb-1 ">Country Code</label>{" "}
                          {isFinanceMandatory && (
                            <span className="text-red-500">*</span>
                          )}
                        </div>
                        <select
                          name="country code"
                          value={countryCode}
                          ref={countryCodeRef}
                          onChange={handleCountryCodeChange}
                          className={`w-full px-2 2xl:px-3 border ${
                            !isValidCountryCode ? "!border-bright-red" : ""
                          }  rounded-md`}
                        >
                          <option value="" disabled>
                            Select...
                          </option>
                          {dropdowns?.countries &&
                            dropdowns?.countries.map((country_code, key) => {
                              return (
                                <option key={key} value={country_code.id}>
                                  {`${country_code.code}-${country_code.name}`}
                                </option>
                              );
                            })}
                        </select>
                        {!isValidCountryCode && (
                          <span className="text-red-500">
                            Please select a country code.
                          </span>
                        )}
                      </div>

                      <div className="flex flex-col">
                        <div className="mb-1">
                          <label className="labels mb-1 ">Payment Terms</label>{" "}
                          {isFinanceMandatory && (
                            <span className="text-red-500">*</span>
                          )}
                        </div>
                        <input
                          type="number"
                          name="payment_terms"
                          maxLength={5}
                          value={paymentTerms || ""}
                          ref={paymentTermsRef}
                          onChange={handlePaymentTermsChange}
                          className={`w-full py-1 px-2 2xl:px-3 border ${
                            !isValidPaymentTerms ? "!border-bright-red" : ""
                          } rounded-md inputs`}
                        />
                        {!isValidPaymentTerms && (
                          <span className="text-red-500">
                            Please enter valid payment terms of max 50 chars.
                          </span>
                        )}
                      </div>

                      <div className="flex flex-col">
                        <div className="mb-1">
                          <label className="labels mb-1 ">Payment Type</label>{" "}
                          {isFinanceMandatory && (
                            <span className="text-red-500">*</span>
                          )}
                        </div>

                        <select
                          name="payment type"
                          value={paymentTypeId}
                          ref={paymentTypeRef}
                          onChange={handlePaymentTypeChange}
                          className={`w-full px-2 2xl:px-3 border ${
                            !isValidPaymentType ? "!border-bright-red" : ""
                          } rounded-md`}
                        >
                          <option value="" disabled>
                            Select...
                          </option>

                          {dropdowns?.payment_types &&
                            dropdowns?.payment_types.map(
                              (payment_type, key) => {
                                return (
                                  <option key={key} value={payment_type.id}>
                                    {payment_type.name}
                                  </option>
                                );
                              }
                            )}
                          {/* <option value="BACAC">BACAC</option>
                        <option value="BACAC">BACAC</option>
                        <option value="BACAC">BACAC</option> */}
                        </select>
                        {!isValidPaymentType && (
                          <span className="text-red-500">
                            {paymentTermsErrorMessage}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <div className="mb-3">
                      <h4 className="formtitle pb-1 border-b border-light-gray">
                        Banking
                      </h4>
                    </div>
                    {/* //todo: If the country is UK then Sort code and account number will be mandatory and BIC and iban will be disabled and autogenerated based on country code,sort and account number */}
                    {/* //todo: if the country is other UK then BIC and IBAN will be mandatory and Sort code and account number will be disabled and will be filled with dummy data */}
                    <div className="grid lg:grid-cols-2 grid-cols-1 gap-4">
                      {/* //!Sort code */}
                      <div className="flex flex-col">
                        <div className="mb-1">
                          <label className="text-gray-600 mb-1 labels">
                            {/* {data[0].country_code == "UK" || data[0].currency_code=="GBP"
                              ? "Sort Code"
                              : "SWIFT or BIC"} */}
                            Sort Code/SWIFT or BIC
                            {isFinanceMandatory && (
                              <span className="text-red-500">*</span>
                            )}
                          </label>
                        </div>
                        <input
                          type="text"
                          name="sort_bic"
                          maxLength={11}
                          value={sortBic || ""}
                          ref={sortBicRef}
                          disabled={
                            validated ||
                            userData?.email !== data[0]?.requestor_email
                          }
                          placeholder=""
                          onChange={(e) =>
                            handleSortBICChange(
                              e.target.value
                                .toUpperCase()
                                .trim()
                                .replace(/\s/g, ""),
                              true
                            )
                          }
                          className={`w-full py-1 px-2 2xl:px-3 border border-light-gray rounded-md  inputs ${
                            validated
                              ? "!text-gray-500 bg-gray-100"
                              : "bg-white"
                          } ${!isValidSortBic ? "!border-bright-red" : ""}`}
                          style={{ textTransform: "uppercase" }}
                        />
                        {!isValidSortBic && (
                          <span className="text-red-500">
                            {sortBicErrorMessage}
                          </span>
                        )}
                      </div>
                      {/* //!Bank name and branch */}
                      <div className="flex flex-col">
                        <div className="mb-1">
                          <label className="text-gray-600 mb-1 labels">
                            Bank Name & Branch{" "}
                            {isFinanceMandatory && (
                              <span className="text-red-500">*</span>
                            )}
                          </label>
                        </div>
                        <input
                          type="text"
                          name="bank_name"
                          maxLength={50}
                          value={bankName || ""}
                          ref={bankNameRef}
                          disabled={
                            validated ||
                            userData?.email !== data[0]?.requestor_email
                          }
                          onChange={handleBankNameChange}
                          className={`w-full py-1 px-2 2xl:px-3 border border-light-gray rounded-md bg-gray-100 inputs ${
                            validated
                              ? "!text-gray-500 bg-gray-100"
                              : "bg-white"
                          } ${!isValidBankName ? "!border-bright-red" : ""}`}
                        />
                        {!isValidBankName && (
                          <span className="text-red-500">
                            Please enter a valid name of max 50 chars.
                          </span>
                        )}
                      </div>
                      {isSwiftBICCode && (
                        <div className="flex flex-col col-span-2">
                          <div className="mb-1">
                            <label className="text-gray-600 mb-1 labels">
                              Does the Supplier have an IBAN?
                            </label>
                          </div>
                          <div className="flex md:flex-row flex-col flex-wrap items-center">
                            <div className="flex justify-center">
                              <input
                                id="yesiban-radio"
                                type="radio"
                                value="yes"
                                checked={hasIban === true}
                                onChange={() => {
                                  setFormChange(true);
                                  handlehasIbanChange(true);
                                }}
                                className="mt-[2px] ml-2 w-4 h-4 text-skin-a11y bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 accent-skin-primary"
                                disabled={
                                  validated ||
                                  userData?.email !== data[0]?.requestor_email
                                }
                              />
                              <label
                                htmlFor="yesiban-radio"
                                className="p-0 ml-2 me-5 text-blackcolor"
                              >
                                Yes
                              </label>
                            </div>
                            <div className="flex justify-center">
                              <input
                                id="noiban-radio"
                                type="radio"
                                value="no"
                                checked={hasIban === false}
                                onChange={() => {
                                  setFormChange(true);
                                  handlehasIbanChange(false);
                                }}
                                disabled={
                                  validated ||
                                  userData?.email !== data[0]?.requestor_email
                                }
                                className="mt-[2px] ml-2 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 inputs accent-skin-primary"
                              />
                              <label
                                htmlFor="noiban-radio"
                                className="p-0 ml-2 text-blackcolor"
                              >
                                No
                              </label>
                            </div>
                          </div>
                        </div>
                      )}
                      {/* //!Swift */}
                      <div className="flex flex-col">
                        <div className="mb-1">
                          <label className="text-gray-600 mb-1 labels">
                            {/* {data[0]?.country_code == "UK"
                              ? "Account number"
                              : "IBAN"} */}
                            {accIbanLabel}
                            {isFinanceMandatory && (
                              <span className="text-red-500">*</span>
                            )}
                          </label>
                        </div>
                        <input
                          type="text"
                          name="account_number"
                          maxLength={30}
                          value={accountNumber || ""}
                          ref={accountNumberRef}
                          disabled={
                            (accIbanLabel != "Account Number" &&
                              accIbanLabel != "IBAN") ||
                            validated ||
                            userData?.email !== data[0]?.requestor_email
                          }
                          onChange={(e) =>
                            handleAccountNumberChange(
                              e.target.value
                                .toUpperCase()
                                .trim()
                                .replace(/\s/g, "")
                            )
                          }
                          className={`w-full py-1 px-2 2xl:px-3 border border-light-gray rounded-md bg-gray-100 inputs ${
                            validated
                              ? "!text-gray-500 bg-gray-100"
                              : "bg-white"
                          } ${
                            !isValidAccountNumber ? "!border-bright-red" : ""
                          }`}
                        />
                        {!isValidAccountNumber && (
                          <span className="text-red-500">
                            {accountNumberErrorMessage}
                          </span>
                        )}
                      </div>

                      {/* //!Interemediary */}
                      <div className="flex flex-col">
                        <label className="text-gray-600 mb-1 labels">
                          {intAccIbanLabel}
                        </label>
                        <input
                          type="text"
                          name="intermediary_bank_account"
                          maxLength={50}
                          ref={intermediaryBankAccountRef}
                          value={intermediaryBankAccount || ""}
                          disabled={
                            (intAccIbanLabel != "Intermediary Account Number" &&
                              intAccIbanLabel != "Intermediary IBAN") ||
                            validated ||
                            userData?.email !== data[0]?.requestor_email
                          }
                          onChange={(e) =>
                            handleIntermediaryBankAccountNumberChange(
                              e.target.value
                                .toUpperCase()
                                .trim()
                                .replace(/\s/g, "")
                            )
                          }
                          className={`w-full py-1 px-2 2xl:px-3 border border-light-gray rounded-md bg-gray-100 inputs ${
                            validated
                              ? "!text-gray-500 bg-gray-100"
                              : "bg-white"
                          }`}
                        />
                        {!isValidIntermediaryBankAccount && (
                          <span className="text-red-500">
                            {intermediaryBankAccountErrorMessage}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col gap-4 mt-6">
                      <div className="flex items-center w-full">
                        <input
                          id="checked-checkbox"
                          type="checkbox"
                          value={validated}
                          checked={validated}
                          disabled={
                            !sortBic ||
                            !bankName ||
                            !accountNumber ||
                            !isValidAccountNumber ||
                            !isValidBankName ||
                            !isValidSortBic ||
                            !isValidIntermediaryBankAccount ||
                            dbFinancialApproval == 0 ||
                            userData?.email !== data[0]?.requestor_email
                          }
                          onChange={handleValidate}
                          className="w-5 h-5 text-blue bg-white border-blue-300 rounded accent-skin-primary"
                        />
                        <label
                          htmlFor="checked-checkbox"
                          className="p-0 ml-3 labels"
                        >
                          Validated By
                        </label>{" "}
                        <p className="ml-3">{validated ? validatedData : ""}</p>
                      </div>
                      <div className="flex flex-col md:flex-row md:items-center">
                        <label className="me-5 labels">
                          Financial Team Approval
                        </label>
                        <div className="flex md:flex-row flex-col flex-wrap items-center">
                          <div className="flex justify-center">
                            <input
                              id="approved-radio"
                              type="radio"
                              value="approved"
                              checked={financialApproval === 0}
                              disabled={
                                dbFinancialApproval === 0 ||
                                dbFinancialApproval === 1 ||
                                financeApprovalQue ||
                                !(
                                  userData?.role_id === 2 ||
                                  userData?.role_id === 1 ||
                                  userData?.role_id === 5 ||
                                  userData?.role_id === 6
                                ) ||
                                !validated ||
                                userData?.email === data[0].requestor_email
                              }
                              onChange={() => {
                                setFormChange(true);
                                handleFinancialApprovalChange(0);
                              }}
                              className="mt-[2px] ml-2 w-4 h-4 text-skin-a11y bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 accent-skin-primary"
                            />
                            <label
                              htmlFor="approved-radio"
                              className="p-0 ml-2 me-5 text-blackcolor"
                            >
                              Approved
                            </label>
                          </div>

                          <div className="flex">
                            <button
                              data-modal-target="default-modal"
                              data-modal-toggle="default-modal"
                              className="flex flex-row items-center"
                              type="button"
                            >
                              <input
                                id="rejected-radio"
                                type="radio"
                                value="rejected"
                                disabled={
                                  dbFinancialApproval === 0 ||
                                  dbFinancialApproval === 1 ||
                                  financeApprovalQue ||
                                  !(
                                    userData?.role_id === 2 ||
                                    userData?.role_id === 1 ||
                                    userData?.role_id === 5 ||
                                    userData?.role_id === 6
                                  ) ||
                                  !validated ||
                                  userData?.email === data[0].requestor_email
                                }
                                checked={financialApproval === 1}
                                onChange={() => {
                                  setFormChange(true);
                                  handleFinancialApprovalChange(1);
                                }}
                                className="ml-3 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 inputs accent-skin-primary"
                              />
                            </button>
                            <label
                              htmlFor="rejected-radio"
                              className="p-0 ml-2 me-5 text-blackcolor"
                            >
                              Rejected
                            </label>
                          </div>
                          <div className="flex justify-center">
                            <input
                              id="yet-to-review-radio"
                              type="radio"
                              value="yetToReview"
                              checked={financialApproval === 2}
                              disabled={
                                dbFinancialApproval === 0 ||
                                financeApprovalQue ||
                                dbFinancialApproval === 1 ||
                                !(
                                  userData?.role_id === 2 ||
                                  userData?.role_id === 1 ||
                                  userData?.role_id === 5 ||
                                  userData?.role_id === 6
                                ) ||
                                !validated ||
                                userData?.email === data[0].requestor_email
                              }
                              onChange={() => {
                                setFormChange(true);
                                handleFinancialApprovalChange(2);
                              }}
                              className="mt-[2px] ml-2 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 inputs accent-skin-primary"
                            />
                            <label
                              htmlFor="yet-to-review-radio"
                              className="p-0 ml-2 text-blackcolor"
                            >
                              Yet to Review
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col xl:w-1/2 xl:ps-8">
                  <Disclosure defaultOpen={true}>
                    {({ open }) => (
                      <>
                        <Disclosure.Button>
                          <div className="relative flex flex-row justify-between w-full mb-3 border-b border-light-gray">
                            <h4 className="accordion active formtitle pb-1">
                              Delivery
                            </h4>
                            <div className="absolute right-10 -top-2 pb-4">
                              <span className="block w-[160px] py-1 px-4 bg-skin-primary text-white rounded-full text-sm">
                                {deliveryJson?.length} Delivery Contact(s)
                              </span>
                            </div>
                            {open ? (
                              <FontAwesomeIcon
                                icon={faChevronDown}
                                className="rotate-180"
                              />
                            ) : (
                              <FontAwesomeIcon icon={faChevronDown} />
                            )}
                          </div>
                        </Disclosure.Button>
                        <Disclosure.Panel>
                          <div
                            className="flex flex-row panel"
                            style={{ maxHeight: "100%" }}
                          >
                            <div className="flex gap-4 mb-6 md:w-full lg:w-full xl:w-[100%] relative">
                              <div className="flex flex-col w-full relative">
                                <label className="text-gray-600 mb-1 labels">
                                  Delivery Terms
                                </label>
                                <select
                                  type="text"
                                  name="delivery_terms"
                                  value={delivery_terms}
                                  className={`w-full px-2 2xl:px-3 border ${
                                    !isValidDelivery ? "!border-bright-red" : ""
                                  } rounded-md`}
                                  onChange={handleDeliveryTermsChange}
                                >
                                  <option value="" disabled>
                                    Select...
                                  </option>
                                  {dropdowns?.delivery_terms &&
                                    dropdowns?.delivery_terms.map(
                                      (term, key) => {
                                        return (
                                          <option key={key} value={term.name}>
                                            {term.name}
                                          </option>
                                        );
                                      }
                                    )}
                                </select>
                                {!isValidDelivery && (
                                  <span className="absolute text-red-500 top-full">
                                    Please select a delivery term.
                                  </span>
                                )}{" "}
                              </div>
                              <div className="flex flex-col w-full relative">
                                <label className="text-gray-600 mb-1 labels">
                                  Mode of Transport
                                </label>
                                <select
                                  type="text"
                                  name="mode_transport"
                                  value={mode_of_transport}
                                  onChange={handleModeOfTransport}
                                  className={`w-full px-2 2xl:px-3 border ${
                                    !isValidModeOfTransport
                                      ? "!border-bright-red"
                                      : ""
                                  } rounded-md`}
                                >
                                  <option value="" disabled>
                                    Select...
                                  </option>
                                  {dropdowns?.transports &&
                                    dropdowns?.transports.map((mode, key) => {
                                      return (
                                        <option key={key} value={mode.name}>
                                          {mode.name}
                                        </option>
                                      );
                                    })}
                                </select>
                                {!isValidModeOfTransport && (
                                  <span className="absolute text-red-500 top-full">
                                    Please select a mode of transport.
                                  </span>
                                )}
                              </div>
                              <div
                                className="flex w-[10%] items-end justify-center"
                                onClick={saveData}
                              >
                                <span className="flex relative -top-[0.9] px-2 py-1 2xl:px-3.5 2xl:py-1 border border-skin-primary rounded-md text-skin-primary cursor-pointer">
                                  <FontAwesomeIcon
                                    icon={faFloppyDisk}
                                    className="mb-1 mt-1"
                                  />
                                </span>
                              </div>
                            </div>
                          </div>
                          <div
                            style={{ height: 140, width: "100%" }}
                            className="border-b border-light-gray"
                          >
                            <AgGridReact
                              id="1"
                              ref={gridRefDelivery}
                              defaultColDef={defaultColDef}
                              columnDefs={deliveryColDef}
                              rowData={deliveryJson ? deliveryJson : []}
                              rowHeight={25}
                            />
                          </div>
                        </Disclosure.Panel>
                      </>
                    )}
                  </Disclosure>
                  {company == "dpsltd" && (
                    <div className="flex flex-row col-span-2 my-2">
                      <div className="mb-1">
                        <label className="text-gray-600 mb-1 labels">
                          Does Supplier send Products directly to the End
                          Customer?
                        </label>
                      </div>
                      <div className="flex md:flex-row flex-col flex-wrap items-center">
                        <div className="flex justify-center">
                          <input
                            id="yes-radio"
                            type="radio"
                            value="yes"
                            checked={isDPRequired === true}
                            onChange={() => {
                              setIsDPRequired(true);
                            }}
                            className="mt-[2px] ml-2 w-4 h-4 text-skin-a11y bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 accent-skin-primary"
                          />
                          <label
                            htmlFor="yes-radio"
                            className="p-0 ml-2 me-5 text-blackcolor"
                          >
                            Yes
                          </label>
                        </div>
                        <div className="flex justify-center">
                          <input
                            id="no-radio"
                            type="radio"
                            value="no"
                            checked={isDPRequired === false}
                            onChange={() => {
                              setIsDPRequired(false);
                            }}
                            className="mt-[2px] ml-2 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 inputs accent-skin-primary"
                          />
                          <label
                            htmlFor="no-radio"
                            className="p-0 ml-2 text-blackcolor"
                          >
                            No
                          </label>
                        </div>
                      </div>
                    </div>
                  )}
                  {isDPRequired && (
                    <>
                      {/* <div className="relative flex flex-row justify-between mb-3">
                    <h4 className="accordion active formtitle pb-1 border-b border-light-gray">
                      Distribution Point
                      {" "}<span className="text-red-500">*</span>
                    </h4>
                  </div> */}
                      <div
                        className="panel"
                        style={{ maxHeight: "100%", overflow: "visible" }}
                      >
                        <div className="flex gap-4 mb-4 xl:w-[100%] relative">
                          <div className="flex flex-col w-full relative pl-[2px]">
                            <label className="text-gray-600 mb-2 labels">
                              Distribution Point Name{" "}
                              <span className="text-red-500">*</span>
                            </label>

                            <DebouncedAutocompleteDistribution
                              onChange={handleDistributionChange}
                              distribution={distributionPoint}
                              setDistributionPoint={setDistributionPoint}
                              prophetsIds={prophetsIds}
                              supplierId={supplierId}
                              setIsValidDistribution={setIsValidDistribution}
                              isValidDistribution={isValidDistribution}
                              setDistributionErrorMessage={
                                setDistributionErrorMessage
                              }
                              distributionErrorMessage={distriutionErrorMessage}
                              user={userData}
                              disabled={
                                distributionPointsJson?.length >= 1
                                  ? true
                                  : false
                              }
                              isDistributionDisabled={isDistributionDisabled}
                              setDistributionDisabled={setDistributionDisabled}
                            />
                          </div>

                          <div
                            className={`flex w-[10%] ${
                              !isValidDistribution
                                ? "items-center pt-1"
                                : "items-end"
                            } justify-center ${
                              isDistributionDisabled
                                ? "opacity-50 cursor-not-allowed"
                                : ""
                            }`}
                            onClick={saveDistributionData}
                          >
                            <span
                              className={`relative -top-[2px] px-2 py-1 2xl:px-3.5 2xl:py-1 border border-skin-primary rounded-md text-skin-primary ${
                                isDistributionDisabled
                                  ? "cursor-not-allowed"
                                  : "cursor-pointer"
                              }  absolute`}
                            >
                              <FontAwesomeIcon icon={faFloppyDisk} />
                            </span>
                          </div>
                        </div>
                        <div className="flex flex-row justify-start items-center">
                          <label className="text-gray-600 mb-0 me-10 labels">
                            Direct DP?
                          </label>
                          <div>
                            <label className="relative inline-flex items-center cursor-pointer py-[5px]">
                              <input
                                type="checkbox"
                                value={directDP}
                                checked={directDP}
                                onChange={() => setDirectDP(!directDP)}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 rounded-full peer after:translate-x-0 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-2 after:left-[1px] peer-checked:after:left-[3px] after:bg-white after:border-light-gray after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600 inputs"></div>
                            </label>
                          </div>
                        </div>
                      </div>
                      <div
                        className="distribution-point"
                        style={{ height: 200, width: "100%" }}
                      >
                        <AgGridReact
                          id="2"
                          defaultColDef={defaultColDef}
                          columnDefs={distributionColDef}
                          ref={gridRefDistribution}
                          rowData={
                            distributionPointsJson ? distributionPointsJson : []
                          }
                          rowHeight={25}
                        />
                      </div>
                    </>
                  )}
                </div>
              </div>

              <div className="flex justify-between border-t border-light-gray py-5 bg-white">
                <div>
                  <button
                    className="border border-skin-primary text-skin-primary rounded-md py-1 px-8"
                    onClick={onPrev}
                  >
                    Previous
                  </button>
                </div>
                <div>
                  <button
                    className="border border-skin-primary text-skin-primary me-10 py-1 px-8 font-medium rounded-md"
                    onClick={(e) => validate("sae", e)}
                  >
                    Save & Exit
                  </button>
                  <button
                    className="border border-skin-primary bg-skin-primary text-white rounded-md py-1 px-8 font-medium"
                    onClick={(e) => validate("sap", e)}
                  >
                    Save & Proceed
                  </button>
                </div>
              </div>
            </div>
          </div>
          <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-10" onClose={closeModal}>
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
              </Transition.Child>

              <div className="fixed inset-0 overflow-y-auto">
                <div className="flex items-center justify-center min-h-full p-4 text-center">
                  <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0 scale-95"
                    enterTo="opacity-100 scale-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100 scale-100"
                    leaveTo="opacity-0 scale-95"
                  >
                    <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                      {/* <!-- Modal content --> */}
                      <div className="relative bg-white rounded-lg shadow">
                        {/* <!-- Modal header --> */}
                        <div className="flex items-start justify-between p-8 rounded-t">
                          <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                            <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                              <FontAwesomeIcon icon={faInfo} />{" "}
                            </span>{" "}
                            Notify issue
                          </h3>
                          <button
                            onClick={closeModal}
                            type="button"
                            className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                            data-modal-hide="default-modal"
                          >
                            <FontAwesomeIcon
                              icon={faXmark}
                              className="text-skin-primary"
                            />{" "}
                          </button>
                        </div>
                        {/* <!-- Modal body --> */}
                        <div className="p-8 py-0 space-y-6">
                          <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                            Notify the procurement team about any issue in the
                            details in the banking section
                          </p>
                          <textarea
                            className="flex flex-col w-full rounded-md p-2 px-3 border border-light-gray2"
                            rows="8"
                            value={rejectedReasonapi}
                            onChange={(e) => {
                              setFormChange(true);
                              setRejectedReasonapi(e.target.value);
                            }}
                            placeholder="Any details about the issue"
                          ></textarea>
                        </div>
                        {/* <!-- Modal footer --> */}
                        <div className="flex items-end p-6 space-x-2 justify-end">
                          <button
                            onClick={saveModalData}
                            data-modal-hide="default-modal"
                            type="button"
                            className="text-skin-a11y bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                          >
                            Save
                          </button>
                        </div>
                      </div>
                    </Dialog.Panel>
                  </Transition.Child>
                </div>
              </div>
            </Dialog>
          </Transition>

          <Transition appear show={isPopupOpen} as={Fragment}>
            <Dialog
              as="div"
              className="relative z-10"
              onClose={closePopupModal}
            >
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
              </Transition.Child>

              <div className="fixed inset-0 overflow-y-auto">
                <div className="flex items-center justify-center min-h-full p-4 text-center">
                  <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0 scale-95"
                    enterTo="opacity-100 scale-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100 scale-100"
                    leaveTo="opacity-0 scale-95"
                  >
                    <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                      {/* <!-- Modal content --> */}
                      <div className="relative bg-white rounded-lg shadow">
                        {/* <!-- Modal header --> */}
                        <div className="flex items-start justify-between p-8 rounded-t">
                          <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                            <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                              <FontAwesomeIcon icon={faInfo} />{" "}
                            </span>{" "}
                            Warning
                          </h3>
                          <button
                            onClick={closePopupModal}
                            type="button"
                            className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                            data-modal-hide="default-modal"
                          >
                            <FontAwesomeIcon
                              icon={faXmark}
                              className="text-skin-primary"
                            />{" "}
                          </button>
                        </div>
                        {/* <!-- Modal body --> */}
                        <div className="p-8 py-0 space-y-6">
                          <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                            Mandatory information missing. Do you want to
                            continue?
                          </p>
                        </div>
                        {/* <!-- Modal footer --> */}
                        <div className="flex items-end p-6 space-x-2 justify-end">
                          <button
                            onClick={closePopupModal}
                            data-modal-hide="default-modal"
                            type="button"
                            className="border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                          >
                            Cancel
                          </button>
                          <button
                            onClick={handleContinueSubmit}
                            data-modal-hide="default-modal"
                            type="button"
                            className="text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                          >
                            Continue
                          </button>
                        </div>
                      </div>
                    </Dialog.Panel>
                  </Transition.Child>
                </div>
              </div>
            </Dialog>
          </Transition>
        </>
      )}
    </>
  );
};

export default FinancialsSection;
