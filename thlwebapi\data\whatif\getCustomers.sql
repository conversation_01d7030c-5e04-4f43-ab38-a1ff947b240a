DECLARE @CurrentDate DATE = GETDATE();
DECLARE @CurrentFinancialYearFirstDate CHAR(8);
DECLARE @NextFinancialYearFirstDate CHAR(8);

-- Calculate the first date of the current financial year
SET @CurrentFinancialYearFirstDate = 
    CASE 
        WHEN MONTH(@CurrentDate) >= 10 THEN 
            CONVERT(CHAR(8), CAST(YEAR(@CurrentDate) AS CHAR(4)) + '1001', 112)
        ELSE 
            CONVERT(CHAR(8), CAST(YEAR(@CurrentDate) - 1 AS CHAR(4)) + '1001', 112)
    END;


;with cte as

(      select 

                   

                     ff_actuals.hocustcode_customer

           

  FROM [FLR_DEV_TEST_off_BI_lookup].[dbo].[finance_fact] ff_actuals

       left outer join [FLR_DEV_TEST_off_BI_lookup].[dbo].[finance_additional_costings_base] add_cost_base on

          ff_actuals.orddetid = add_cost_base.orddetid and

          ff_actuals.lotdetid = add_cost_base.lotdetid

       inner join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_product_grouping] pg on ff_actuals.altfilid = pg.altfilid

       inner join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_altfil] alt on  ff_actuals.altfilid  = alt.altfilid

       inner join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_product_lvl_group] plg on ff_actuals.prodnum =  plg.prodnum

       inner join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_prod_group_desc] pgd on plg.mascode = pgd.mascode                                                                    

  where ff_actuals.deldate >= @CurrentFinancialYearFirstDate

  and  ff_actuals.deldate < cast(getdate() as date)

  and ff_actuals.hocustcode_customer not in ('DUMPED','RETURN','SAMPLE')

 

union all

 

       select 

                     ff_actuals.hocustcode_customer

           

  FROM [FLR_DEV_TEST_off_BI_lookup].[dbo].[finance_fact] ff_actuals

       left outer join [FLR_DEV_TEST_off_BI_lookup].[dbo].[finance_additional_costings_base] add_cost_base on

          ff_actuals.orddetid = add_cost_base.orddetid and

          ff_actuals.lotdetid = add_cost_base.lotdetid

       inner join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_product_grouping] pg on ff_actuals.altfilid = pg.altfilid

       inner join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_altfil] alt on  ff_actuals.altfilid  = alt.altfilid

       inner join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_product_lvl_group] plg on ff_actuals.prodnum =  plg.prodnum

       inner join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_prod_group_desc] pgd on plg.mascode = pgd.mascode                                                                    

  where ff_actuals.deldate >= @CurrentFinancialYearFirstDate

  and  ff_actuals.deldate < cast(getdate() as date)

  and ff_actuals.hocustcode_customer in ('DUMPED','RETURN','SAMPLE')

 

union all

 

select 

                     fcast_daily.hocust_code

                    

from [FLR_DEV_TEST_off_BI_lookup].[dbo].[forecast_daily] fcast_daily

       inner join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_product_grouping] pg on fcast_daily.altfilid = pg.altfilid

       inner join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_altfil] alt on  fcast_daily.altfilid  = alt.altfilid

       inner join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_product_lvl_group] plg on fcast_daily.product_number =  plg.prodnum

       inner join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_prod_group_desc] pgd on plg.mascode = pgd.mascode

       left outer join [FLR_DEV_TEST_off_BI_lookup].[dbo].[finance_gross_margin] fgm on fcast_daily.altfilid = fgm.altfilid and

                                    fcast_daily.forecast_date between fgm.gm_start_date and fgm.gm_end_date and

                                    fcast_daily.hocust_code = fgm.hocustcode

where fcast_daily.forecast_date >= cast(getdate() as date)

  and  fcast_daily.forecast_date < (select distinct [fcastendate] from [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_fcastenddate])

  and  fcast_daily.forecast_unit_qty > 0

 

UNION ALL

 

select 

                     jbhocustcode

                    

              FROM [FLR_DEV_TEST_off_BI_lookup].[dbo].[finance_fact] ff_actuals

                     inner join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_product_lvl_group] plg on ff_actuals.prodnum =  plg.prodnum

                     inner join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_prod_group_desc] pgd on plg.mascode = pgd.mascode

       left outer join [FLR_DEV_TEST_off_BI_lookup].[dbo].[finance_additional_costings_base] add_cost_base on

          ff_actuals.orddetid = add_cost_base.orddetid and

          ff_actuals.lotdetid = add_cost_base.lotdetid                                                                  

  where ff_actuals.deldate >= @CurrentFinancialYearFirstDate

  and  ff_actuals.deldate < cast(getdate() as date)

  and custcode='WASTE'

 

  union all

 

  select      

                     hocustcode_customer

  FROM [FLR_DEV_TEST_off_BI_lookup].[dbo].[finance_fact] ff_actuals

       left outer join [FLR_DEV_TEST_off_BI_lookup].[dbo].[finance_additional_costings_base] add_cost_base on

          ff_actuals.orddetid = add_cost_base.orddetid and

          ff_actuals.lotdetid = add_cost_base.lotdetid

       inner join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_product_lvl_group] plg on ff_actuals.prodnum =  plg.prodnum

       inner join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_prod_group_desc] pgd on plg.mascode = pgd.mascode                                                                    

  where ff_actuals.deldate >= @CurrentFinancialYearFirstDate

  and  ff_actuals.deldate < cast(getdate() as date)

  AND custcode='SHORTW'

)

select distinct hocustcode_customer as value, hocustcode_customer as label

from cte