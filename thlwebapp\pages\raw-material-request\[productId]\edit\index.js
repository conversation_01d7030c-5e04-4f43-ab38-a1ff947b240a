import Layout from "@/components/Layout";
import RawMaterialRequest from "@/components/RawMaterialRequest";
import { apiConfig } from "@/services/apiConfig";
import { extractCompanyFromEmail } from "@/utils/extractCompanyFromEmail";
import { logout } from "@/utils/secureStorage";
import Cookies from "js-cookie";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { ThreeCircles } from "react-loader-spinner";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const index = ({ userData }) => {
  const [dropdowns, setDropdowns] = useState(null);
  const [rawMaterialData, setRawMaterialData] = useState(null);
  const router = useRouter();
  const { productId } = router.query;

  useEffect(() => {
    const company=userData?.company || extractCompanyFromEmail(userData?.email);
    let prophetId = 1;
    if (company == "dpsltd") {
      prophetId = 1;
    } else if (company == "efcltd") {
      prophetId = 3;
    } else if (company == "fpp-ltd") {
      prophetId = 4;
    }
    const fetchData = async () => {
      const serverAddress = apiConfig.serverAddress;

      try {
        const allDropDowns = [
          "reasonForRequest",
          "masterProductCode",
          "markVariety",
          "productType",
          "organicCertification",
          "temperatureGrade",
          "intrastatCommodityCode",
          "classifiedAllergicTypes",
          "countryOfOrigin",
          "brand",
          "caliberSize",
          "endCustomer",
          "variety",
          "subProductCode",
          // "sort_group"
        ];

        const productType = 0;
        const dropdownsRequest = await fetch(
          `${serverAddress}products/get-products-dropdowns-list?prophetId=${prophetId}&productType=${productType}`,
          {
            method: "POST",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
            },
            credentials: "include",
            body: JSON.stringify(allDropDowns),
          }
        );
        // console.log("dropdownsRequest",dropdownsRequest);
        if (dropdownsRequest.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async() => {
            await logout();
            const redirectUrl = `/login?redirect=${encodeURIComponent(
              window.location.pathname
            )}`;
            router.push(redirectUrl);
            }, 3000);
        }
        if (dropdownsRequest.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        }

        const rawMaterialRequest = fetch(
          `${serverAddress}products/get-raw-materials-by-id/${productId}`,
          {
            method: "GET",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
            },
            credentials: "include",
          }
        );
        if (rawMaterialRequest.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async() => {
            await logout();
            const redirectUrl = `/login?redirect=${encodeURIComponent(
              window.location.pathname
            )}`;
            router.push(redirectUrl);
            }, 3000);
        }
        if (rawMaterialRequest.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        }


        const [dropdownsResponse, rawMaterialResponse] = await Promise.all([
          dropdownsRequest,
          rawMaterialRequest,
        ]);

        const allDropdownsList = await dropdownsResponse.json();
        const rawMaterialData = await rawMaterialResponse.json();

        // console.log("dropdowns", allDropdownsList);
        // console.log("raw material data", rawMaterialData);

        setDropdowns(allDropdownsList);
        setRawMaterialData(rawMaterialData);
      } catch (error) {
        console.error("Error fetching data", error);
        toast.error("There was an error with your request. Please check your data and try again.");
      }
    };

    if (productId) {
      fetchData();
    }
  }, [productId]);

  return (
    <Layout userData={userData}>
      <ToastContainer limit={1} />
      {!dropdowns && !rawMaterialData ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "100vh",
          }}
        >
          <ThreeCircles
            color="#002D73"
            height={50}
            width={50}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      ) : (
        <RawMaterialRequest
          dropdowns={dropdowns}
          userData={userData}
          rawMaterialData={rawMaterialData}
          pageType={"update"}
        />
      )}
    </Layout>
  );
};

export default index;

export const getServerSideProps = async (context) => {
  try {
    const { req, resolvedUrl } = context;
    const sessionId = req.cookies.thl_session;
    if (!sessionId) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(resolvedUrl)}`,
          permanent: false,
        },
      };
    }

    const apiBase =
      process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8081";

    const response = await fetch(`${apiBase}/api/auth/me`, {
      method: "GET",
      headers: {
        Cookie: `thl_session=${sessionId}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(resolvedUrl)}`,
          permanent: false,
        },
      };
    }

    const { user } = await response.json();

    return {
      props: {
        userData: user,
      },
    };
  } catch (error) {
    console.error("Error in getServerSideProps:", error);
    return {
      redirect: {
        destination: "/login",
        permanent: false,
      },
    };
  }
};
