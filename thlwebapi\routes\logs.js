"use strict";

const express = require("express");
const logData = require("../controllers/logs");
const { validateSession } = require("../middleware/sessionAuth");
const { requireRole } = require('../middleware/roleAuth');
const router = express.Router();

const {
    loginLog,
    getLogs
} = logData;

router.use(requireRole([1, 5, 6]));

// Login log endpoint (used during authentication, no session validation needed)
router.post("/logLogin", loginLog);

// Get logs endpoint (requires session validation)
router.get("/get-logs", validateSession, getLogs);

module.exports = router;
  