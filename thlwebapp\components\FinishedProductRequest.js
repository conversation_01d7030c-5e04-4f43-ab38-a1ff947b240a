import React, { useState, Fragment, useRef, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPlus,
  faInfoCircle,
  faInfo,
  faXmark,
} from "@fortawesome/free-solid-svg-icons";
import "ag-grid-community/styles//ag-grid.css";
import "ag-grid-community/styles//ag-theme-alpine.css";
import Layout from "@/components/Layout";
import { ThreeCircles } from "react-loader-spinner";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { apiConfig } from "@/services/apiConfig";
import finishedProductScenarios from "@/public/images/finishedProductScenarios.png";
import { Dialog, Transition } from "@headlessui/react";
import { useRouter } from "next/router";
import DrawerComponent from "./DrawerComponent";
import Select from "react-select";
import Cookies from "js-cookie";

const customSelectStyles = {
  // Default style
  control: (base) => ({
    ...base,
    height: "28px",
    minHeight: "28px",
  }),
  // Style when the condition is true
  option: (base, { data }) => ({
    ...base,
    color: data.is_new == true ? "red" : "",
  }),
};

const FinishedProductRequest = ({
  dropdowns,
  userData,
  finishedProductData,
  pageType,
}) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [nameOfOriginator, setNameOfOriginator] = useState("");
  const [isFinishedMaterialImage, setIsFinishedMaterialImage] = useState(false);
  const [isEndLabelImage, setIsEndLabelImage] = useState(false);
  const [reasonForRequest, setReasonForRequest] = useState("");
  const [reasonForRequestLabel, setReasonForRequestLabel] = useState("");
  const [expectedDeliveryDate, setExpectedDeliveryData] = useState("");
  const [productDescription, setProductDescription] = useState("");
  const [customerDescription, setCustomerDescription] = useState("");
  const [masterProductCode, setMasterProductCode] = useState("");
  const [masterProductCodeLabel, setMasterProductCodeLabel] = useState("");
  const [markVariety, setMarkVariety] = useState("");
  const [markVarietyLabel, setMarkVarietyLabel] = useState("");
  const [casesPerPallet, setCasesPerPallet] = useState("");
  const [unitsInOuter, setUnitsInOuter] = useState("");
  const [finishedPackSize, setFinishedPackSize] = useState("");
  const [grossWeightCase, setGrossWeightCase] = useState("");
  const [netWeightCase, setNetWeightCase] = useState("");
  const [subProductCode, setProductCode] = useState("");
  const [brand, setBrand] = useState("");
  const [brandLabel, setBrandLabel] = useState("");
  const [endCustomer, setEndCustomer] = useState("");
  const [endCustomerLabel, setEndCustomerLabel] = useState("");
  const [temperatureGrade, setTemperatureGrade] = useState("");
  const [temperatureGradeLabel, setTemperatureGradeLabel] = useState("");
  const [classRequired, setClassRequired] = useState("");
  const [classifiedAllergenic, setClassifiedAllergenic] = useState("");
  const [organicCertificationNumber, setOrganicCertificationNumber] =
    useState("");
  const [newOuterType, setNewOuterType] = useState("");
  const [newOuterTypeLabel, setNewOuterTypeLabel] = useState("");
  const [boxOuterType, setBoxOuterType] = useState("");
  const [packagingTypes, setPackagingTypes] = useState("");
  const [description, setDescription] = useState("");
  const [punnetTrayType, setPunnetTrayType] = useState("");
  const [machineFormat, setMachineFormat] = useState("");
  const [machineFormatLabel, setMachineFormatLabel] = useState("");
  const [packLabelType, setPackLabelType] = useState("");
  const [endLabelDescription, setEndLabelDescription] = useState("");
  const [promotionalLabelDescription, setPromotionLabelDescription] =
    useState("");
  const [labelScanGrade, setLabelScanGrade] = useState("");
  const [occBoxEnd, setOccBoxEnd] = useState("");
  const [eanPackLabel, setEanPackLabel] = useState("");
  const [tpndTradingUnitNum, setTpndTradingUnitNum] = useState("");
  const [tpnbBaesUnitNum, setTpnbBaesUnitNum] = useState("");
  const [displayUntil, setDisplayUntil] = useState("");
  const [bestBefore, setBestBefore] = useState("");
  const [otherDateCodeType, setOtherDateCodeType] = useState("");
  const [fCode, setFcode] = useState("");
  const [supplierSiteCode, setSupplierSiteCode] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [isCancelled, setIsCancelled] = useState(false);
  const [isContinue, setIsContinue] = useState(true);
  const [productId, setProductId] = useState("");
  const [isCancelOpen, setIsCancelOpen] = useState(false);
  const [cancelledReasonapi, setCancelledReasonapi] = useState("");
  const [isUserHaveCreated, setIsUserHaveCreated] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [disabledClass, setDisabledClass] = useState("");

  const [requestNumber, setrequestNumber] = useState("");
  const serverAddress = apiConfig.serverAddress;
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [title, setTitle] = useState("");
  const [dropdownData, setDropDownData] = useState("");
  const [placeholderText, setPlaceholderText] = useState("");
  const [legend, setLegend] = useState("");
  const [objKey, setObjKey] = useState("");
  const [allDropdown, setAlldropDown] = useState({});
  const [unblockedReasonapi, setUnblockedReasonapi] = useState("");
  const [isUnblockOpen, setIsUnblockOpen] = useState(false);
  const [isUnblockRequest, setIsUnblockRequest] = useState(false);
  const [cancelledBy, setCancelledBy] = useState("");
  const [cancelledDate, setCancelledDate] = useState("");
  const [status, setStatus] = useState("");
  const [submissionType, setSubmissionType] = useState("");
  const [submittedToISS, setSubmittedToISS] = useState();
  const [finishedMasterCode, setFinishedMasterCode] = useState("");
  const [finishedBrandCode, setFinishedBrandCode] = useState("");
  const [finishedEndCustomerCode, setFinishedEndCustomerCode] = useState("");
  const [finishedOuterTypeChange, setFinishedOuterTypeChange] = useState("");
  const [isProductGroups, setIsProductGroups] = useState(false);
  const [productGroups, setProductGroups] = useState([]);
  const [productGroup, setProductGroup] = useState([]);

  const brandRef = useRef(null);
  const endCustomerRef = useRef(null);
  const masterProdRef = useRef(null);
  const newOuterRef = useRef(null);
  const groupRef = useRef(null);
  const openSelect = (ref) => {
    if (ref.current) {
      ref.current.focus();
      ref.current.setState({ menuIsOpen: true });
    }
  };

  const classified_allergenic = [
    { value: "Yes", label: "Yes" },
    { value: "No", label: "No" },
  ];

  const handleNewDropdownData = (dataKey, newData) => {
    // console.log("data key", dataKey, "new data", newData);
    // // Update the dropdown data based on the dataKey
    switch (dataKey) {
      case "masterProductCode":
        setAlldropDown((prevDropdowns) => ({
          ...prevDropdowns,
          masterProductCode: newData,
        }));
        break;
      case "markVariety":
        setAlldropDown((prevDropdowns) => ({
          ...prevDropdowns,
          markVariety: newData,
        }));
        break;
      case "brand":
        setAlldropDown((prevDropdowns) => ({
          ...prevDropdowns,
          brand: newData,
        }));
        break;
      case "end_customer":
        setAlldropDown((prevDropdowns) => ({
          ...prevDropdowns,
          endCustomer: newData,
        }));
        break;
      case "newOuterBoxType":
        setAlldropDown((prevDropdowns) => ({
          ...prevDropdowns,
          newOuterBoxType: newData,
        }));
        break;
      // Add cases for other dataKey values as needed
      default:
        break;
    }
  };

  

  const handleIconClick = (dropDownType) => {
    const dropdownProperties = {
      master_product: {
        title: "Master Product Code",
        placeholder: "Master product code(5 digits)",
        legend: "Add new master product code",
        dropdownData: allDropdown?.masterProductCode,
        key: "masterProductCode",
      },
      mark_variety: {
        title: "Mark/Variety",
        placeholder: "Mark/Variety(5 Digits)",
        legend: "Add new mark/variety",
        dropdownData: allDropdown?.markVariety,
        key: "markVariety",
      },
      brand: {
        title: "Brand",
        placeholder: "Brand(5 Digits)",
        legend: "Add new brand",
        dropdownData: allDropdown?.brand,
        key: "brand",
      },
      end_customer: {
        title: "End Customer",
        placeholder: "End Customer(2 Digits)",
        legend: "Add new end customer",
        dropdownData: allDropdown?.endCustomer,
        key: "end_customer",
      },
      new_outer_box_type: {
        title: "New outer box type",
        placeholder: "New outer box type(5 Digits)",
        legend: "Add new-new outer box type",
        dropdownData: allDropdown?.newOuterBoxType,
        key: "newOuterBoxType",
      },
    };
    const { title, placeholder, legend, dropdownData, key } =
      dropdownProperties[dropDownType];

    setTitle(title);
    setPlaceholderText(placeholder);
    setDropDownData(dropdownData);
    setLegend(legend);
    setObjKey(key);

    setIsDrawerOpen(true);
  };

  const handleReasonChange = (data) => {
    //const selectedIndex = e.target.options.selectedIndex;
    setReasonForRequest([data]);
    //setReasonForRequestLabel(e.target.options[selectedIndex].text);
  };

  const handleMasterProductChange = (data) => {
    setMasterProductCode([data]);
    if (data) {
      setFinishedMasterCode(data.code);
    } else {
      setFinishedMasterCode("Click to add new master code");
    }
    
    if (data?.is_new == true) {
      getProductGroups(userData);
    } else {
      setIsProductGroups(false);
      setProductGroup([]);
    }
    //setMasterProductCodeLabel(e.target.options[selectedIndex].text);
  };

  const finishedProductCode = (netWeightCase, value) => {
    switch (value) {
      case 3:
        return `AS${netWeightCase}`;
      case 4:
        return `CP${netWeightCase}`;
      case 5:
        return `JD${netWeightCase}`;
      case 6:
        return `LI${netWeightCase}`;
      case 7:
        return `OC${netWeightCase}LSE`;
      case 8:
        return `OC${netWeightCase}`;
      case 9:
        return `SA${netWeightCase}LSE`;
      case 10:
        return `SA${netWeightCase}`;
      case 11:
        return `TS${netWeightCase}LSE`;
      case 12:
        return `TS${netWeightCase}`;
      case 13:
        return `TC${netWeightCase}`;
      default:
        return null;
    }
  };

  async function getProductGroups(userData) {
    let serverAddress = apiConfig.serverAddress;
    return fetch(`${serverAddress}products/get-product-groups/1`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${userData.token}`,
      },
    })
      .then(async (res) => {
        if (res.status === 200) {
          return res.json();
        }
        throw new Error("Failed to fetch data");
      })
      .then((json) => {
        setIsProductGroups(true);
        setProductGroups(json);
      })
      .catch((error) => {
        //setCommonError(error.message);
        return error.message;
      });
  }

 

  const handleMarkVarietyChange = (data) => {
    if (data) {
      const finishedSubProductCode = finishedProductCode(
        netWeightCase,
        data.value
      );
      setProductCode(finishedSubProductCode);
    } else {
      setProductCode("");
    }
    setMarkVariety(data?.value ? [data] : []);
    if (!data?.value) {
      setProductCode("");
    }
  };

  const handleBrandChange = (data) => {
    if (data) {
      setFinishedBrandCode(data.code);
    } else {
      setFinishedBrandCode("Click to add new brand");
    }
    setBrand([data]);
    if (data) {
      setFinishedBrandCode(data.code);
    } else {
      setFinishedBrandCode("");
    }
    //setBrandLabel(e.target.options[selectedIndex].text);
  };
  const handleEndCustomerChange = (data) => {
    if (data) {
      setFinishedEndCustomerCode(data.code);
    } else {
      setFinishedEndCustomerCode("Click to add new end customer");
    }
    setEndCustomer([data]);
    if (data) {
      setFinishedEndCustomerCode(data.code);
    } else {
      setFinishedEndCustomerCode("");
    }
    //setEndCustomerLabel(e.target.options[selectedIndex].text);
  };

  const handleTemperatureChange = (data) => {
    setTemperatureGrade([data]);
    //setTemperatureGradeLabel(e.target.options[selectedIndex].text);
  };
  const handleOuterTypeChange = (data) => {
    if (data) {
      setFinishedOuterTypeChange(data.code);
    } else {
      setFinishedOuterTypeChange("Click to add new box type");
    }
    setNewOuterType([data]);
    if (data) {
      setFinishedOuterTypeChange(data.code);
    } else {
      setFinishedOuterTypeChange("");
    }
    //setNewOuterTypeLabel(e.target.options[selectedIndex].text);
  };

  const handleProductGroups = (data) => {
    //console.log(data)
    if (data) {
      setProductGroup([data]);
    } else {
      setProductGroup([]);
    }
  };

  const handleMachineFormatChange = (data) => {
    setMachineFormat([data]);
    //setMachineFormatLabel(e.target.options[selectedIndex].text);
  };

  const handleClassifiedAllergenic = (data) => {
    setClassifiedAllergenic([data]);
  };

  const showFinishedMaterialImage = () => {
    setIsFinishedMaterialImage(true);
  };

  const hideFinishedMaterialImage = () => {
    setIsFinishedMaterialImage(false);
  };

  const showEndlabelImage = () => {
    setIsEndLabelImage(true);
  };

  const hideEndLabelImage = () => {
    setIsEndLabelImage(false);
  };

  const handleCancel = () => {
    setIsCancelOpen(true);
  };

  const closeCancelModal = () => {
    setIsCancelled(true);
    setIsCancelOpen(false);
  };

  const mark_variaty = [
    { value: 3, label: "Asda Pack" },
    { value: 4, label: "Co-op Pack" },
    { value: 5, label: "JDF Pack" },
    { value: 6, label: "Lidl Pack" },
    { value: 7, label: "Ocado Loose" },
    { value: 8, label: "Ocado Pack" },
    { value: 9, label: "Sains Loose" },
    { value: 10, label: "Sains Pack" },
    { value: 11, label: "Tesco Loose" },
    { value: 12, label: "Tesco Pack" },
    { value: 13, label: "Tesco Cell" },
  ];

  useEffect(() => {
   
    if (Cookies.get("finishWarning") == undefined) {
      toast.warn(
        "Entries marked with an * have not been created by ISS and are temporary until confirmed",
        {
          position: "top-center",
          autoClose: 50000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "light",
        }
      );
    }
    setTimeout(function () {
      Cookies.set("finishWarning", true, { expires: 365 });
      Cookies.remove("rawWarning");
    }, 2000);

    setAlldropDown(dropdowns);
    if ((pageType !== "update" || nameOfOriginator === "") && userData) {
      setNameOfOriginator(userData.name);
    }
    if (pageType == "update") {
     
      setIsEdit(true);
    }
    

    if (finishedProductData) {
     
      if (
        (userData.user_id != finishedProductData[0].requestor &&
          userData.role != 1) ||
        (finishedProductData[0].submitted_to_iss && userData.role != 1) ||
        (finishedProductData[0].status == 6) == 6
      ) {
        setIsUserHaveCreated(true);
        setDisabledClass(true);
      }
    }
  }, []);

  useEffect(() => {
    console.log(finishedProductData);
    // console.log(finishedProductData[0].delivery_date)
    // console.log(new Date(finishedProductData[0].delivery_date).toLocaleDateString("es-CL", {
    //   year: "numeric",
    //   month: "2-digit",
    //   day: "2-digit",
    // }))
    if (finishedProductData?.length > 0) {
      setSubmittedToISS(finishedProductData[0].submitted_to_iss);
      setProductId(finishedProductData[0].id);
      setReasonForRequest([
        {
          value: finishedProductData[0].reason_id,
          label: finishedProductData[0].reason,
        },
      ]);
      setReasonForRequestLabel(finishedProductData[0].reason);
      setExpectedDeliveryData(
        new Date(finishedProductData[0].delivery_date).toLocaleDateString(
          "en-CA",
          {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
          }
        )
      );
      setNameOfOriginator(finishedProductData[0].originator);
      setProductDescription(finishedProductData[0].product_description);
      setCustomerDescription(finishedProductData[0].customer_description);
      setMasterProductCode([
        {
          value: finishedProductData[0].master_product_id,
          label: finishedProductData[0].product_name,
          is_new: finishedProductData[0].mp_is_new,
        },
      ]);
      setProductGroup([
        {
          value: finishedProductData[0].group_id,
          label: finishedProductData[0].group_name,
          is_new: finishedProductData[0].group_is_new,
        },
      ]);
      setIsProductGroups(true);
      setMasterProductCodeLabel(finishedProductData[0].product_name);
      setFinishedMasterCode(finishedProductData[0].master_product_code);
      setMarkVariety([
        {
          value: finishedProductData[0].mark_variety_id,
          label: finishedProductData[0].mark_variety_name,
          is_new: finishedProductData[0].mark_variety_is_new,
        },
      ]);
      setMarkVarietyLabel(finishedProductData[0].mark_variaty_name);
      setCasesPerPallet(finishedProductData[0].cases_per_pallet);
      setUnitsInOuter(finishedProductData[0].units_in_outer);
      setFinishedPackSize(finishedProductData[0].finished_pack_size);
      setGrossWeightCase(finishedProductData[0].outer_gross_weight);
      setNetWeightCase(finishedProductData[0].outer_net_weight);
      setProductCode(finishedProductData[0].sub_product_code);
      setBrand([
        {
          value: finishedProductData[0].brand_id,
          label: finishedProductData[0].brand_name,
          is_new: finishedProductData[0].b_is_new,
        },
      ]);
      setBrandLabel(finishedProductData[0].brand_name);
      setFinishedBrandCode(finishedProductData[0].brand_code);
      setEndCustomer([
        {
          value: finishedProductData[0].end_customer_id,
          label: finishedProductData[0].end_customer_name,
          is_new: finishedProductData[0].end_customer_is_new,
        },
      ]);
      setEndCustomerLabel(finishedProductData[0].end_customer_name);
      setFinishedEndCustomerCode(finishedProductData[0].end_customer_code);
      setTemperatureGrade([
        {
          value: finishedProductData[0].temperature_grade_id,
          label: finishedProductData[0].temperature_grade_name,
        },
      ]);
      setTemperatureGradeLabel(finishedProductData[0].temperature_grade_name);
      setClassifiedAllergenic([
        {
          value: finishedProductData[0].is_classified_allergic_fsa14,
          label: finishedProductData[0].is_classified_allergic_fsa14,
        },
      ]);
      setNewOuterType([
        {
          value: finishedProductData[0].box_type_id,
          label: finishedProductData[0].box_type_name,
          is_new: finishedProductData[0].box_type_is_new,
        },
      ]);
      setNewOuterTypeLabel(finishedProductData[0].box_type_name);
      setBoxOuterType(finishedProductData[0].box_type_other_comments);
      setFinishedOuterTypeChange(finishedProductData[0].box_type_code);
      setPackagingTypes(finishedProductData[0].packaging_types);
      setDescription(finishedProductData[0].description_net_film);
      setPunnetTrayType(finishedProductData[0].punnet_tray_type);
      setMachineFormat([
        {
          value: finishedProductData[0].machine_format_id,
          label: finishedProductData[0].machine_format_name,
        },
      ]);
      setMachineFormatLabel(finishedProductData[0].machine_format_name);
      setPackLabelType(finishedProductData[0].pack_label_type);
      setEndLabelDescription(finishedProductData[0].end_label_description);
      setPromotionLabelDescription(
        finishedProductData[0].promotional_label_desc
      );
      setLabelScanGrade(finishedProductData[0].label_scan_grade);
      setOccBoxEnd(finishedProductData[0].occ_box_end);
      setEanPackLabel(finishedProductData[0].ean_pack_label);
      setTpndTradingUnitNum(finishedProductData[0].tpnd_trading_unit);
      setTpnbBaesUnitNum(finishedProductData[0].tpnb_base_unit);
      setDisplayUntil(finishedProductData[0].display_unit);
      setBestBefore(finishedProductData[0].best_before);
      setOtherDateCodeType(finishedProductData[0].is_other_date_code_type);
      setFcode(finishedProductData[0].f_code);
      setSupplierSiteCode(finishedProductData[0].supplier_site_code);
      setClassRequired(finishedProductData[0].class_required);
      setOrganicCertificationNumber(finishedProductData[0].organic_certificate);
      setDisplayUntil(finishedProductData[0].display_until);
      setrequestNumber(finishedProductData[0].request_no);
      setIsUnblockRequest(finishedProductData[0].is_unblock);
      setStatus(finishedProductData[0].status);
      setCancelledReasonapi(finishedProductData[0].cancelled_reason);
      setCancelledBy(finishedProductData[0].cancelled_by);
      setCancelledDate(finishedProductData[0].cancelled_date);
    }
  }, [finishedProductData]);

  console.log(productGroup);

  const formatDate = (dateString) => {
    const dateObject = new Date(dateString);

    const year = dateObject.getFullYear();
    const month = String(dateObject.getMonth() + 1).padStart(2, "0"); // Months are zero-based
    const day = String(dateObject.getDate()).padStart(2, "0");

    return `${day}/${month}/${year}`;
  };

  const formattedDate = formatDate(cancelledDate);

  async function getData(company, userData) {
    let serverAddress = apiConfig.serverAddress;

    let generateSequence = "";

    return fetch(`${serverAddress}products/get-request-number/${company}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${userData.token}`,
      },
    })
      .then(async (res) => {
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(() => {
            const redirectUrl = `/login?redirect=${encodeURIComponent(
              window.location.pathname
            )}`;
            logoutHandler(instance, redirectUrl);
            router.push("/login");
          }, 3000);
        }
        if (res.status === 200) {
          return res.json();
        }
        throw new Error("Failed to fetch data");
      })
      .then((json) => {
        if (json[0].request_number) {
          generateSequence =
            String(0).padStart(5, "0") + (parseInt(json[0].request_number) + 1);
          const existingRequestNumber = generateSequence;
          console.log(existingRequestNumber);
          return existingRequestNumber;
        } else {
          const formattedSequenceNumber = String(0).padStart(5, "0") + 1;
          const newRequestNumber = formattedSequenceNumber;
          console.log(newRequestNumber);
          return newRequestNumber;
        }
      })
      .catch((error) => {
        //setCommonError(error.message);
        return error.message;
      });
  }

  function insertRequestNumber(company, product_no) {
    const data = {
      company: company,
      request_no: product_no,
      product_request_type: 2,
    };
    try {
      fetch(`${serverAddress}products/insert-request-number`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userData.token}`,
        },
        body: JSON.stringify(data),
      })
        .then((res) => {
          if (res.status === 200) {
            return res.json();
          } else {
            toast.error("Failed to save raw material.");
            setLoading(false);
          }
        })
        .then((json) => {
          if (json && json.msg) {
            // Assuming the response contains a 'msg' field
            toast.success(json.msg, {
              position: "top-right",
            });
            setTimeout(() => {
              router.replace("/products");
              setLoading(false);
            }, 2000);
          }
        });
    } catch (error) {
      console.error("Failed to save raw material:", error);
      setLoading(false);
    }
  }

  const handleValidate = async (isContinue) => {
    const company_name = localStorage.getItem("domain");
    let errorCount = 1;
    const product_no = await getData(company_name, userData);
    console.log(product_no);
    console.log("user data", userData);
    const saveData = {
      nameOfOriginator: nameOfOriginator,
      request_no:
        pageType == "update"
          ? requestNumber
          : company_name.toUpperCase() + product_no,
      supplierSiteCode: supplierSiteCode,
      fCode: fCode,
      otherDateCodeType: otherDateCodeType,
      bestBefore: bestBefore,
      displayUntil: displayUntil,
      tpnbBaesUnitNum: tpnbBaesUnitNum,
      tpndTradingUnitNum: tpndTradingUnitNum,
      eanPackLabel: eanPackLabel,
      occBoxEnd: occBoxEnd,
      labelScanGrade: labelScanGrade,
      promotionalLabelDescription: promotionalLabelDescription,
      endLabelDescription: endLabelDescription,
      packLabelType: packLabelType,
      machineFormat: machineFormat,
      machineFormatLabel: machineFormatLabel,
      punnetTrayType: punnetTrayType,
      description: description,
      packagingTypes: packagingTypes,
      boxOuterType: boxOuterType,
      newOuterType: newOuterType,
      newOuterTypeLabel: newOuterTypeLabel,
      organicCertificationNumber: organicCertificationNumber,
      classifiedAllergenic: classifiedAllergenic,
      classRequired: classRequired,
      temperatureGrade: temperatureGrade,
      temperatureGradeLabel: temperatureGradeLabel,
      endCustomer: endCustomer,
      endCustomerLabel: endCustomerLabel,
      brand: brand,
      brandLabel: brandLabel,
      subProductCode: subProductCode,
      netWeightCase: netWeightCase,
      grossWeightCase: grossWeightCase,
      finishedPackSize: finishedPackSize,
      unitsInOuter: unitsInOuter,
      casesPerPallet: casesPerPallet,
      markVariety: markVariety,
      markVarietyLabel: markVarietyLabel,
      masterProductCode: masterProductCode,
      masterProductCodeLabel: masterProductCodeLabel,
      productGroup: productGroup,
      customerDescription: customerDescription,
      productDescription: productDescription,
      expectedDeliveryDate: expectedDeliveryDate,
      reasonForRequest: reasonForRequest,
      reasonForRequestLabel: reasonForRequestLabel,
      companyId: company_name,
      requestor: userData.user_id,
      username: userData.name,
      useremail: userData.email_id,
      status: 3,
      isSubmitted:
        submissionType === "submit"
          ? true
          : submissionType === "save"
          ? false
          : null,
    };
    if (errorCount > 0) {
      setIsOpen(true);
    } else {
      console.log("saving");
      if (pageType == "update") {
        handleEdit(saveData);
      }
      handleAdd(saveData, product_no);
    }

    if (isCancelled) {
      setIsCancelled(false);
      return;
    }

    if (isContinue) {
      if (pageType == "update") {
        handleEdit(saveData);
      } else {
        handleAdd(saveData, product_no);
      }
      setIsOpen(false);
    }

    if (isCancelled) {
      setIsCancelled(false);
      return;
    }
    console.log("save data", saveData);
  };

  const handleAdd = (data, product_no) => {
    try {
      setLoading(true);
      fetch(`${serverAddress}products/add-finished-products`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userData.token}`,
        },
        body: JSON.stringify(data),
      })
        .then((res) => {
          if (res.status === 200) {
            return res.json();
          } else {
            toast.error("Failed to save finished product.");
            setLoading(false);
          }
        })
        .then((json) => {
          if (json && json.msg) {
            const company_name = localStorage.getItem("domain");
            const insert_request_number = insertRequestNumber(
              company_name,
              product_no
            );
            // Assuming the response contains a 'msg' field
            console.log(json);
            toast.success(json.msg, {
              position: "top-right",
            });
            setTimeout(() => {
              router.replace("/products");
              setLoading(false);
            }, 2000);
          }
        });
    } catch (error) {
      console.error("Failed to save finished product:", error);
      setLoading(false);
    }
  };

  const saveModalData = () => {
    try {
      fetch(`${serverAddress}products/product-update-status`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userData.token}`,
        },
        body: JSON.stringify({
          status: 6,
          productId: productId,
          updated_date: new Date().toISOString(),
          reason: cancelledReasonapi,
          cancelled_by: userData.name,
          cancelled_date: new Date().toISOString(),
        }),
      })
        .then((res) => {
          if (res.status === 200) {
            return res.json();
          }
        })
        .then((json) => {
          if (json.length > 0) {
            router.push("/products");
          }
        });
    } catch (error) {
      console.error("Failed to cancel product by :", error);
    }
  };

  const handleEdit = (data) => {
    try {
      setLoading(true);
      fetch(`${serverAddress}products/update-finished-products/${productId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userData.token}`,
        },
        body: JSON.stringify(data),
      })
        .then((res) => {
          if (res.status === 200) {
            return res.json();
          } else {
            toast.error("Failed to update finished product.");
            setLoading(false);
          }
        })
        .then((json) => {
          if (json && json.msg) {
            // Assuming the response contains a 'msg' field
            console.log(json);
            toast.success(json.msg, {
              position: "top-right",
            });
            setTimeout(() => {
              router.replace("/products");
              setLoading(false);
            }, 2000);
          }
        });
    } catch (error) {
      console.error("Failed to update finished product:", error);
      setLoading(false);
    }
  };

  const closeModal = () => {
    setIsCancelled(true);
    setIsOpen(false);
  };

  const handleContinueSubmit = () => {
    handleValidate(isContinue);
    setIsOpen(false);
  };

  const closeUnblockModal = () => {
    setIsCancelled(true);
    setIsUnblockOpen(false);
  };

  const handleUnblockEvent = () => {
    setIsUnblockOpen(true);
  };

  console.log(status);

  const saveUnblockModalData = () => {
    try {
      fetch(`${serverAddress}products/product-update-unblock`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userData.token}`,
        },
        body: JSON.stringify({
          productId: productId,
          unblock_reason: unblockedReasonapi,
          unblock_by: userData.name,
          unblock_date: new Date().toISOString(),
        }),
      })
        .then((res) => {
          if (res.status === 200) {
            return res.json();
          }
        })
        .then((json) => {
          if (json.length > 0) {
            router.push("/products");
          }
        });
    } catch (error) {
      console.error("Failed to cancel product by :", error);
    }
  };

  const handleWithoutReason = () => {
    router.push("/products");
  };

  return (
    <>
      <ToastContainer limit={1} />
      {loading ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "calc(100vh - 100px)",
            width: "calc(100vw - 125px)",
          }}
        >
          <ThreeCircles
            color="#002D73"
            height={50}
            width={50}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      ) : (
        <Layout>
          <div className="relative panel-container bg-white rounded-lg w-[93%] lg:w-[95%] 2xl:w-[calc(100%-70px)] p-4 pb-0">
            <div className="flex md:flex-row flex-col my-2">
              <div className="px-5 pe-8 mb-0 h-100vh border-e-[1px] border-light-gray md:w-1/2">
                {status == 6 && (
                  <h6 className="text-red-500 fw-bold">
                    Cancellation Reason: {cancelledReasonapi}, Cancelled By:{" "}
                    {cancelledBy}, Cancelled Date : {formattedDate}
                  </h6>
                )}
                <div className="mb-6">
                  <div className="mb-3">
                    <h4 className="formtitle pb-1 border-b border-light-gray3">
                      Request Information
                    </h4>
                  </div>

                  <div className="grid lg:grid-cols-2 gap-6 grid-cols-1">
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">Request number</label>
                      <input
                        type="text"
                        name="Request_number"
                        placeholder="Will be generated on submit"
                        disabled={true}
                        className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md bg-gray-100`}
                        required
                        value={requestNumber}
                        tabIndex={1}
                      />
                    </div>

                    <div className="flex flex-col mr-[25px]">
                      <label className="labels mb-1">Reason for request</label>{" "}
                      {/* <select
                        tabIndex={8}
                        type="text"
                        name="reason_for_request"
                        value={reasonForRequest}
                        onChange={handleReasonChange}
                        className="w-full px-2 2xl:px-3 border border-light-gray rounded-md"
                        required
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                        // onBlur={handleCountryChange}
                      >
                        <option value=""></option>
                        {allDropdown?.reason &&
                          allDropdown?.reason?.map((con, key) => {
                            return (
                              <option key={key} value={con.id}>
                                {con.reason}
                              </option>
                            );
                          })}
                      </select> */}
                      <Select
                        options={allDropdown?.reason}
                        placeholder="Select reason for request"
                        value={reasonForRequest}
                        onChange={handleReasonChange}
                        isSearchable={true}
                        instanceId="selectbox"
                        className="w-full"
                        styles={customSelectStyles}
                        isClearable={true}
                        isDisabled={
                          submittedToISS || isUserHaveCreated ? true : false
                        }
                        //onBlur={handleValidationChange}
                      />
                    </div>

                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">
                        Expected 1st delivery date
                      </label>
                      <input
                        type="date"
                        name="date"
                        className="w-full px-2 2xl:px-3 border border-light-gray rounded-md inputs"
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                        value={expectedDeliveryDate}
                        onChange={(e) =>
                          setExpectedDeliveryData(e.target.value)
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[25px]">
                      <label className="labels mb-1">Name of originator</label>
                      <input
                        type="text"
                        name="name_of_originator"
                        className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md bg-gray-100`}
                        value={nameOfOriginator}
                        required
                        tabIndex={3}
                        onChange={(e) => setNameOfOriginator(e.target.value)}
                        disabled={true}
                        readOnly
                      />
                    </div>
                  </div>
                </div>

                <div className="mb-6">
                  <div className="mb-3">
                    <h4 className="formtitle pb-2 border-b border-light-gray3">
                      Product Information
                    </h4>
                  </div>

                  <div className=" grid lg:grid-cols-2 grid-cols-1 gap-6">
                    <div className="flex flex-col mr-[25px]">
                      <label className="labels mb-1">
                        Product description/line
                      </label>
                      <input
                        type="text"
                        name="product_description_line"
                        value={productDescription}
                        onChange={(e) => setProductDescription(e.target.value)}
                        className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border  rounded-md`}
                        required
                        tabIndex={4}
                        style={{ textTransform: "capitalize" }}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[25px]">
                      <label className="labels mb-1">
                        Customer description for the product
                      </label>
                      <input
                        type="text"
                        name="customer_description_for_the_product"
                        value={customerDescription}
                        onChange={(e) => setCustomerDescription(e.target.value)}
                        className="w-full py-1 px-2 2xl:px-3 2xl:py-3 border border-light-gray rounded-md"
                        required
                        tabIndex={5}
                        style={{ textTransform: "capitalize" }}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col">
                      <label className="labels mb-1" required>
                        Master product code
                      </label>
                      <div className="flex flex-row items-center">
                        {/* <select
                          tabIndex={8}
                          type="text"
                          name="master_product_code"
                          value={masterProductCode}
                          onChange={handleMasterProductChange}
                          className="w-full px-2 2xl:px-3 border border-light-gray rounded-md"
                          required
                          disabled={
                            submittedToISS
                              ? true
                              : false || isUserHaveCreated
                              ? true
                              : false
                          }
                          // onBlur={handleCountryChange}
                        >
                          <option value=""></option>
                          {allDropdown?.masterProductCode &&
                            allDropdown?.masterProductCode?.map((con, key) => {
                              return (
                                <option key={key} value={con.id}>
                                  {con.name}
                                </option>
                              );
                            })}
                        </select> */}
                        <Select
                          options={allDropdown?.masterProductCode}
                          placeholder="Select master code"
                          value={masterProductCode}
                          onChange={handleMasterProductChange}
                          isSearchable={true}
                          ref={masterProdRef}
                          openMenuOnFocus={true}
                          instanceId="selectbox"
                          styles={customSelectStyles}
                          className="w-full"
                          classNames={{
                            control: (state) =>
                              state.is_new == true
                                ? "text-red-500"
                                : "!text-red-500",
                          }}
                          isClearable={true}
                          isDisabled={
                            submittedToISS || isUserHaveCreated ? true : false
                          }
                          //onBlur={handleValidationChange}
                        />
                        <FontAwesomeIcon
                          onClick={() => handleIconClick("master_product")}
                          icon={faPlus}
                          size="sm"
                          className={`ml-2 p-1 border rounded-sm border-[#0066ff] cursor-pointer ${
                            disabledClass
                              ? "cursor-not-allowed pointer-events-none opacity-0.5"
                              : ""
                          }`}
                          style={{ color: "#0066ff" }}
                        />{" "}
                      </div>
                    </div>
                    <div className="flex flex-col mr-[25px]">
                      {/* <input
                        type="text"
                        name="master_code"
                        value={finishedMasterCode}
                        //onChange={(e) => setFinishedMasterCode(e.target.value)}
                        className="w-full py-1 px-2 2xl:px-3 2xl:py-3 border bg-gray-100 rounded-md"
                        tabIndex={7}
                        disabled={true}
                        readOnly
                      /> */}
                      <label
                        className="flex items-center h-[28px] w-full py-1 px-2 2xl:px-3 2xl:py-2 mt-5 cursor-pointer"
                        onClick={() => openSelect(masterProdRef)}
                      >
                        {finishedMasterCode
                          ? finishedMasterCode
                          : "Click here to select Master Product Code"}
                      </label>
                    </div>
                    {isProductGroups && (
                      <div className="flex flex-col mr-[25px]">
                        <label className="labels mb-1" required>
                          Groups {/*<span className="text-red-500">*</span> */}
                        </label>
                        <div className="flex flex-row items-center">
                          <Select
                            options={productGroups}
                            placeholder="Select group"
                            value={productGroup}
                            onChange={handleProductGroups}
                            isSearchable={true}
                            ref={groupRef}
                            instanceId="selectbox2"
                            className="w-full"
                            classNames={{
                              control: (state) =>
                                state.is_new == true
                                  ? "text-red-500"
                                  : "!text-red-500",
                            }}
                            styles={customSelectStyles}
                            isClearable={true}
                            isDisabled={
                              submittedToISS || isUserHaveCreated ? true : false
                            }
                          />
                        </div>
                      </div>
                    )}
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">
                        Cases (outers) per pallet
                      </label>
                      <input
                        type="number"
                        name="cases_per_pallet"
                        value={casesPerPallet}
                        onChange={(e) => setCasesPerPallet(e.target.value)}
                        className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                        tabIndex={7}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">Units in outer</label>
                      <div className="flex flex-row items-center">
                        <input
                          type="number"
                          name="units_in_outer"
                          className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                          required
                          tabIndex={9}
                          value={unitsInOuter}
                          onChange={(e) => setUnitsInOuter(e.target.value)}
                          disabled={
                            submittedToISS
                              ? true
                              : false || isUserHaveCreated
                              ? true
                              : false
                          }
                        />
                      </div>
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">Finished pack size</label>
                      <input
                        type="number"
                        name="finished_pack_size"
                        className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                        required
                        tabIndex={9}
                        value={finishedPackSize}
                        onChange={(e) => setFinishedPackSize(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">
                        Gross weight of case (outer) in KG
                      </label>
                      <input
                        type="number"
                        name="gross_weight_of_cases_in_kg"
                        className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                        required
                        tabIndex={9}
                        value={grossWeightCase}
                        onChange={(e) => setGrossWeightCase(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">
                        Net weight of case (outer) in KG
                      </label>
                      <input
                        type="number"
                        name="net_weight_of_caes_in_kg"
                        className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                        required
                        tabIndex={9}
                        value={netWeightCase}
                        onChange={(e) => setNetWeightCase(e.target.value)}
                        onBlur={handleMarkVarietyChange}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1" required>
                        Mark
                      </label>
                      <div className="flex flex-row items-center">
                        {/* <select
                          tabIndex={8}
                          type="text"
                          name="mark_variety"
                          value={markVariety}
                          onChange={handleMarkVarietyChange}
                          className="w-full px-2 2xl:px-3 border border-light-gray rounded-md"
                          required
                          disabled={
                            submittedToISS
                              ? true
                              : false || isUserHaveCreated
                              ? true
                              : false
                          }
                          // onBlur={handleCountryChange}
                        >
                          <option value=""></option>
                          {allDropdown?.markVariety &&
                            allDropdown?.markVariety?.map((con, key) => {
                              return (
                                <option key={key} value={con.id}>
                                  {con.name}
                                </option>
                              );
                            })}
                        </select> */}
                        <Select
                          options={mark_variaty}
                          placeholder="Select mark"
                          value={markVariety}
                          onChange={handleMarkVarietyChange}
                          isSearchable={true}
                          instanceId="selectbox"
                          className="w-full"
                          styles={customSelectStyles}
                          classNames={{
                            control: (state) =>
                              state.is_new == true
                                ? "text-red-500"
                                : "!text-red-500",
                          }}
                          isClearable={true}
                          isDisabled={
                            submittedToISS || isUserHaveCreated ? true : false
                          }
                          //onBlur={handleValidationChange}
                        />
                        {/* <FontAwesomeIcon
                          onClick={() => handleIconClick("mark_variety")}
                          icon={faPlus}
                          size="sm"
                          className={`ml-2 p-1 border rounded-sm border-[#0066ff] ${
                            disabledClass
                              ? "cursor-not-allowed pointer-events-none"
                              : ""
                          }`}
                          style={{ color: "#0066ff" }}
                        />{" "} */}
                      </div>
                    </div>
                    <div className="flex flex-col col-span-2 w-[44%]">
                      <label className="flex items-center labels mb-1">
                        Sub product code (Suggested)
                        <FontAwesomeIcon
                          icon={faInfoCircle}
                          size="lg"
                          title="Sub product code info"
                          className="ml-2 p-1"
                          style={{ color: "#111111" }}
                          onMouseEnter={showFinishedMaterialImage}
                          onMouseLeave={hideFinishedMaterialImage}
                        />
                      </label>
                      <input
                        type="text"
                        name="sub_product_code"
                        className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md`}
                        required
                        tabIndex={9}
                        value={subProductCode}
                        onChange={(e) => setProductCode(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                      <img
                        className={`z-10 absolute w-[60%] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 ${
                          isFinishedMaterialImage ? "block" : "hidden"
                        }`}
                        src={finishedProductScenarios.src}
                      />
                    </div>
                    <div className="flex flex-col">
                      <label className="labels mb-1" required>
                        Brand
                      </label>
                      <div className="flex flex-row items-center">
                        {/* <select
                          tabIndex={8}
                          type="text"
                          name="brand"
                          value={brand}
                          onChange={handleBrandChange}
                          className="w-full px-2 2xl:px-3 border border-light-gray rounded-md"
                          required
                          disabled={
                            submittedToISS
                              ? true
                              : false || isUserHaveCreated
                              ? true
                              : false
                          }
                          // onBlur={handleCountryChange}
                        >
                          <option value=""></option>
                          {allDropdown?.brand &&
                            allDropdown?.brand?.map((con, key) => {
                              return (
                                <option key={key} value={con.id}>
                                  {con.code}
                                </option>
                              );
                            })}
                        </select> */}
                        <Select
                          options={allDropdown?.brand}
                          placeholder="Select brand"
                          value={brand}
                          onChange={handleBrandChange}
                          isSearchable={true}
                          ref={brandRef}
                          openMenuOnFocus={true}
                          instanceId="selectbox"
                          className="w-full"
                          styles={customSelectStyles}
                          classNames={{
                            control: (state) =>
                              state.is_new == true
                                ? "text-red-500"
                                : "!text-red-500",
                          }}
                          isClearable={true}
                          isDisabled={
                            submittedToISS || isUserHaveCreated ? true : false
                          }
                          //onBlur={handleValidationChange}
                        />
                        <FontAwesomeIcon
                          onClick={() => handleIconClick("brand")}
                          icon={faPlus}
                          size="sm"
                          className={`ml-2 p-1 border rounded-sm border-[#0066ff] cursor-pointer ${
                            disabledClass
                              ? "cursor-not-allowed pointer-events-none opacity-0.5"
                              : ""
                          }`}
                          style={{ color: "#0066ff" }}
                        />
                      </div>
                    </div>
                    {/* <div className="flex flex-col mr-[25px]">
                      <label className="labels mb-1">Code</label>
                      <span className="flex items-center h-[28px]"><b>{finishedBrand}</b></span>
                    </div> */}
                    <div className="flex flex-col">
                      <label
                        className="flex items-center h-[28px] w-full py-1 px-2 2xl:px-3 2xl:py-2 mt-5 cursor-pointer"
                        onClick={() => openSelect(brandRef)}
                      >
                        {finishedBrandCode
                          ? finishedBrandCode
                          : "Click here to select Brand"}
                      </label>
                    </div>
                    <div className="flex flex-col">
                      <label className="labels mb-1" required>
                        End customer (retail customer)
                      </label>
                      <div className="flex flex-row items-center">
                        {/* <select
                          tabIndex={8}
                          type="text"
                          name="end_customer"
                          value={endCustomer}
                          onChange={handleEndCustomerChange}
                          className="w-full px-2 2xl:px-3 border border-light-gray rounded-md"
                          required
                          disabled={
                            submittedToISS
                              ? true
                              : false || isUserHaveCreated
                              ? true
                              : false
                          }
                          // onBlur={handleCountryChange}
                        >
                          <option value=""></option>
                          {allDropdown?.endCustomer &&
                            allDropdown?.endCustomer?.map((con, key) => {
                              return (
                                <option key={key} value={con.id}>
                                  {con.name}
                                </option>
                              );
                            })}
                        </select> */}
                        <Select
                          options={allDropdown?.endCustomer}
                          placeholder="Select end customer"
                          value={endCustomer}
                          onChange={handleEndCustomerChange}
                          isSearchable={true}
                          ref={endCustomerRef}
                          openMenuOnFocus={true}
                          instanceId="selectbox"
                          className="w-full"
                          styles={customSelectStyles}
                          classNames={{
                            control: (state) =>
                              state.is_new == true
                                ? "text-red-500"
                                : "!text-red-500",
                          }}
                          isClearable={true}
                          isDisabled={
                            submittedToISS || isUserHaveCreated ? true : false
                          }
                          //onBlur={handleValidationChange}
                        />
                        <FontAwesomeIcon
                          onClick={() => handleIconClick("end_customer")}
                          icon={faPlus}
                          size="sm"
                          className={`ml-2 p-1 border rounded-sm border-[#0066ff] cursor-pointer ${
                            disabledClass
                              ? "cursor-not-allowed pointer-events-none opacity-0.5"
                              : ""
                          }`}
                          style={{ color: "#0066ff" }}
                        />
                      </div>
                    </div>{" "}
                    <div className="flex flex-col">
                      <label
                        className="flex items-center h-[28px] w-full py-1 px-2 2xl:px-3 2xl:py-2 mt-5 cursor-pointer"
                        onClick={() => openSelect(endCustomerRef)}
                      >
                        {finishedEndCustomerCode
                          ? finishedEndCustomerCode
                          : "Click here to select End Customer"}
                      </label>
                    </div>
                    {/* <div className="flex flex-col mr-[25px]">
                      <label className="labels mb-1">Code</label>
                      <span className="flex items-center h-[28px]"><b>{finishedEndCustomer}</b></span>
                    </div> */}
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1" required>
                        Temperature grade
                      </label>
                      {/* <select
                        tabIndex={8}
                        type="text"
                        name="temperature_grade"
                        value={temperatureGrade}
                        onChange={handleTemperatureChange}
                        className="w-full px-2 2xl:px-3 border border-light-gray rounded-md"
                        required
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                        // onBlur={handleCountryChange}
                      >
                        <option value=""></option>
                        {allDropdown?.temperatureGrade &&
                          allDropdown?.temperatureGrade?.map((con, key) => {
                            return (
                              <option key={key} value={con.id}>
                                {con.name}
                              </option>
                            );
                          })}
                      </select> */}
                      <Select
                        options={allDropdown?.temperatureGrade}
                        placeholder="Select temperature grade"
                        value={temperatureGrade}
                        onChange={handleTemperatureChange}
                        isSearchable={true}
                        instanceId="selectbox"
                        styles={customSelectStyles}
                        isClearable={true}
                        isDisabled={
                          submittedToISS || isUserHaveCreated ? true : false
                        }
                        //onBlur={handleValidationChange}
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">Class required</label>
                      <input
                        type="text"
                        name="class_required"
                        className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md`}
                        required
                        tabIndex={9}
                        value={classRequired}
                        style={{ textTransform: "capitalize" }}
                        onChange={(e) => setClassRequired(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1" required>
                        Classified Allergenic Under The FSA14 Allergen Types?
                      </label>
                      {/* <select
                        tabIndex={8}
                        type="text"
                        name="classified_allergenic_under_the_fsa14_allergen_types"
                        value={classifiedAllergenic}
                        onChange={(e) =>
                          setClassifiedAllergenic(e.target.value)
                        }
                        className="w-full px-2 2xl:px-3 border border-light-gray rounded-md"
                        required
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                        // onBlur={handleCountryChange}
                      >
                        <option value=""></option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                      </select> */}
                      <Select
                        options={classified_allergenic}
                        placeholder="Is Allergenic FSA14"
                        value={classifiedAllergenic}
                        onChange={handleClassifiedAllergenic}
                        isSearchable={true}
                        instanceId="selectbox"
                        className="w-full"
                        styles={customSelectStyles}
                        isClearable={true}
                        isDisabled={
                          submittedToISS || isUserHaveCreated ? true : false
                        }
                        //onBlur={handleValidationChange}
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1" required>
                        Organic certification
                      </label>

                      <input
                        type="text"
                        name="pack_label_type"
                        className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md`}
                        required
                        style={{ textTransform: "capitalize" }}
                        tabIndex={1}
                        value={organicCertificationNumber}
                        onChange={(e) =>
                          setOrganicCertificationNumber(e.target.value)
                        }
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="p-x5 ps-8 mb-0 w-1/2">
                <div className="mb-6">
                  <div className="mb-3">
                    <h4 className="formtitle pb-1 border-b border-light-gray3">
                      Packaging and format information
                    </h4>
                  </div>
                  <div className="grid lg:grid-cols-2 gap-4 grid-cols-1">
                    <div className="flex flex-col">
                      <label className="labels mb-1" required>
                        New outer type Required (Box type/Colors)
                      </label>{" "}
                      <div className="flex flex-row items-center">
                        {/* <select
                          tabIndex={8}
                          type="text"
                          name="new_outer_type_required"
                          value={newOuterType}
                          onChange={handleOuterTypeChange}
                          className="w-full px-2 2xl:px-3 border border-light-gray rounded-md"
                          required
                          disabled={
                            submittedToISS
                              ? true
                              : false || isUserHaveCreated
                              ? true
                              : false
                          }
                          // onBlur={handleCountryChange}
                        >
                          <option value=""></option>
                          {allDropdown?.newOuterBoxType &&
                            allDropdown?.newOuterBoxType?.map((con, key) => {
                              return (
                                <option key={key} value={con.id}>
                                  {con.name}
                                </option>
                              );
                            })}
                        </select>{" "} */}
                        <Select
                          options={allDropdown?.newOuterBoxType}
                          placeholder="Select box type"
                          value={newOuterType}
                          onChange={handleOuterTypeChange}
                          isSearchable={true}
                          ref={newOuterRef}
                          openMenuOnFocus={true}
                          instanceId="selectbox"
                          className="w-full"
                          styles={customSelectStyles}
                          classNames={{
                            control: (state) =>
                              state.is_new == true
                                ? "text-red-500"
                                : "!text-red-500",
                          }}
                          isClearable={true}
                          isDisabled={
                            submittedToISS || isUserHaveCreated ? true : false
                          }
                          //onBlur={handleValidationChange}
                        />
                        <FontAwesomeIcon
                          onClick={() => handleIconClick("new_outer_box_type")}
                          icon={faPlus}
                          size="sm"
                          className={`ml-2 p-1 border rounded-sm border-[#0066ff] cursor-pointer ${
                            disabledClass
                              ? "cursor-not-allowed pointer-events-none opacity-0.5"
                              : ""
                          }`}
                          style={{ color: "#0066ff" }}
                        />
                      </div>
                    </div>{" "}
                    <div className="flex flex-col">
                      <label
                        className="flex items-center h-[28px] w-full py-1 px-2 2xl:px-3 2xl:py-2 mt-5 cursor-pointer"
                        onClick={() => openSelect(newOuterRef)}
                      >
                        {finishedOuterTypeChange
                          ? finishedOuterTypeChange
                          : "Click here to selectect Finished Outer Type"}
                      </label>
                    </div>
                    {/* <div className="flex flex-col mr-[25px]">
                      <label className="labels mb-1">Code</label>
                      <span className="flex items-center h-[28px]"><b>{finishedBoxType}</b></span>
                    </div> */}
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">
                        Box / outer type other comments:
                      </label>
                      <input
                        type="text"
                        name="box_outer_type_other_comments"
                        className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md`}
                        required
                        style={{ textTransform: "capitalize" }}
                        tabIndex={1}
                        value={boxOuterType}
                        onChange={(e) => setBoxOuterType(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1" required>
                        Packaging types
                      </label>

                      <input
                        type="text"
                        name="punnet_or_tray_type"
                        className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md`}
                        required
                        style={{ textTransform: "capitalize" }}
                        tabIndex={1}
                        value={packagingTypes}
                        onChange={(e) => setPackagingTypes(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1" required>
                        Description of net / blog / ribbon / film
                      </label>

                      <input
                        type="text"
                        name="descriptionNetBlogRibbonFilm"
                        className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md`}
                        required
                        style={{ textTransform: "capitalize" }}
                        tabIndex={1}
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1" required>
                        Punnet or tray type
                      </label>
                      <input
                        type="text"
                        name="punnet_or_tray_type"
                        className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md`}
                        required
                        style={{ textTransform: "capitalize" }}
                        tabIndex={1}
                        value={punnetTrayType}
                        onChange={(e) => setPunnetTrayType(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1" required>
                        Machine format
                      </label>
                      {/* <select
                        tabIndex={8}
                        type="text"
                        name="machine_format"
                        value={machineFormat}
                        onChange={handleMachineFormatChange}
                        className="w-full px-2 2xl:px-3 border border-light-gray rounded-md"
                        required
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                        // onBlur={handleCountryChange}
                      >
                        <option value=""></option>
                        {allDropdown?.machineFormat &&
                          allDropdown?.machineFormat?.map((con, key) => {
                            return (
                              <option key={key} value={con.id}>
                                {con.name}
                              </option>
                            );
                          })}
                      </select> */}
                      <Select
                        options={allDropdown?.machineFormat}
                        placeholder="Select machine format"
                        value={machineFormat}
                        onChange={handleMachineFormatChange}
                        isSearchable={true}
                        instanceId="selectbox"
                        styles={customSelectStyles}
                        isClearable={true}
                        isDisabled={
                          submittedToISS || isUserHaveCreated ? true : false
                        }
                        //onBlur={handleValidationChange}
                      />
                    </div>
                  </div>
                </div>
                <div className="mb-6">
                  <div className="mb-3">
                    <h4 className="formtitle pb-2 border-b border-light-gray3">
                      Labelling information
                    </h4>
                  </div>
                  <div className="grid lg:grid-cols-2 gap-4 grid-cols-1">
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">Pack label type</label>
                      <input
                        type="text"
                        name="pack_label_type"
                        className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md`}
                        required
                        style={{ textTransform: "capitalize" }}
                        tabIndex={1}
                        value={packLabelType}
                        onChange={(e) => setPackLabelType(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1 flex items-center">
                        End label description
                        <FontAwesomeIcon
                          icon={faInfoCircle}
                          size="lg"
                          title="Sub product code info"
                          className="ml-2 p-1"
                          style={{ color: "#111111" }}
                          onMouseEnter={showEndlabelImage}
                          onMouseLeave={hideEndLabelImage}
                        />
                      </label>
                      <input
                        type="text"
                        name="end_label_description"
                        className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md`}
                        required
                        style={{ textTransform: "capitalize" }}
                        tabIndex={1}
                        value={endLabelDescription}
                        onChange={(e) => setEndLabelDescription(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                      {/* // !this image is wrong need to change it */}
                      <img
                        className={`z-10 absolute w-[60%] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 ${
                          isEndLabelImage ? "block" : "hidden"
                        }`}
                        src={finishedProductScenarios.src}
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">
                        Promotional label description
                      </label>
                      <input
                        type="text"
                        name="promotional_label_description"
                        className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md`}
                        required
                        tabIndex={1}
                        value={promotionalLabelDescription}
                        style={{ textTransform: "capitalize" }}
                        onChange={(e) =>
                          setPromotionLabelDescription(e.target.value)
                        }
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">Label scan grade</label>
                      <input
                        type="text"
                        name="label_scan_grade"
                        className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md`}
                        required
                        tabIndex={1}
                        value={labelScanGrade}
                        style={{ textTransform: "capitalize" }}
                        onChange={(e) => setLabelScanGrade(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">
                        OCC BOX END - Digits 13 OFF (14 DPS)
                      </label>
                      <input
                        type="number"
                        name="occ_box_end"
                        className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                        required
                        style={{ textTransform: "capitalize" }}
                        tabIndex={1}
                        value={occBoxEnd}
                        onChange={(e) => setOccBoxEnd(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">
                        EAN - PACK LABEL - 8 digits
                      </label>
                      <input
                        type="number"
                        name="ean_pack_label"
                        className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                        required
                        tabIndex={1}
                        value={eanPackLabel}
                        onChange={(e) => setEanPackLabel(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">
                        TPND - TRADING UNIT NUM - OFF 7-8 Digits & DPS
                      </label>
                      <input
                        type="number"
                        name="tpnd_trading_unit_num"
                        className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                        required
                        tabIndex={1}
                        value={tpndTradingUnitNum}
                        onChange={(e) => setTpndTradingUnitNum(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">
                        TPNB - BAES UNIT NUM - 8 Digits
                      </label>
                      <input
                        type="number"
                        name="tpnb_baes_unit_num"
                        className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                        required
                        tabIndex={1}
                        value={tpnbBaesUnitNum}
                        onChange={(e) => setTpnbBaesUnitNum(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">
                        Display until (depot delivery +)
                      </label>
                      <input
                        type="number"
                        name="display_until"
                        className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                        required
                        tabIndex={1}
                        value={displayUntil}
                        style={{ textTransform: "capitalize" }}
                        onChange={(e) => setDisplayUntil(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">Best before +</label>
                      <input
                        type="number"
                        name="best_before"
                        className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                        required
                        tabIndex={1}
                        value={bestBefore}
                        style={{ textTransform: "capitalize" }}
                        onChange={(e) => setBestBefore(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">
                        If other date code type - Eg DU Min & DU Max
                      </label>
                      <input
                        type="number"
                        name="if_other_date_code_type"
                        className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                        required
                        tabIndex={1}
                        value={otherDateCodeType}
                        onChange={(e) => setOtherDateCodeType(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">F Code (tesco only)</label>
                      <input
                        type="number"
                        name="f_code"
                        className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                        required
                        tabIndex={1}
                        value={fCode}
                        style={{ textTransform: "capitalize" }}
                        onChange={(e) => setFcode(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    <div className="flex flex-col mr-[23px]">
                      <label className="labels mb-1">Suppplier site code</label>
                      <input
                        type="text"
                        name="supplier_side_code"
                        className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md`}
                        required
                        tabIndex={1}
                        value={supplierSiteCode}
                        style={{ textTransform: "capitalize" }}
                        onChange={(e) => setSupplierSiteCode(e.target.value)}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex justify-between border-t border-light-gray py-5 bg-white">
              <div></div>
              <div>
                <button
                  className="border border-skin-primary text-skin-primary me-10 py-1 px-8 font-medium rounded-md"
                  onClick={isEdit ? handleCancel : handleWithoutReason}
                  disabled={status == 6 || isUserHaveCreated ? true : false}
                >
                  Cancel
                </button>
                {submittedToISS && userData.role_id == 1 && (
                  <button
                    className="border border-skin-primary text-skin-primary me-10 py-1 px-8 font-medium rounded-md"
                    onClick={handleUnblockEvent}
                    disabled={isUserHaveCreated ? true : false}
                  >
                    Unblock
                  </button>
                )}
                {!submittedToISS && (
                  <>
                    <button
                      className="border  border-save-green bg-save-green text-white rounded-md py-1 px-8 me-10 font-medium"
                      onClick={() => {
                        setSubmissionType("save");
                        handleValidate();
                      }}
                      disabled={isUserHaveCreated ? true : false}
                    >
                      Save
                    </button>
                    <button
                      className="border border-skin-primary  text-skin-a11y bg-skin-primary py-1 px-8 font-medium rounded-md"
                      disabled={isUserHaveCreated ? true : false}
                      onClick={() => {
                        setSubmissionType("submit");
                        handleValidate();
                      }}
                    >
                      Submit
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        </Layout>
      )}

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                  {/* <!-- Modal content --> */}
                  <div className="relative bg-white rounded-lg shadow">
                    {/* <!-- Modal header --> */}
                    <div className="flex items-start justify-between p-8 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                        <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                          <FontAwesomeIcon icon={faInfo} />{" "}
                        </span>{" "}
                        Warning
                      </h3>
                      <button
                        onClick={closeModal}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                        data-modal-hide="default-modal"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />{" "}
                      </button>
                    </div>
                    {/* <!-- Modal body --> */}
                    <div className="p-8 py-0 space-y-6">
                      <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                        Mandatory information missing. Do you want to continue?
                      </p>
                    </div>
                    {/* <!-- Modal footer --> */}
                    <div className="flex items-end p-6 space-x-2 justify-end">
                      <button
                        onClick={handleContinueSubmit}
                        data-modal-hide="default-modal"
                        type="button"
                        className="text-skin-a11y bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Continue
                      </button>
                      <button
                        onClick={closeModal}
                        data-modal-hide="default-modal"
                        type="button"
                        className="border text-dark-gray focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition appear show={isCancelOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeCancelModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                  {/* <!-- Modal content --> */}
                  <div className="relative bg-white rounded-lg shadow">
                    {/* <!-- Modal header --> */}
                    <div className="flex items-start justify-between p-8 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                        <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                          <FontAwesomeIcon icon={faInfo} />{" "}
                        </span>{" "}
                        Notify issue
                      </h3>
                      <button
                        onClick={closeCancelModal}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                        data-modal-hide="default-modal"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />{" "}
                      </button>
                    </div>
                    {/* <!-- Modal body --> */}
                    <div className="p-8 py-0 space-y-6">
                      <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                        Product Cancellation Of Reason
                      </p>
                      <textarea
                        className="flex flex-col w-full rounded-md p-2 px-3 border border-light-gray2"
                        rows="8"
                        value={cancelledReasonapi}
                        onChange={(e) => setCancelledReasonapi(e.target.value)}
                        placeholder="Any details about the issue"
                        maxlength="500"
                      ></textarea>
                    </div>
                    {/* <!-- Modal footer --> */}
                    <div className="flex items-end p-6 space-x-2 justify-end">
                      <button
                        onClick={saveModalData}
                        data-modal-hide="default-modal"
                        type="button"
                        className="text-skin-a11y bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Save
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
      <Transition appear show={isUnblockOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeUnblockModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                  {/* <!-- Modal content --> */}
                  <div className="relative bg-white rounded-lg shadow">
                    {/* <!-- Modal header --> */}
                    <div className="flex items-start justify-between p-8 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                        <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                          <FontAwesomeIcon icon={faInfo} />{" "}
                        </span>{" "}
                        Notify issue
                      </h3>
                      <button
                        onClick={closeUnblockModal}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                        data-modal-hide="default-modal"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />{" "}
                      </button>
                    </div>
                    {/* <!-- Modal body --> */}
                    <div className="p-8 py-0 space-y-6">
                      <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                        Product Request Unblock of reason
                      </p>
                      <textarea
                        className="flex flex-col w-full rounded-md p-2 px-3 border border-light-gray2"
                        rows="8"
                        value={unblockedReasonapi}
                        onChange={(e) => setUnblockedReasonapi(e.target.value)}
                        placeholder="Any details about the issue"
                      ></textarea>
                    </div>
                    {/* <!-- Modal footer --> */}
                    <div className="flex items-end p-6 space-x-2 justify-end">
                      <button
                        onClick={saveUnblockModalData}
                        data-modal-hide="default-modal"
                        type="button"
                        className="text-skin-a11y bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Save
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      {isDrawerOpen && (
        <DrawerComponent
          isDrawerOpen={isDrawerOpen}
          setIsDrawerOpen={setIsDrawerOpen}
          title={title}
          dropdownData={dropdownData}
          legend={legend}
          placeholderText={placeholderText}
          dataKey={objKey}
          onNewDropdownData={handleNewDropdownData}
          username={userData.name}
          useremail={userData.email_id}
          userData={userData}
        />
      )}
    </>
  );
};

export default FinishedProductRequest;
