import React, { useMemo } from "react";

const colorMap = {
  1: {
    background: 'rgba(62, 171, 88, 0.2)',
    text: '#3EAB58',
  },
  2: {
    background: 'rgba(0, 102, 255, 0.2)',
    text: '#0066FF',
  },
  3: {
    background: 'rgb(211, 211, 211, 0.7)',
    text: '#000000',
  },
  8: {
    background: 'rgba(102, 100, 227, 0.2)',
    text: '#592cf2',
  },
  5: {
    background: 'rgba(255, 212, 169, 0.5)',
    text: '#b37858',
  },
  default: {
    background: 'transparent',
    text: '#9A9A9A',
  },
};

const departmentRenderer = (params) => {
  const department = params.value;
  const { background, text } = useMemo(
    () => colorMap[department] || colorMap.default,
    [department]
  );

  const spanStyle = {
    backgroundColor: background,
    width:"95px",
    textAlign:"center",
    display:"inline-block",
    verticalAlign:"middle",
    lineHeight:"24px",
    height:"32px",
    color: text,
    padding: '6px',
    borderRadius: '10px',
  };

  return (
    <span style={spanStyle}>
      {/* {role == 1 ? "Administrator": "Approver"} */}
      {
          department == 1 ? "Sales" : department == 2 ? "Procurement" : department == 3 ? "Financial": department == 5 ? "Technical" : department ==8 ? "Logistics" : ""
      }
    </span>
  );
};

export default departmentRenderer;
