import FinishedProductRequest from '@/components/FinishedProductRequest';
import { apiConfig } from '@/services/apiConfig';
import React from 'react'

const index = ({dropdowns,userData,finishedProductData}) => {
  return (
    <FinishedProductRequest dropdowns={dropdowns} userData={userData} finishedProductData={finishedProductData} pageType={"update"}/>
  )
}

export default index;

export const getServerSideProps=async(context)=>{
  const serverAddress=apiConfig.serverAddress;
  const userData = context.req.cookies.user; // Access the "user" cookie
  const { productId } = context.params; // Accessing the id from URL parameters
  console.log('product id', productId);
  try{
    const allDropDowns = [
      "reasonForRequest",
      "masterProductCode",
      "markVariety",
      "brand",
      "endCustomer",
      "classifiedAllergicTypes","machineFormat","newOuterBoxType","temperatureGrade","packagingTypes"
    ];
  
    const dropdownsRequest = fetch(
      `${serverAddress}products/get-products-dropdowns-list`,
      {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${userData.token}`,
        },
        body: JSON.stringify(allDropDowns),
      }
    );

    const finishedProuductRequest = fetch(
      `${serverAddress}products/get-finished-product-by-id/${productId}`,
      {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${userData.token}`,
        },
      }
    );

    const [dropdownsResponse, finishedPRoductResponse] = await Promise.all([
      dropdownsRequest,
     finishedProuductRequest,
    ]);
    const allDropdownsList = await dropdownsResponse.json();
    const finishedProductData = await finishedPRoductResponse.json();

    console.log("prop finish", finishedProductData);
    console.log("dropdown", allDropdownsList);

    return{
      props:{
        finishedProductData:finishedProductData,
        dropdowns:allDropdownsList,
        userData: JSON.parse(userData),
      },
    }
  }catch(error){
    console.error("Error fetching data",error);
    return{
      props:{
        dropdowns:null,
        finishedProductData:null,
        userData:null,
      }
    }
  }
  }
