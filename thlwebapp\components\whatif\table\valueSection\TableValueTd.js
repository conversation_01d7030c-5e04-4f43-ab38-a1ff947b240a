import React, { useRef } from "react";

const TableValueTd = ({
  quarterData,
  row,
  previousSunday,
  currentWeek,
  calendarWeeks,
  currency,fiscalyear,currentFiscalYear
}) => {
  const positioningRef = useRef(null);

  return (
    <>
      {calendarWeeks.map((weekStartDate, weekIndex) => {
        const productData = quarterData.find(
          (data) => data.startWeek === weekStartDate
        );
        const isToday = weekStartDate === previousSunday && row.fiscalyear==currentFiscalYear;

        const promotions = row?.promotions?.filter((data) => {
          const currentDate = new Date(productData?.startWeek);
          const currentMonth = currentDate.getMonth() + 1;
          const startWeekYear = currentDate.getFullYear();

          const financialYear =
            currentMonth >= 10 ? startWeekYear + 1 : startWeekYear;
          return (
            productData?.week >= data?.promo_start_week_no &&
            productData?.week <= data?.promo_end_week_no
          );
        });
        const valueToDisplay = (() => {
          if (promotions && promotions.length > 0) {
            for (const promo of promotions) {
              const promoWeekData = promo.weekData[productData?.week]?.current;
              if (
                promoWeekData?.sales != null &&
                productData?.week >= currentWeek
              ) {
                return { data: promoWeekData.sales, isPromo: true };
              }
            }
          }
          return {
            data:
              fiscalyear != productData.fiscalYear &&
              productData.NoDataNextFY_Q1 == 1
                ? ""
                : productData?.data[1]?.value ?? 0,
            isPromo: false,
          };
        })();

        return (
          <td
          title={fiscalyear != productData.fiscalYear &&
            productData.NoDataNextFY_Q1 == 1
            ?"Data does not exist for this product":""}
            ref={positioningRef}
            key={weekIndex}
            className={`${
              isToday ? "bg-currentWeek" : fiscalyear != productData.fiscalYear &&
            productData.NoDataNextFY_Q1 == 1
            ? "bg-gray" : productData.hasData==0?"bg-gray"
            : "bg-white"
            } !text-center hover:cursor-pointer relative`}
          >
            {productData.hasData==0?"":valueToDisplay &&
                valueToDisplay.data !== undefined &&
                valueToDisplay.data !== "" &&
                !isNaN(valueToDisplay.data) &&
                (valueToDisplay.data < 0
                  ? `-${currency}${Math.abs(
                      Number(valueToDisplay.data)
                    ).toFixed(2)}`
                  : `${currency}${Number(valueToDisplay.data).toFixed(2)}`)}
          </td>
        );
      })}
    </>
  );
};

export default TableValueTd;
