SELECT
    pnv.id AS variety_id,
    pnv.code,
    pnv.description,
    pnvst.comment,
    pnv.prophet_id,
    ps.prophet_name,
    mp.id AS master_product_id,
    mp.code AS master_product_code,
    mp.name AS master_product_name
FROM
    [THL_Webapp_Portal].[dbo].[product_nvariety] pnv
    LEFT JOIN [THL_Webapp_Portal].[dbo].[product_nvariety_status] pnvst ON pnv.id = pnvst.variety_id and pnvst.is_latest=1
    LEFT JOIN [THL_Webapp_Portal].[dbo].[prophet_system] ps ON pnv.prophet_id = ps.id
    LEFT JOIN [THL_Webapp_Portal].[dbo].[master_products] mp ON pnv.master_product_code = mp.code
    AND pnv.prophet_id = mp.prophet_id
WHERE
    (pnv.description LIKE '%' + @value + '%'
    OR pnv.code LIKE '%' + @value + '%')
    AND pnv.is_active = 1
    AND (
        pnv.prophet_id = @prophetId
        OR pnv.prophet_id = 5
    ) order by pnv.code