import React, { useState } from "react";
import {
  Button,
  <PERSON>over,
  PopoverSurface,
  PopoverTrigger,
  Badge,
  Checkbox,
} from "@fluentui/react-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faChevronDown, faFilter } from "@fortawesome/free-solid-svg-icons";
import { formatDisplayWithTime } from "@/utils/whatif/utils/getFormattedDate";

// {
//   "reasons": {
//       "2414": "Orders",
//       "2415": "Packhouse Error",
//       "2416": "Rejected at Depot"
//   },
//   "subReasons": {
//       "3": "Packaging",
//       "16": "Retailer EDI issues",
//       "26": "No ASN"
//   }
// }

const ReasonsDetails = ({
  reasonsData,
  type,
  selectedReasons,
  setSelectedReasons,
  selectedSubReasons,
  setSelectedSubReasons,
  seeAll,
}) => {
  const sortedReasons =
    type == "reasonsDetails" &&
    reasonsData?.sort(
      (a, b) =>
        new Date(
          b.REASON_UPDATED_BY
            ? b.REASON_UPDATED_TIMESTAMP
            : b.REASON_ADDED_TIMESTAMP
        ) -
        new Date(
          a.REASON_UPDATED_BY
            ? a.REASON_UPDATED_TIMESTAMP
            : a.REASON_ADDED_TIMESTAMP
        )
    );


  const handleCheckboxChange = (reasonId) => {
    let updatedReasonsData = {};

    if (Object.keys(reasonsData).length > 0) {
      updatedReasonsData = Object.fromEntries(
        Object.entries(reasonsData).slice(1)
      );
    }

    if (type === "reasonsList") {
      setSelectedSubReasons([]);
      if (reasonId === "Select All") {
        setSelectedReasons((prevSelected) =>
          prevSelected.length === Object.values(updatedReasonsData).length
            ? []
            : Object.values(updatedReasonsData)
        );
      } else {
        setSelectedReasons((prevSelected) =>
          prevSelected.includes(reasonId)
            ? prevSelected.filter((id) => id !== reasonId)
            : [...prevSelected, reasonId]
        );
      }
    } else if (type === "subReasonsList") {
      setSelectedReasons([]);
      if (reasonId === "Select All") {
        setSelectedSubReasons((prevSelected) =>
          prevSelected.length === Object.values(updatedReasonsData).length
            ? []
            : Object.values(updatedReasonsData)
        );
      } else {
        setSelectedSubReasons((prevSelected) =>
          prevSelected.includes(reasonId)
            ? prevSelected.filter((id) => id !== reasonId)
            : [...prevSelected, reasonId]
        );
      }
    }
  };

  return (
    <>
      {type == "reasonsDetails" ? (
        <Popover>
          <PopoverTrigger>
            {sortedReasons?.length > 0 && (
              <Button className=" !border-0 !bg-transparent !absolute !top-1 !p-1 !right-1 !min-w-0">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 512 512"
                  className="w-4 h-5"
                  fill="#000"
                >
                  <path d="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416zm0 464A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM216 336c-13.3 0-24 10.7-24 24s10.7 24 24 24h80c13.3 0 24-10.7 24-24s-10.7-24-24-24h-8V248c0-13.3-10.7-24-24-24H216c-13.3 0-24 10.7-24 24s10.7 24 24 24h24v64H216zm40-144a32 32 0 1 0 0-64 32 32 0 1 0 0 64z" />
                </svg>
              </Button>
            )}
          </PopoverTrigger>
          <PopoverSurface
            tabIndex={-1}
            className="!p-0 !rounded-xl !w-2/5 !bg-gray-100"
          >
            <div className="w-full">
              <div className="rounded-sm p-3 bg-skin-primary text-white rounded-tl-xl rounded-tr-xl">
                Reasons Added
              </div>
              <div className="p-3">
                <div className="pb-2 !max-h-[28rem] overflow-y-auto">
                  {sortedReasons?.map((reason, index) => {
                    return (
                      <div
                        className="px-4 py-2 shadow m-3 bg-white rounded-lg"
                        key={index}
                      >
                        <table cellSpacing={8} className="w-full text-sm">
                          <tr>
                            <td className="w-28"></td>
                            <td></td>
                          </tr>
                          <tr>
                            <th colSpan={2} className="pt-3 ">
                              Reason {index + 1}
                              <hr className="border-b border-gray-300 mb-2" />
                            </th>
                          </tr>

                          <tr>
                            <td className="align-top text-gray-400">Qty :</td>
                            <td>{reason?.REASON_QTY}</td>
                          </tr>
                          <tr>
                            <td className="align-top text-gray-400">
                              Reason :
                            </td>
                            <td>{reason?.MAIN_REASON}</td>
                          </tr>
                          <tr>
                            <td className="align-top text-gray-400">
                              Sub Reason :
                            </td>
                            <td>{reason?.SUB_REASON}</td>
                          </tr>
                          <tr>
                            <td className="align-top text-gray-400">
                              Comment:
                            </td>
                            <td>{reason?.COMMENT}</td>
                          </tr>
                          <tr>
                            <td colSpan={2}>
                              <Badge
                                className="!w-full !flex !justify-between !my-3"
                                appearance="filled"
                                color={`${
                                  !!reason.REASON_UPDATED_BY
                                    ? "warning"
                                    : "success"
                                }`}
                              >
                                <div className="px-2 py-1 text-sm w-auto">
                                  {!!reason.REASON_UPDATED_BY
                                    ? "Updated By"
                                    : "Added By"}
                                  :{" "}
                                  {!!reason.REASON_UPDATED_BY
                                    ? reason.REASON_UPDATED_BY
                                    : reason.REASON_ADDED_BY}
                                </div>
                                <div className="px-2 py-1 text-sm w-auto">
                                  {!!reason.REASON_UPDATED_BY
                                    ? "Updated At"
                                    : "Added At"}
                                  :{" "}
                                  {!!reason.REASON_UPDATED_BY
                                    ? formatDisplayWithTime(
                                        reason.REASON_UPDATED_TIMESTAMP
                                      )
                                    : formatDisplayWithTime(
                                        reason.REASON_ADDED_TIMESTAMP
                                      )}
                                </div>
                              </Badge>
                            </td>
                          </tr>
                        </table>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </PopoverSurface>
        </Popover>
      ) : type == "reasonsList" && seeAll ? (
        <Popover>
          <PopoverTrigger>
            <Button className="!border-0 !bg-transparent !absolute !p-1 !right-2 !min-w-0">
              {selectedReasons.length > 0 ? (
                <FontAwesomeIcon icon={faFilter} className="" />
              ) : (
                <FontAwesomeIcon icon={faChevronDown} className="" />
              )}
            </Button>
          </PopoverTrigger>
          <PopoverSurface
            tabIndex={-1}
            className="!p-0 !rounded-xl !w-[20%] !bg-gray-100 absolute"
          >
            <div className="w-full">
              <div className="rounded-sm p-3 bg-skin-primary text-white rounded-tl-xl rounded-tr-xl">
                Reasons
              </div>
            <div className="p-3">
                <div className="pl-2">
                  <button
                    className="flex items-center space-x-1 p-1 hover:cursor-pointer"
                    onClick={() => setSelectedReasons([])}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 640 512"
                      className="w-5 h-5"
                      fill="#444"
                    >
                      <path d="M507.3 27.3c6.2-6.2 6.2-16.4 0-22.6s-16.4-6.2-22.6 0l-144 144-18.6-18.6c-20.2-20.2-51.4-24.6-76.3-10.7L16.4 246.9C6.3 252.5 0 263.2 0 274.8c0 8.5 3.4 16.6 9.3 22.6L214.7 502.7c6 6 14.1 9.3 22.6 9.3c11.6 0 22.3-6.3 27.9-16.4L392.6 266.2c13.9-25 9.5-56.1-10.7-76.3l-18.6-18.6 144-144zM299.5 152.8l59.7 59.7c10.1 10.1 12.3 25.7 5.3 38.2l-21.2 38.1L223.2 168.6l38.1-21.2c12.5-6.9 28.1-4.8 38.2 5.3zm-105.4 32L327.2 317.9l-90 162.1L113.9 356.7l41.4-41.4c6.2-6.2 6.2-16.4 0-22.6s-16.4-6.2-22.6 0L91.3 334.1 32.1 274.8l162.1-90z" />
                    </svg>{" "}
                    Clear Filters
                  </button>
                </div>
                {Object.keys(reasonsData).length > 0 ? (
                  Object.entries(reasonsData).map(([reason, reasonId]) => (
                    <label
                      key={reasonId}
                      className="flex items-center space-x-1 p-1 hover:cursor-pointer"
                    >
                      <Checkbox
                        checked={
                          reason === "Select All"
                            ? selectedReasons.length ===
                              Object.keys(reasonsData).length - 1
                            : selectedReasons.includes(reasonId)
                        }
                        onChange={() => handleCheckboxChange(reasonId)}
                      />
                      <span>{reason}</span>
                    </label>
                  ))
                ) : (
                  <div className="text-gray-500 text-center p-2">
                    No reasons added
                  </div>
                )}
              </div>
            </div>
          </PopoverSurface>
        </Popover>
      ) : type == "subReasonsList" && seeAll ? (
        <Popover>
          <PopoverTrigger>
            <Button className="!border-0 !bg-transparent !absolute !p-1 !right-2 !min-w-0">
              {selectedSubReasons.length > 0 ? (
                <FontAwesomeIcon icon={faFilter} className="" />
              ) : (
                <FontAwesomeIcon icon={faChevronDown} className="" />
              )}
            </Button>
          </PopoverTrigger>
          <PopoverSurface
            tabIndex={-1}
            className="!p-0 !rounded-xl !w-[20%] !bg-gray-100 absolute"
          >
            <div className="w-full">
              <div className="rounded-sm p-3 bg-skin-primary text-white rounded-tl-xl rounded-tr-xl">
                Sub Reasons
              </div>
              <div className="p-3">
                <div className="pl-3">
                  <button
                    className="flex items-center space-x-1 p-1 hover:cursor-pointer"
                    onClick={() => setSelectedSubReasons([])}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 640 512"
                      className="w-5 h-5"
                      fill="#444"
                    >
                      <path d="M507.3 27.3c6.2-6.2 6.2-16.4 0-22.6s-16.4-6.2-22.6 0l-144 144-18.6-18.6c-20.2-20.2-51.4-24.6-76.3-10.7L16.4 246.9C6.3 252.5 0 263.2 0 274.8c0 8.5 3.4 16.6 9.3 22.6L214.7 502.7c6 6 14.1 9.3 22.6 9.3c11.6 0 22.3-6.3 27.9-16.4L392.6 266.2c13.9-25 9.5-56.1-10.7-76.3l-18.6-18.6 144-144zM299.5 152.8l59.7 59.7c10.1 10.1 12.3 25.7 5.3 38.2l-21.2 38.1L223.2 168.6l38.1-21.2c12.5-6.9 28.1-4.8 38.2 5.3zm-105.4 32L327.2 317.9l-90 162.1L113.9 356.7l41.4-41.4c6.2-6.2 6.2-16.4 0-22.6s-16.4-6.2-22.6 0L91.3 334.1 32.1 274.8l162.1-90z" />
                    </svg>{" "}
                    Clear Filters
                  </button>
                </div>
                {Object.keys(reasonsData).length > 0 ? (
                  Object.entries(reasonsData).map(
                    ([subReason, subReasonId]) => (
                      <label
                        key={subReasonId}
                        className="flex items-center space-x-2 p-2 hover:cursor-pointer"
                      >
                        <Checkbox
                          checked={
                            subReason === "Select All"
                              ? selectedSubReasons.length ===
                                Object.keys(reasonsData).length - 1
                              : selectedSubReasons.includes(subReasonId)
                          }
                          onChange={() => handleCheckboxChange(subReasonId)}
                        />
                        <span>{subReason}</span>
                      </label>
                    )
                  )
                ) : (
                  <div className="text-gray-500 text-center p-2">
                    No sub-reasons added
                  </div>
                )}
                
              </div>
            </div>
          </PopoverSurface>
        </Popover>
      ) : null}
    </>
  );
};

export default ReasonsDetails;
