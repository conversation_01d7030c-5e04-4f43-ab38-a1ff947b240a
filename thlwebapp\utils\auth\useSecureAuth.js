import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useMsal } from '@azure/msal-react';
import { getCurrentUser, login, logout, clearClientStorage } from '@/utils/secureStorage';
import { toast } from 'react-toastify';

export const useSecureAuth = () => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { instance } = useMsal();
  const router = useRouter();

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const currentUser = await getCurrentUser();
      if (currentUser) {
        setUser(currentUser);
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const secureLogin = async (redirect) => {
    setIsLoading(true);
    
    try {
      const requestedScope = `api://${process.env.NEXT_PUBLIC_CLIENT_ID}/access_as_user`;
      
      const request = {
        prompt: "select_account", 
        scopes: [requestedScope],
      };

      const loginObj = await instance.loginPopup(request);
      instance.setActiveAccount(loginObj.account);

      // Acquire a separate Graph token for company details
      const graphToken = await instance.acquireTokenSilent({
        scopes: ["User.Read"],
        account: loginObj.account,
      });

      // Send both tokens to backend - the backend handles all session management
      const result = await login(loginObj.accessToken, {
        graphToken: graphToken.accessToken
      });
      
      if (!result.success) {
        instance.clearCache()
        throw new Error(result.error || 'Login failed');
      }

      setUser(result.user);
      setIsAuthenticated(true);

      // Log login event - simplified to use session data
      await logLoginEvent(loginObj.account, result.user);
      instance.clearCache()
      if (redirect) {
        router.replace(redirect);
      } else {
        router.replace("/suppliers");
      }

      toast.success("Login successful!", { position: "top-right" });

    } catch (error) {
      console.error("Secure login failed:", error);
      toast.error(error.message || "Login failed", { position: "top-right" });
      setIsAuthenticated(false);
      setIsLoading(false);
    }
  };

  const secureLoginExistingAccount = async (redirect) => {
    setIsLoading(true);

    try {
      const activeAccount = instance.getActiveAccount();
      if (!activeAccount) {
        throw new Error("No active account found!");
      }

      const loginObj = await instance.acquireTokenSilent({
        scopes: [`api://${process.env.NEXT_PUBLIC_CLIENT_ID}/access_as_user`],
        account: activeAccount,
        forceRefresh: true,
      });

      // Acquire Graph token
      const graphToken = await instance.acquireTokenSilent({
        scopes: ["User.Read"],
        account: activeAccount,
      });

      const result = await login(loginObj.accessToken, {
        graphToken: graphToken.accessToken
      });
      
      if (!result.success) {
        throw new Error(result.error || 'Login failed');
      }

      setUser(result.user);
      setIsAuthenticated(true);

      await logLoginEvent(activeAccount, result.user);

      if (redirect) {
        router.push(redirect);
      } else {
        router.push("/suppliers");
      }

    } catch (error) {
      console.error("Secure login failed:", error);
      toast.error(error.message || "Login failed", { position: "top-right" });
    } finally {
      setIsLoading(false);
    }
  };

  // Simplified login logging - no localStorage needed
  const logLoginEvent = async (account, userData) => {
    try {
      const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8081';
      await fetch(`${apiBase}/api/logs/logLogin`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: 'include', // Use session authentication
        body: JSON.stringify({ 
          userData: account,
          company: userData?.ADCompanyName || userData?.companyName
        }),
      });
    } catch (error) {
      console.error('Login logging failed:', error);
    }
  };

  const secureLogout = async () => {
    try {
      await logout();
      
      setUser(null);
      setIsAuthenticated(false);
      
      instance.clearCache()
      // instance.logoutPopup();
      instance.logout({
        postLogoutRedirectUri: '/login',
      });
      
      // router.push('/login');
      
    } catch (error) {
      console.error('Logout failed:', error);
      instance.clearCache()
      clearClientStorage();
      setUser(null);
      setIsAuthenticated(false);
      instance.logoutPopup();
    }
  };

  return {
    user,
    isAuthenticated,
    isLoading,
    secureLogin,
    secureLoginExistingAccount,
    secureLogout,
    checkAuthStatus
  };
}; 