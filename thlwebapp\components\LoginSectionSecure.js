import React, { useEffect, useState } from "react";
import Image from "next/image";
import { useMsal } from "@azure/msal-react";
import { useRouter } from "next/router";
import { useLoading } from "@/utils/loaders/loadingContext";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useSecureAuth } from "@/utils/auth/useSecureAuth";

const LoginSectionSecure = () => {
  const { accounts } = useMsal();
  const router = useRouter();
  const { redirect } = router.query;
  const { setIsLoading } = useLoading();
  const [isMounted, setIsMounted] = useState(false);
  
  const {
    user,
    isAuthenticated,
    isLoading: authLoading,
    secureLogin,
    secureLoginExistingAccount,
    secureLogout
  } = useSecureAuth();

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (isAuthenticated && user && isMounted) {
      const targetUrl = redirect || "/suppliers";
      router.replace(targetUrl).catch((err) => {
        // Ignore navigation cancellation errors
        if (err.cancelled) {
          console.log('Navigation was cancelled:', targetUrl);
        } else {
          console.error('Navigation error:', err);
        }
      });
    }
  }, [isAuthenticated, user, redirect, router, isMounted]);

  useEffect(() => {
    if (isMounted) {
      setIsLoading(authLoading);
    }
  }, [authLoading, setIsLoading, isMounted]);

  const signInHandler = async () => {
    await secureLogin(redirect);
  };

  const useThisAccountHandler = async () => {
    await secureLoginExistingAccount(redirect);
  };

  const signOutHandler = async () => {
    await secureLogout();
  };

  if (!isMounted) {
    return (
      <div className="flex justify-center items-center">
          <h2 className="text-2xl font-bold text-center mb-6 text-gray-800">
            Welcome to THL Portal
          </h2>
          <div className="text-center">
            <div className="text-gray-600">Loading...</div>
          </div>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center">
      <div className="bg-white rounded-lg shadow-lg p-8 w-96">
        <div className="flex justify-center mb-6">
          <Image src="/images/terradace-logo.png" alt="Company Logo" width={160} height={50} />
        </div>
        
        <h2 className="text-2xl font-bold text-center mb-6 text-gray-800">
          Welcome to THL Portal
        </h2>

        {accounts?.length > 0 ? (
          <div className="space-y-4">
            <div className="border rounded-lg p-4 bg-gray-50">
              <div className="flex items-center space-x-3">
                <Image src="/images/user-account.png" alt="User" width={40} height={40} />
                <div>
                  <p className="font-medium text-gray-800">
                    {accounts[0]?.name}
                  </p>
                  <p className="text-sm text-gray-600">
                    {accounts[0]?.username}
                  </p>
                </div>
              </div>
              
              <div className="mt-4 space-y-2">
                <button
                  onClick={useThisAccountHandler}
                  disabled={authLoading}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-lg transition duration-200"
                >
                  {authLoading ? "Signing in..." : "Continue as " + accounts[0]?.name}
                </button>
                
                <button
                  onClick={signOutHandler}
                  disabled={authLoading}
                  className="w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition duration-200"
                >
                  Use different account
                </button>
              </div>
            </div>
            
            <div className="text-center">
              <span className="text-gray-500">or</span>
            </div>
          </div>
        ) : null}

        <button
          onClick={signInHandler}
          disabled={authLoading}
          className="w-full bg-white border border-gray-300 hover:bg-gray-50 disabled:bg-gray-100 text-gray-700 font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center space-x-3"
        >
          <Image src="/images/microsoft-icon.png" alt="Microsoft" width={20} height={20} />
          <span>
            {authLoading ? "Signing in..." : "Sign in with Microsoft"}
          </span>
        </button>
      </div>
      
      <ToastContainer />
    </div>
  );
};

export default LoginSectionSecure; 
