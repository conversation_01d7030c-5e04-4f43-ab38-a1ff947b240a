"use strict";

const express = require("express");
const { requireRole } = require("../middleware/roleAuth");
const supplierController = require("../controllers/suppliers");
const { validateSession } = require("../middleware/sessionAuth");
const router = express.Router();
const app = express();

const {
  getSuppliers,
  getLinkedSuppliers,
  getSupplierRoles,
  getSupplierLinks,
  getSupplierById,
  createSupplier,
  updateSupplier,
  deleteSupplier,
  addSendacGroup,
  getFilteredSupplierNames,
  getIfUniqueSupplierCode,
  getRolePermissions,
  getAllDropdownList,
  checkProphetCode,
  insertProphetCode,
  getLinksBySendac,
  getSendacGroupByProphets,
  getFilteredDistribution,checkDistributions, getLinksByProphets, getProductByProphet,deleteAll,getSuppliersExtractData,getSupplierTypes
} = supplierController;

router.use(validateSession);

router.get("/get-suppliers/:company/:prophetId", getSuppliers);
router.get("/get-linked-suppliers/:linkedSup/:company", getLinkedSuppliers);
router.get("/get-role-permissions", getRolePermissions);
router.get("/get-supplier-by-id/:id", getSupplierById);
router.post("/add-supplier", createSupplier);
router.put("/update-supplier/:id", updateSupplier);
router.delete("/delete-supplier/:id", deleteSupplier);
router.get("/get-supplier-roles", getSupplierRoles);
router.get("/get-supplier-types", getSupplierTypes);
//router.get("/get-supplier-links/:roles", getSupplierLinks);
router.get("/get-supplier-links/:company", getSupplierLinks);
router.get("/get-link-by-sendac/:sendacId", getLinksBySendac);
router.post("/get-sendac-group-by-prophets", getSendacGroupByProphets);
router.get("/get-link-by-prophets/:prophetId", getLinksByProphets);
router.post("/add-group", addSendacGroup);
router.get(
  "/get-filtered-supplier-names/:searchString",
  getFilteredSupplierNames
);
router.get(
  "/get-filtered-supplier-code/:searchString/:supplierId",
  getIfUniqueSupplierCode
);
router.post(
  "/get-filtered-distributions/:searchString",
  getFilteredDistribution
);
router.post(
  "/check-distributions",
  checkDistributions
);
router.post("/get-all-dropdown-list", getAllDropdownList);
router.post("/check-prophet-code", checkProphetCode);
router.post("/create-prophet-code", insertProphetCode);
router.get('/get-product-by-prophet/:prophet_id', getProductByProphet);
router.get('/get-extract-data/:id', getSuppliersExtractData);
router.delete('/delete-all', deleteAll);

// Example: Only allow role_id 1 to access this endpoint
router.get("/admin-only", requireRole(1), (req, res) => {
  res.json({ message: "Admin access granted", user: req.session.user });
});

// Example: Allow multiple roles
router.get("/suppliers", requireRole([1, 2, 5, 6]), supplierController.getSuppliers);

// Example: Public endpoint (no role restriction)
router.get("/public", (req, res) => {
  res.json({ message: "Public access" });
});

module.exports = router;
