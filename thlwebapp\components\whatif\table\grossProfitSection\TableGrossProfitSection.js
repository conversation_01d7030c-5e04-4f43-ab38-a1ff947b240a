import React, { useEffect, useRef } from "react";
import useOnScreen from "../../hooks/useOnScreen";
import TableColumnTh from "../TableColumnTh";
import TableGrossProfitTd from "./TableGrossProfitTd";
import { Tooltip } from "@fluentui/react-components";

function TableGrossProfitSection({
  currentData,
  rowHeight,
  noOfWeeks,
  checkedStates,
  columnRefs,
  columnWidths,
  currentStartWeek,
  currentWeek,
  calendarWeeks,
  setIsGPRendered,
  currency,
  ctxCalenderData,
  customerDataTotals,
  fiscalyear,
  currentFiscalYear,
}) {
  const rowGrossProfitRef = useRef();
  const isRowGrossProfitVisible = useOnScreen(rowGrossProfitRef);

  useEffect(() => {
    if (isRowGrossProfitVisible) {
      setIsGPRendered(true);
    }
  }, [isRowGrossProfitVisible]);
  return (
    <>
      <tr
        style={{ top: `${rowHeight}px` }}
        className="sticky z-10 gp"
        ref={rowGrossProfitRef}
      >
        {!isRowGrossProfitVisible && <td style={{ minHeight: "10px" }}></td>}
        {isRowGrossProfitVisible && (
          <TableColumnTh
            rowId="sectionrow"
            rowTitle="GROSS PROFIT"
            headHeight={rowHeight}
            noOfWeeks={noOfWeeks}
            checkedStates={checkedStates}
            columnRefs={columnRefs}
            columnWidths={columnWidths}
          />
        )}
      </tr>
      {isRowGrossProfitVisible &&
        currentData?.map((row, index) => {
          const quarterData = [
            ...(row.quarters?.Q1 || []),
            ...(row.quarters?.Q2 || []),
            ...(row.quarters?.Q3 || []),
            ...(row.quarters?.Q4 || []),
            ...(row.quarters?.Q5 || []),
          ];

          return (
            <tr key={index}>
              <TableColumnTh
                isTd={true}
                checkedStates={checkedStates}
                columnRefs={columnRefs}
                columnWidths={columnWidths}
                row={row}
                tdValue={row?.total_product_gross_profit?.toFixed(2)}
              />
              <TableGrossProfitTd
                quarterData={quarterData}
                previousSunday={currentStartWeek}
                currentWeek={currentWeek}
                calendarWeeks={calendarWeeks}
                row={row}
                currency={currency}
                fiscalyear={fiscalyear}
                currentFiscalYear={currentFiscalYear}
              />
            </tr>
          );
        })}

      {/* table row to display section totals */}
      <tr>
        <TableColumnTh
          checkedStates={checkedStates}
          columnRefs={columnRefs}
          columnWidths={columnWidths}
        />
        {ctxCalenderData.map((ele, index) => {
          const weekNo = ele.fiscalweek.toString();

          return (
            <Tooltip
              key={index}
              content="Total of all products for this week"
              relationship="label"
              className="!bg-white"
            >
              <td
                key={index}
                className={`!text-center font-bold border-[#ddd] ${
                  ele.currentWeek == weekNo &&
                  ele.fiscalyear == currentFiscalYear
                    ? "bg-currentWeek"
                    : ""
                }`}
              >
                {customerDataTotals.weeklyAllProductsTotals?.[ele.fiscalyear]?.[
                  weekNo
                ]?.total_all_products_gross_profit?.toFixed(2)}
              </td>
            </Tooltip>
          );
        })}
      </tr>
    </>
  );
}

export default TableGrossProfitSection;
