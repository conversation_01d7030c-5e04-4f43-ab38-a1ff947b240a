import Layout from "@/components/Layout";
import { apiConfig } from "@/services/apiConfig";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { ThreeCircles } from "react-loader-spinner";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import PackgingForm from "@/components/PackagingForm";
import { extractCompanyFromEmail } from "@/utils/extractCompanyFromEmail";
import { logout } from "@/utils/secureStorage";


const index = ({ userData }) => {
  const router = useRouter();
  const [dropdowns, setDropdowns] = useState(null);

  useEffect(() => {
    // const company=Cookies.get("company");
    const company=userData?.company || extractCompanyFromEmail(userData?.email);
    let prophetId=1;
    if(company=="dpsltd"){
      prophetId=1;
    }else if(company=="efcltd"){
      prophetId=3;
    }else if(company=="fpp-ltd"){
      prophetId=4;
    }else if (company=="iss" || "issproduce"){
      prophetId=5;
    }
    const fetchData = async () => {
      const serverAddress = apiConfig.serverAddress;

      try {
        const allDropDowns = [
          "endCustomer",
          "masterProductCode",
          "packagingMasterproductCodes",
          "PackagingType",
          "RecyclableOPRL",
          "subProductCode",
          "SustainableForestryPaper",
          "TradingBusiness",
          "PackagingMaterialColors",
          "PackagingMaterialTypes",
          "PackagingReason",
        ];

        const res = await fetch(
          `${serverAddress}products/get-products-dropdowns-list?prophetId=${prophetId}`,
          {
            method: "POST",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
            },
            credentials: "include",
            body: JSON.stringify(allDropDowns),
          }
        );        
        // console.log("dropdownsRequest",res);
        if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async() => {
            await logout();
            const redirectUrl = `/login?redirect=${encodeURIComponent(
              window.location.pathname
            )}`;
            router.push(redirectUrl);
          }, 3000);
        }
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } 

        const allDropdownsList = await res.json();

        setDropdowns(allDropdownsList);
      } catch (error) {
        console.error("Error fetching data", error);
      }
    };

    fetchData();
  }, []);

  return (
    <Layout userData={userData}>
      <ToastContainer limit={1} />
      {!dropdowns ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "calc(100vh - 100px)",
            // width: "calc(100vw - 125px)",
          }}
        >
          <ThreeCircles
            color="#002D73"
            height={50}
            width={50}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      ) : (
        
        <PackgingForm
          dropdowns={dropdowns}
          userData={userData}
          pageType={"add"}
        />
       
      )}
      </Layout>
  );
};

export default index;

export const getServerSideProps = async (context) => {
  try {
    const { req, resolvedUrl } = context;
    const sessionId = req.cookies.thl_session;

    if (!sessionId) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(resolvedUrl)}`,
          permanent: false,
        },
      };
    }

    const apiBase =
      process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8081";

    const response = await fetch(`${apiBase}/api/auth/me`, {
      method: "GET",
      headers: {
        Cookie: `thl_session=${sessionId}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(resolvedUrl)}`,
          permanent: false,
        },
      };
    }

    const { user } = await response.json();

    return {
      props: {
        userData: user,
      },
    };
  } catch (error) {
    console.error("Error in getServerSideProps:", error);
    return {
      redirect: {
        destination: "/login",
        permanent: false,
      },
    };
  }
};
