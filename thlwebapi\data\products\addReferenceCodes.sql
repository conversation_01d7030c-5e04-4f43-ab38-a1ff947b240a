-- DECLARE @table_name VARCHAR(50);

IF @table_name = 'product'
    BEGIN
       INSERT INTO [dbo].[master_products]
           ([name]
           ,[code]
           ,[is_new]
           ,[is_active])
        VALUES
            (@description
            ,@code
            ,@is_new
            ,@is_active)

        SELECT * from master_products;
    END;

IF @table_name = 'mark_variety'
    BEGIN
       INSERT INTO [dbo].[mark_variety]
           ([name]
           ,[is_new]
           ,[is_active])
        VALUES
            (@description
            ,@is_new
            ,@is_active)
        SELECT * from mark_variety;
    END;

IF @table_name == 'brand'
    BEGIN
       INSERT INTO [dbo].[brands]
           ([name]
           ,[code]
           ,[is_new]
           ,[is_active])
        VALUES
            (@description
            ,@code
            ,@is_new
            ,@is_active)

        SELECT * from brands;
    
    END;

IF @table_name == 'caliber_size'
    BEGIN
       INSERT INTO [dbo].[caliber_size]
           ([name]
           ,[code]
           ,[is_new]
           ,[is_active])
        VALUES
            (@description
            ,@code
            ,@is_new
            ,@is_active)

        SELECT * from caliber_size;

    END;

IF @table_name == 'variety'
    BEGIN
       INSERT INTO [dbo].[product_nvariety]
           ([description]
           ,[code]
           ,[is_new]
           ,[is_active])
        VALUES
            (@description
            ,@code
            ,@is_new
            ,@is_active)

        SELECT * from product_nvariety;
    END;

IF @table_name == 'end_customer'
    BEGIN
       INSERT INTO [dbo].[end_customers]
           ([name]
           ,[code]
           ,[is_new]
           ,[is_active])
        VALUES
            (@description
            ,@code
            ,@is_new
            ,@is_active)

        SELECT * from end_customers;
    END;

IF @table_name == 'box_type'
    BEGIN
       INSERT INTO [dbo].[box_types]
           ([box_type]
           ,[code]
           ,[is_new]
           ,[is_active])
        VALUES
            (@description
            ,@code
            ,@is_new
            ,@is_active)

        SELECT * from box_types;

    END;

IF @table_name == 'country_of_origin'
    BEGIN
       INSERT INTO [dbo].[country_of_origin]
           ([name]
           ,[code]
           ,[is_new]
           ,[is_active])
        VALUES
            (@description
            ,@code
            ,@is_new
            ,@is_active)

        SELECT * from country_of_origin;
    END;

    