import { apiConfig } from "@/services/apiConfig";
import { logout } from "@/utils/secureStorage";
import { toast } from "react-toastify";

export async function getNextQuarterDataByPkey(pkey,weekNo, currentYear,whatif_id) {
  const encodedPkey = encodeURIComponent(pkey);

    return await fetch(`${apiConfig.serverAddress}whatif/get-next-quarter-product-data-by-week?year=${currentYear}&pkey=${encodedPkey}&weekNo=${weekNo}&whatif_id=${whatif_id}`, {
      method: "GET",
      credentials: "include",
    })
      .then(async (res) => {
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async() => {
            await logout();
            const redirectUrl = `/login?redirect=${encodeURIComponent(window.location.pathname)}`;
            window.location.href = redirectUrl;
          }, 3000);
        }
        if (res.status === 200) {
          return res.json();
        }
        throw new Error("Failed to fetch data");
      })
      .catch((error) => {
        console.log(error);
      });
  }
