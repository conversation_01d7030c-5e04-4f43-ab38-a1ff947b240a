SELECT
    s.[id],
    s.[is_active],
    s.[name],
    c.[symbol] As currency,
    c.[currency_id] As iss_currency_id,
    c.[name] As currency_name,
    c.[code] as currency_code,
    c.[iss_ledger_code] As iss_ledger_code,
    c.[internal_ledger_code] As internal_ledger_code,
    c.[bacs_currency_code],
    s.[requestor_name],
    s.[requestor_email],
    s.[vatable],
    s.[compliance],
    s.[financial],
    s.[technical],
    s.[procurement],
    s.[emergency_request],
    s.[gdpr_compliant],
    s.[product_supplier],
    s.[trading_name],
    s.[email_id],
    s.[facsimile],
    s.[telephone],
    s.[address_line_1],
    s.[address_line_2],
    s.[address_line_3],
    s.[address_line_4],
    s.[country],
    s.[postal_code],
    s.[vat_number],
    s.[company_registration],
    s.[country_code] AS country_code_id,
    ctr.[code] AS country_code,
    s.[payment_terms],
    pt.[code] as payment_type,
    pt.[name] as payment_type_name,
    s.[validated_procurement_team],
    s.[validated_date],
    s.[validated_by],
    s.[finance_authorization],
    s.[finance_authorization_date],
    s.[finance_authorization_by],
    s.[rejected_reason],
    s.[red_tractor],
    s.[puc_code],
    s.[chile_certificate_number],
    s.[organic_certificate_number],
    s.[global_gap_number],
    s.[customer_site_code],
    s.[created_date],
    s.[updated_date],
    s.[status],
    ss.[label],
    STUFF(
        (
            SELECT
                ', ' + spp_inner.[prophet_name]
            FROM
                [dbo].[supplier_prophets] sp_inner
                JOIN [dbo].[prophet_system] spp_inner ON sp_inner.prophet_id = spp_inner.id
            WHERE
                sp_inner.supplier_id = s.id FOR XML PATH(''),
                TYPE
        ).value('.', 'NVARCHAR(MAX)'),
        1,
        2,
        ''
    ) AS prophet_names,
    STUFF(
        (
            SELECT
                ', ' + srr_inner.[role_name]
            FROM
                [dbo].[supplier_roles] sr_inner
                JOIN [dbo].[supplier_role] srr_inner ON sr_inner.role_id = srr_inner.id
            WHERE
                sr_inner.supplier_id = s.id FOR XML PATH(''),
                TYPE
        ).value('.', 'NVARCHAR(MAX)'),
        1,
        2,
        ''
    ) AS role_names,
    STUFF(
        (
            SELECT
                ', ' + CAST(srr_inner.[role_num] AS VARCHAR(255)) -- Adjust the length based on your actual data
            FROM
                [dbo].[supplier_roles] sr_inner
                JOIN [dbo].[supplier_role] srr_inner ON sr_inner.role_id = srr_inner.id
            WHERE
                sr_inner.supplier_id = s.id FOR XML PATH(''),
                TYPE
        ).value('.', 'NVARCHAR(MAX)'),
        1,
        2,
        ''
    ) AS role_nums,
    (
        SELECT
            sr_inner.[role_id]
        FROM
            [dbo].[supplier_roles] sr_inner
        WHERE
            s.id = sr_inner.supplier_id FOR JSON PATH
    ) AS role_ids,
    (
        SELECT
            sr_inner.[role_id],
            sr_inner.[role_num],
            srr_inner.[supplier_type]
        FROM
            [dbo].[supplier_roles] sr_inner
            JOIN [dbo].[supplier_role] srr_inner ON sr_inner.role_id = srr_inner.id
        WHERE
            s.id = sr_inner.supplier_id FOR JSON PATH
    ) AS role_json,
    (
        SELECT
            sp_inner.[prophet_id],
            sp_inner.[prophet_code],
            (
                SELECT
                    COUNT(*)
                FROM
                    [dbo].[supplier_prophets] sp_all
                WHERE
                    sp_all.[prophet_code] = sp_inner.[prophet_code]
                    and in_prophet = 0
            ) + (
                SELECT
                    COUNT(*)
                FROM
                    [dbo].[supplier_iss] si_all
                WHERE
                    si_all.[supcode] = sp_inner.[prophet_code]
                    and prophet_id in (1, 3, 4, 5)
            ) AS code_count
        FROM
            [dbo].[supplier_prophets] sp_inner
        WHERE
            s.id = sp_inner.supplier_id FOR JSON PATH
    ) AS prophet_ids,
    (
        SELECT
            ssg.[supplier_id],
            (
                SELECT
                    s.[name]
                FROM
                    [dbo].[supplier] s
                WHERE
                    ssg.supplier_id = s.id
            ) AS name,
            srs.[role_id],
            STUFF(
                (
                    SELECT
                        ', ' + CAST(spr.role_name AS VARCHAR(100)) [text()]
                    FROM
                        [dbo].[supplier_role] spr
                    WHERE
                        spr.id = srs.role_id
                        and ssg.supplier_id = srs.supplier_id FOR XML PATH(''),
                        TYPE
                ).value('.', 'NVARCHAR(MAX)'),
                1,
                2,
                ' '
            ) role_names
        FROM
            [dbo].[supplier_sendac_group] ssg
            LEFT JOIN [dbo].[supplier_roles] srs ON ssg.supplier_id = srs.supplier_id
        WHERE
            ssg.group_id = (
                SELECT
                    distinct group_id
                FROM
                    [dbo].[supplier_sendac_group]
                WHERE
                    supplier_id = s.id
            ) FOR JSON PATH
    ) AS supplier_links_json,
    (
        SELECT
            spgs.[group_id],
            sg.[label],
            sg.[created_by],
            sg.[value]
        FROM
            [dbo].[supplier_sendac_group] spgs
            LEFT JOIN [dbo].[sendac_groups] sg ON sg.id = spgs.group_id
        WHERE
            s.id = spgs.supplier_id FOR JSON PATH
    ) AS sendac_groups_json,
    (
        SELECT
            ct.[id] as id,
            ct.[name],
            ct.[email_id],
            ct.[telephone],
            toc.[name] AS type_of_contact
        FROM
            [dbo].[supplier_contacts] ct
            INNER JOIN [dbo].[type_of_contacts] toc ON ct.type_of_contact = toc.id
        WHERE
            s.id = ct.supplier_id FOR JSON PATH
    ) AS contacts_json,
     (
        SELECT
            dpts.[id] as id,
            dpts.[from_dp],
            dpts.[name],
            sdp.[direct_dp],
            dpts.[prophet_id],
            sdp.[supplier_id]
        FROM
            [dbo].[distribution_points] dpts
            INNER JOIN [dbo].[supplier_distribution_point] sdp ON dpts.[id] = sdp.[distribution_point_id]
        WHERE
            s.id = sdp.supplier_id
            AND dpts.id = sdp.distribution_point_id
            AND sdp.[isActive] = 1 FOR JSON PATH
    ) AS distribution_points_json,
    ba.[sort_bic],
    ba.[name_branch],
    ba.[account_number],
    ba.[intermediatery_account_number],
    ba.[id] AS bank_account_id,
    ba.[has_iban],
    st.[supplier_type] AS supplier_type_label,
    s.[supplier_type] AS supplier_type_id,
    s.edi
FROM
    [dbo].[Supplier] s
    LEFT JOIN [dbo].[default_terms_agreed_yield] dtay ON s.id = dtay.supplier_id
    LEFT JOIN [dbo].[status] ss ON s.status = ss.id
    LEFT JOIN [dbo].[currencies] c ON c.id = s.currency
    LEFT JOIN [dbo].[bank_accounts] ba ON s.id = ba.supplier_id
    LEFT JOIN [dbo].[countries] ctr ON s.country = ctr.id
    LEFT JOIN [dbo].[payment_types] pt ON s.payment_type = pt.id
    LEFT JOIN [dbo].[supplier_type] st ON st.id = s.supplier_type
    LEFT JOIN [dbo].[supplier_prophets] sp ON s.id = sp.supplier_id
WHERE
    (
        (@company IN ('flr', 'thl', 'issproduce')
        OR s.company = @company) AND
        (@prophetId= 0 OR sp.prophet_id = @prophetId)
    )
GROUP BY
    s.[id],
    s.[name],
    s.[is_active],
    s.[currency],
    c.[currency_id],
    c.[name],
    c.[code],
    s.[compliance],
    s.[financial],
    s.[technical],
    s.[country_code],
    s.[procurement],
    s.[emergency_request],
    s.[gdpr_compliant],
    s.[product_supplier],
    s.[trading_name],
    s.[email_id],
    s.[facsimile],
    s.[telephone],
    s.[address_line_1],
    s.[address_line_2],
    s.[address_line_3],
    s.[address_line_4],
    s.[country],
    s.[postal_code],
    s.[vat_number],
    s.[company_registration],
    s.[country],
    ctr.[code],
    s.[payment_terms],
    pt.[code],
    pt.[name],
    s.[validated_procurement_team],
    s.[validated_date],
    s.[validated_by],
    s.[finance_authorization],
    s.[finance_authorization_date],
    s.[finance_authorization_by],
    s.[rejected_reason],
    s.[red_tractor],
    s.[puc_code],
    s.[chile_certificate_number],
    s.[organic_certificate_number],
    s.[global_gap_number],
    s.[customer_site_code],
    s.[created_date],
    s.[updated_date],
    s.[status],
    ba.[sort_bic],
    ba.[name_branch],
    ba.[account_number],
    ba.[intermediatery_account_number],
    ba.[id],
    ss.[label],
    c.[symbol],
    s.[vatable],
    s.[requestor_name],
    s.[requestor_email],
    c.[iss_ledger_code],
    c.[internal_ledger_code],
    c.[bacs_currency_code],
    ba.[has_iban],
    st.[supplier_type],
    s.[supplier_type],
    s.edi
ORDER BY
    s.id DESC;