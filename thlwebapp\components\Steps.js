import React, { useState, useEffect } from "react";

const Steps = ({ activeStep, uniqueAllowedSections, allowedSectionsInOrder }) => {
 
  const [filteredActiveStep, setFilteredActiveStep] = useState(1);

  useEffect(() => {
    const activeSectionIndex = uniqueAllowedSections?.findIndex(
      (section) => section === allowedSectionsInOrder[activeStep - 1]
    );
    setFilteredActiveStep(activeSectionIndex + 1);
  }, [activeStep, uniqueAllowedSections, allowedSectionsInOrder]);

  return (
    <div className="flex justify-center w-2/3 md:ml-[15%] ml-[18%] mt-0 mx-auto mb-4 z-50">
      {uniqueAllowedSections?.map((section, index) => (
        <div key={section} className="relative flex flex-row w-1/4 md:w-auto 2xl:w-1/5 items-center">
          <div className="flex">
            <span
              className={`font-poppinssemibold w-[30px] h-[30px] rounded-full text-center leading-[30px] text-white ${
                filteredActiveStep === index + 1
                  ? "bg-skin-primary border border-skin-primary"
                  : "bg-gray-200"
              } cursor-default`}
            >
              {index + 1}
            </span>
          </div>
          <p
            className={`relative ${
              filteredActiveStep === index + 1 ? "text-skin-primary" : "text-light-gray2"
            } text-lg px-5 font-poppinssemibold`}
          >
            {section}
          </p>
        </div>
      ))}
    </div>
  );
};

export default Steps;
