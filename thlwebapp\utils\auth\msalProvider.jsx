// components/MsalProvider.tsx
import { PublicClientApplication, EventType, ClientConfigurationError } from "@azure/msal-browser";
import { Msal<PERSON>rovider } from "@azure/msal-react";
import { msalConfig } from "./authConfig";
import React from "react";

const MsalAuthProvider = ({ children }) => {
  const msalInstance = new PublicClientApplication(msalConfig);

  const handlePopup = (event) => {
    if (event instanceof Event && event.isTrusted) {
      msalInstance.handlePopupPromise().catch((error) => {
        console.error("Error handling popup:", error);
      });
    }
  };

  React.useEffect(() => {
    window.addEventListener(EventType.LOGIN_SUCCESS, handlePopup);
    window.addEventListener(EventType.ACQUIRE_TOKEN_SUCCESS, handlePopup);
    return () => {
      window.removeEventListener(EventType.LOGIN_SUCCESS, handlePopup);
      window.removeEventListener(EventType.ACQUIRE_TOKEN_SUCCESS, handlePopup);
    };
  }, []);

  return <MsalProvider instance={msalInstance}>{children}</MsalProvider>;
};

export default MsalAuthProvider;
