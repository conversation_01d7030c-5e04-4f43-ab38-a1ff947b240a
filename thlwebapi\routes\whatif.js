"use strict";

const express = require("express");
const whatIfController = require("../controllers/whatif");
const router = express.Router();

const {
  getWhatifByCustomer,
  getWhatifTotalsByCustomer,
  getWhatifProductByCustomer,
  getWhatifByQuarter,
  getDefaultCalendarData,
  getstdCalendarData,
  getWhatifCustomers,
  getWhatifProducts,
  addWhatif,
  removeWhatif,
  getTaskTypes,
  removeLocks,
  addLock,
  getBreakevenStatuses,
  getCurrentQuarterCalendarData,
  getCurrentQuarterStdCalendarData,
  getIntialData,
  getNextQuarterProductDataByWeek,
  getIntialCustomerData,
  // exportWhatIfData,
  getRequiredFilteredData,
} = whatIfController;

const { validateSession } = require("../middleware/sessionAuth");

router.use(validateSession);

router.get("/get-whatif-by-customer/:cust_code", getWhatifByCustomer);
router.get(
  "/get-whatif-totals-by-customer/:cust_code",
  getWhatifTotalsByCustomer
);
router.get(
  "/get-whatif-product-by-customer/:cust_code",
  getWhatifProductByCustomer
);
router.get("/get-whatif-by-quarter/:qrt_number", getWhatifByQuarter);
router.get("/get-current-quarter-calendar-data", getCurrentQuarterCalendarData);
router.get(
  "/get-current-quarter-stdcalendar-data",
  getCurrentQuarterStdCalendarData
);
router.get("/get-default-calendar-data", getDefaultCalendarData);
router.get("/get-stdcalendar-data", getstdCalendarData);
router.get("/get-customers", getWhatifCustomers);
router.get("/get-products", getWhatifProducts);
router.post("/add-whatif", addWhatif);
router.post("/remove-whatif", removeWhatif);
router.get("/get-breakeven-statuses", getBreakevenStatuses);
router.get("/get-task-types", getTaskTypes);
router.post("/remove-locks", removeLocks);
router.post("/add-lock", addLock);
router.get("/get-initial-data/:cust_code", getIntialData);
router.get(
  "/get-next-quarter-product-data-by-week",
  getNextQuarterProductDataByWeek
);
router.get("/get-initial-customer-data/:cust_code", getIntialCustomerData);
// router.post("/export-what-if-customer-data/:cust_code", exportWhatIfData);
router.get("/get-required-filtered-data/:cust_code", getRequiredFilteredData);

module.exports =router;
