DECLARE @CurrentDate DATE = GETDATE();
DECLARE @QuarterList TABLE (FiscalQuarter INT);
DECLARE @nextWeek INT = @WeekNO;

DECLARE @nextWeekQuarter INT;

SELECT
    @nextWeekQuarter = fiscalquarter
FROM
    FLR_DEV_TEST_off_BI_lookup.dbo.off_cal_start_end_week
WHERE
    calendar_name = 'OFF Financial Calendar'
    AND fiscalyear = YEAR(@CurrentDate)
    AND fiscalweek = @nextWeek;

SELECT
    [fiscalyear],
    [fiscalquarter],
    [fiscalweek],
    FORMAT(startweek, 'yyyy-MM-dd') AS startweek,
    FORMAT(endweek, 'yyyy-MM-dd') AS endweek
   
FROM
    FLR_DEV_TEST_off_BI_lookup.dbo.off_cal_start_end_week
WHERE
    calendar_name = 'OFF Financial Calendar'
    AND fiscalyear = YEAR(@CurrentDate)
    AND fiscalquarter = @nextWeekQuarter
ORDER BY
    startweek;
