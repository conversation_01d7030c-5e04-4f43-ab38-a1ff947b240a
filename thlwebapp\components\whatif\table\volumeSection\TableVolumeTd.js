import React, { useRef } from "react";
import { GRID_VOLUME } from "@/utils/whatif/utils/constants";
import PromoPopover from "../../PromoPopover";
import { toast } from "react-toastify";

const TableVolumeTd = ({
  quarterData,
  row,
  previousSunday,
  currentWeek,
  calendarWeeks,
  setModal,
  currency,
  isLockedBy,
  fiscalyear,
  currentFiscalYear,
}) => {
  const positioningRef = useRef(null);

  return (
    <>
      {calendarWeeks.map((weekStartDate, weekIndex) => {
        const productData = quarterData.find(
          (data) => data.startWeek === weekStartDate
        );
        const index = quarterData.findIndex(
          (data) => data.startWeek === weekStartDate
        );
        if (!productData) return;
        let nextWeekHasData = 0;
        if (index !== -1 && index + 1 < quarterData.length) {
          nextWeekHasData = quarterData[index + 1].hasData || 0;
        }
        productData.nextWeekHasData = nextWeekHasData;
        const isToday =
          weekStartDate === previousSunday &&
          row.fiscalyear == currentFiscalYear;
        const currentDate = new Date(productData.startWeek);
        const currentMonth = currentDate.getMonth() + 1;
        const startWeekYear = currentDate.getFullYear();

        const financialYear =
          currentMonth >= 10 ? startWeekYear + 1 : startWeekYear;

        const promotions = row?.promotions?.filter((data) => {
          //TODO:need to add this in all the grids like volume
          if (data?.promo_end_week_no >= data?.promo_start_week_no) {
            return (
              productData?.week >= data?.promo_start_week_no && //start week 51 and end week 1 current week is 52, 1
              productData?.week <= data?.promo_end_week_no &&
              financialYear === data?.promo_fiscalYear
            );
          } else {
            return (
              (productData?.week >= data?.promo_start_week_no &&
                financialYear === data?.promo_fiscalYear) ||
              (productData?.week <= data?.promo_end_week_no &&
                financialYear === data?.promo_fiscalYear + 1)
            );
          }
        });
        const valueToDisplay = (() => {
          if (promotions && promotions.length > 0) {
            for (const promo of promotions) {
              const promoWeekData = promo.weekData[productData?.week]?.current;
              if (
                (promoWeekData?.volume != null &&
                  productData?.week >= currentWeek) ||
                (promoWeekData?.volume != null &&
                  productData?.week == currentWeek - 1 &&
                  new Date().getDay() === 0)
              ) {
                return { data: promoWeekData.volume, isPromo: true };
              }
            }
          }
          return {
            data:
              fiscalyear != productData.fiscalYear &&
              productData.NoDataNextFY_Q1 == 1
                ? ""
                : productData?.data[0]?.value ?? 0,
            isPromo: false,
          };
        })();

        const promoDataColor = (() => {
          let whatifId;
          if (promotions && promotions.length > 0) {
            for (const promotion of promotions) {
              if (
                productData.week >= promotion.promo_start_week_no &&
                productData?.week <= promotion?.promo_end_week_no
              ) {
                whatifId = promotion?.what_if_id;
              }
              if (promotion.type_id == 1) {
                if (promotion.is_confirmed) {
                  return { color: "confirm-status", whatifId: "" };
                } else {
                  return { color: "wip-status", whatifId: "" };
                }
              } else if (promotion.type_id == 3) {
                return { color: "issue-status", whatifId: "" };
              } else if (promotion.type_id == 4) {
                return { color: "volumechange-status", whatifId: "" };
              }
            }
          }

          return {
            color:
              fiscalyear != productData.fiscalYear &&
              productData.NoDataNextFY_Q1 == 1
                ? "no-productsbg"
                : productData.hasData == 0
                ? "no-productsbg"
                : "white",
            whatifId: whatifId,
          };
        })();
        const popoverData = (() => {
          let promoArr = [];
          if (promotions && promotions.length > 0) {
            for (const promotion of promotions) {
              if (
                promotion.type_id == 1 ||
                promotion.type_id == 3 ||
                promotion.type_id == 4
              ) {
                promoArr.push(promotion);
                promoArr.push(productData);
                return promoArr;
              }
            }
          }
          return [];
        })();

        return (
          <td
            title={
              fiscalyear != productData.fiscalYear &&
              productData.NoDataNextFY_Q1 == 1
                ? "Data does not exist for this product"
                : ""
            }
            ref={positioningRef}
            key={weekIndex}
            onDoubleClick={() => {
              if (!!isLockedBy) {
                toast.info(
                  `You cannot edit the product while someone is already working on it.`,
                  {
                    theme: "colored",
                    autoClose: 5000,
                  }
                );
                return;
              } else if (productData.hasData == 0) {
                toast.info(
                  `You can't add a promo as the current week doesn't have data.`,
                  {
                    theme: "colored",
                    autoClose: 5000,
                  }
                );
                return;
              }
              if (
                fiscalyear != productData.fiscalYear &&
                productData.NoDataNextFY_Q1 == 1
              ) {
                toast.info(
                  `You cannot edit the product as it does not exist in this fiscal year.`,
                  {
                    theme: "colored",
                    autoClose: 5000,
                  }
                );
                return;
              }
              setModal(
                GRID_VOLUME,
                true,
                row.pkey,
                productData,
                promoDataColor.whatifId
              );
            }}
            className={`${isToday ? "bg-currentWeek" : ""} ${
              !!isLockedBy &&
              (promoDataColor.color === "white" ||
                promoDataColor.color === "no-productsbg")
                ? "bg-locked-products"
                : promoDataColor.color === "white" ||
                  promoDataColor.color === "no-productsbg"
                ? `bg-${promoDataColor.color}`
                : `!bg-${promoDataColor.color}`
            } !text-center ${
              !!isLockedBy ? "" : "hover:cursor-pointer"
            } relative`}
          >
            <span>{productData.hasData == 0 ? "" : valueToDisplay.data}</span>

            <div
              className={`${
                promoDataColor.color != "white" &&
                promoDataColor.color != "no-productsbg"
                  ? "absolute"
                  : "hidden"
              } top-0 right-1 z-[5]`}
              onClick={(e) => e.stopPropagation()}
            >
              <PromoPopover
                positioningRef={positioningRef}
                popoverData={popoverData}
                currency={currency}
              />
            </div>
          </td>
        );
      })}
    </>
  );
};

export default TableVolumeTd;
