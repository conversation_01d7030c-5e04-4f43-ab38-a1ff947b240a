SELECT
    s.[id],
    c.[currency_id] As iss_currency_id,
    c.[name] As currency_name,
    c.[code] as currency_code,
    c.[iss_ledger_code] As iss_ledger_code,
    c.[internal_ledger_code] As internal_ledger_code,
    c.[bacs_currency_code],
    s.[requestor_email],
    s.[vatable],
    s.[trading_name],
    s.[email_id],
    s.[facsimile],
    s.[telephone],
    s.[address_line_1],
    s.[address_line_2],
    s.[address_line_3],
    s.[address_line_4],
    s.[country],
    s.[postal_code],
    s.[vat_number],
    s.[company_registration],
    s.[country_code] AS country_code_id,
    ctr.[code] AS country_code,
    s.[payment_terms],
    pt.[code] as payment_type,
    pt.[name] as payment_type_name,
    s.[validated_date],
    s.[validated_by],
    s.[finance_authorization],
    s.[finance_authorization_date],
    s.[finance_authorization_by],
    s.[rejected_reason],
    s.[red_tractor],
    s.[puc_code],
    s.[chile_certificate_number],
    s.[organic_certificate_number],
    s.[global_gap_number],
    s.[customer_site_code],
    s.[created_date],
    s.[updated_date],
    s.[status],
    STUFF(
        (
            SELECT
                ', ' + CAST(srr_inner.[role_num] AS VARCHAR(255)) -- Adjust the length based on your actual data
            FROM
                [dbo].[supplier_roles] sr_inner
                JOIN [dbo].[supplier_role] srr_inner ON sr_inner.role_id = srr_inner.id
            WHERE
                sr_inner.supplier_id = s.id FOR XML PATH(''),
                TYPE
        ).value('.', 'NVARCHAR(MAX)'),
        1,
        2,
        ''
    ) AS role_nums,
    (
        SELECT
            sr_inner.[role_id],
            sr_inner.[role_num],
            srr_inner.[supplier_type]
        FROM
            [dbo].[supplier_roles] sr_inner
            JOIN [dbo].[supplier_role] srr_inner ON sr_inner.role_id = srr_inner.id
        WHERE
            s.id = sr_inner.supplier_id FOR JSON PATH
    ) AS role_json,
    (
        SELECT
            ct.[id] as id,
            ct.[name],
            ct.[email_id],
            ct.[telephone],
            toc.[name] AS type_of_contact
        FROM
            [dbo].[supplier_contacts] ct
            INNER JOIN [dbo].[type_of_contacts] toc ON ct.type_of_contact = toc.id
        WHERE
            s.id = ct.supplier_id FOR JSON PATH
    ) AS contacts_json,
    (
        SELECT
            dpts.[id] as id,
            dpts.[from_dp],
            dpts.[name],
            sdp.[direct_dp],
            dpts.[prophet_id],
            sdp.[supplier_id]
        FROM
            [dbo].[distribution_points] dpts
            INNER JOIN [dbo].[supplier_distribution_point] sdp ON dpts.[id] = sdp.[distribution_point_id]
        WHERE
            s.id = sdp.supplier_id
            AND dpts.id = sdp.distribution_point_id
            AND sdp.[isActive] = 1 FOR JSON PATH
    ) AS distribution_points_json,
    ba.[sort_bic],
    ba.[name_branch],
    ba.[account_number],
    ba.[intermediatery_account_number],
    ba.[id] AS bank_account_id,
    ba.[has_iban]
FROM
    [dbo].[Supplier] s
    LEFT JOIN [dbo].[default_terms_agreed_yield] dtay ON s.id = dtay.supplier_id
    LEFT JOIN [dbo].[status] ss ON s.status = ss.id
    LEFT JOIN [dbo].[currencies] c ON c.id = s.currency
    LEFT JOIN [dbo].[bank_accounts] ba ON s.id = ba.supplier_id
    LEFT JOIN [dbo].[countries] ctr ON s.country = ctr.id
    LEFT JOIN [dbo].[payment_types] pt ON s.payment_type = pt.id
    where s.id = @id
GROUP BY
    s.id,
    c.[currency_id],
    c.[name],
    c.[code],
    s.[requestor_email],
    s.[trading_name],
    s.[email_id],
    s.[facsimile],
    s.[telephone],
    s.[address_line_1],
    s.[address_line_2],
    s.[address_line_3],
    s.[address_line_4],
    s.[country],
    s.[postal_code],
    s.[vat_number],
    s.[country_code],
    s.[company_registration],
    s.[country],
    ctr.[code],
    s.[payment_terms],
    pt.[code],
    pt.[name],
    s.[validated_date],
    s.[validated_by],
    s.[finance_authorization],
    s.[finance_authorization_date],
    s.[finance_authorization_by],
    s.[rejected_reason],
    s.[red_tractor],
    s.[puc_code],
    s.[chile_certificate_number],
    s.[organic_certificate_number],
    s.[global_gap_number],
    s.[customer_site_code],
    s.[created_date],
    s.[updated_date],
    ba.[sort_bic],
    ba.[name_branch],
    ba.[account_number],
    ba.[intermediatery_account_number],
    ba.[id],
    ss.[label],
    c.[symbol],
    s.[vatable],
    s.[requestor_name],
    c.[iss_ledger_code],
    c.[internal_ledger_code],
    c.[bacs_currency_code],
    s.[status],
    ba.[has_iban]
ORDER BY
    s.id DESC;