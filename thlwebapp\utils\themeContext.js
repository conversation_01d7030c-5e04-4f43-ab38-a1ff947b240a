import Cookies from "js-cookie";
import { createContext, useContext, useState } from "react";
 
const ThemeContext = createContext();
 
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
 
export const ThemeProvider = ({ children }) => {
  const theme = Cookies.get('theme') || '"#022D71"'; // Default theme
  
  let cookieTheme = theme?.replace(/"/g, "") || "#022D71";

  const [themeColor, setThemeColor] = useState(cookieTheme);
  
  return (
    <ThemeContext.Provider value={{ themeColor: themeColor || "#022D71", setThemeColor }}>
      {children}
    </ThemeContext.Provider>
  );
};