"use strict";
const express=require("express");
const ppeConsumablesController=require("../controllers/ppeConsumables");
const ppeConsumableData = require("../data/products");

const router=express.Router();


const {
    addProduct,
    allProducts,
    allPpeRequests
} = ppeConsumablesController

// const { validateSession } = require("../middleware/sessionAuth");
// router.use(validateSession);

// get
router.get("/all-ppe-requests",allPpeRequests)
router.get("/all-products",allProducts)
router.post("/add-product", addProduct);

// post

//update


module.exports=router;