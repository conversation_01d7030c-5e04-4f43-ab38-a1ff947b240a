UPDATE [dbo].[products]
SET
  [brand] = @brand,
  [product_type] = @product_type,
  [reason] = @reason,
  [originator] = @originator,
  [product_description] = @product_description,
  [suppliers_description] = @suppliers_description,
  [product_code] = @product_code,
  [group_id] = @group_id,
  [mark_variaty] = @mark_variaty,
  [count_or_size] = @count_or_size,
  [units_in_outer] = @units_in_outer,
  [cases_per_pallet] = @cases_per_pallet,
  [outer_net_weight] = @outer_net_weight,
  [outer_gross_weight] = @outer_gross_weight,
  [sub_product_code] = @sub_product_code,
  [temperature_grade] = @temperature_grade,
  [class_required] = @class_required,
  [intrastat_commodity_code] = @intrastat_commodity_code,
  [organic_certificate] = @organic_certificate,
  [is_classified_allergic_fsa14] = @is_classified_allergic_fsa14,
  [coo] = @coo,
  [caliber_size] = @caliber_size,
  [end_customer] = @end_customer,
  [variety] = @variety,
  [delivery_date]=@delivery_date,
  [update_date]=GETDATE(),
  [submitted_to_iss]=@submitted_to_iss,
  [status] = @status,
  [email_comment] = @email_comment
WHERE [id] = @id;