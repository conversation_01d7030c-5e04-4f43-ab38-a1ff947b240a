import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import Filter from "./Filter";
import { ModalContext } from "./providers/ModalProvider";
import WhatifTable from "./table/WhatifTable";
import { ThreeCircles } from "react-loader-spinner";
import { ToastContainer } from "react-toastify";
import Image from "next/image";
import NodataImg from "../../public/images/nodatafound.png";
import io from "socket.io-client";
import { apiConfig } from "@/services/apiConfig";
import {
  addLockByPkey,
  clearIDB,
  filtersBasedOnZeros,
  removeLocksByUser,
  replaceCustomerTotalsWithTheNew,
  replaceDataWithTheNew,
  replaceEfcCalenderWithTheNew,
  replaceStdCalenderWithTheNew,
} from "@/utils/whatif/utils/indexedDB";
import { getData } from "@/utils/whatif/utils/getProductData";
import getDistinctBusinessUnitsAndMasterCodes from "@/utils/whatif/utils/getDistinctBusinessUnitsAndMasterCodes";
import Cookies from "js-cookie";
import { useLoading } from "@/utils/loaders/loadingContext";

const WhatifLayout = ({
  customerList,
  userData,
  calenderData,
  customerDataTotals,
  currentQuarterStdCalendarData
}) => {
  let filters = Cookies.get("filters");
  filters = filters ? JSON.parse(filters) : null;
  const { setIsLoading } = useLoading();

  const {
    ctxCalenderData,
    setCurrentData,
    setCurrentTotal,
    currentTotal,
    currentData,
    setCtxCalenderData,
    setCurrentCustomer,
    currentCustomer,
    ctxStdCalenderData,
    setCtxStdCalenderData,
    setModal,
    currentYear,
    setCurrentYear
  } = useContext(ModalContext);
  const [checkboxes, setCheckboxes] = useState({
    all: false,
    q1: false,
    q2: false,
    q3: false,
    q4: false,
  });
  const [initialLoading, setInitialLoading] = useState(true);
  const [loading, setLoading] = useState(true);
  const [noOfWeeks, setNoOfWeeks] = useState(13);
  const [businessUnits, setBusinessUnits] = useState([]);
  const [masterCodes, setMasterCodes] = useState([]);
  const [selectWhereTotalIsZero, setSelectWhereTotalIsZero] = useState(
    filters ? filters.totalZero : false
  );
  const [selectWhereTotalIsNotZero, setSelectWhereTotalIsNotZero] = useState(
    filters ? filters.totalNotZero : true
  );
  const [productList, setProductList] = useState([]);

  // State for hiding/showing row and columns
  const [checkedStates, setCheckedStates] = useState(
    filters
      ? filters.columns
      : {
          weeks: {
            EFCWeek: true,
            calenderWeek: false
          },
          columns: {
            customerCode: false,
            masterProduct: false,
            businessUnit: false,
            altFillID: false,
            skuDescription: true,
            caseSize: true,
          },
        }
  );

  const [gridStates, setGridStates] = useState(
    filters
      ? filters.grids
      : {
          volume: true,
          breakeven: true,
          unitprice: true,
          value: false,
          grossprofit: false,
          gppercent: false,
        }
  );

  const [isQuaterlyTotalsOpen, setIsQuaterlyTotalsOpen] = useState(false);
  const [isTableRendered, setIsTableRendered] = useState(false);
  

  //Set initial data
  useEffect(() => {
    setIsLoading(false);
    const fetchData = async () => {
      const curCustomer = filters ? filters.customer : customerList[0]?.value;
      const selectWhereTotalIs0 = filters ? filters.totalZero : false;
      const selectWhereTotalIsNot0 = filters ? filters.totalNotZero : true;

      setCurrentCustomer(curCustomer);
      setSelectWhereTotalIsZero(selectWhereTotalIs0);
      setSelectWhereTotalIsNotZero(selectWhereTotalIsNot0);

      const json = await getData(
        `get-initial-customer-data/${curCustomer}`,
      );

      if (!json) return;

      const dataWithoutLast = json.slice(0, -1);

      const formattedProductsList = dataWithoutLast?.map((prod, index) => {
        return {
          label: `${prod.product_desc} (${prod.prodnum})`,
          prodName: prod.product_desc,
          value: prod.prodnum,
        };
      });
      setProductList(formattedProductsList);
      const selectedQuarters = [`q${calenderData[0]?.currentQuarter}`];

      let tableDataFiltered = filters
        ? dataWithoutLast.filter((p) => {
            const buCondition =
              filters.bu === "all" || p.Business_unit === filters.bu;
            const mcCondition =
              filters.mc === "all" || p.Master_code === filters.mc;
            const selectedProdCondition = (!filters.selectedProducts || (!!filters.selectedProducts && filters.selectedProducts.includes(p.pkey))) ? true: false 
          
            return buCondition && mcCondition && selectedProdCondition;
          })
        : dataWithoutLast;

      if (!selectWhereTotalIs0 || !selectWhereTotalIsNot0) {
        tableDataFiltered = filtersBasedOnZeros(
          tableDataFiltered,
          selectWhereTotalIs0,
          selectWhereTotalIsNot0,
          selectedQuarters
        );
      }

      setCtxCalenderData(calenderData);
      setCtxStdCalenderData(currentQuarterStdCalendarData);
      setCurrentData(tableDataFiltered);
      setCurrentTotal(customerDataTotals);

      const result = await getDistinctBusinessUnitsAndMasterCodes(
        dataWithoutLast
      );
      setBusinessUnits(result.businessUnits);
      setMasterCodes(result.masterCodes);
      replaceDataWithTheNew(dataWithoutLast);
      replaceCustomerTotalsWithTheNew(customerDataTotals);
      replaceEfcCalenderWithTheNew(calenderData);
      replaceStdCalenderWithTheNew(currentQuarterStdCalendarData);
      setLoading(false);
      setInitialLoading(false);
    };

    fetchData();

    return () => {
      clearIDB();
    };
  }, []);

  useEffect(() => {
    const socket = io(`${apiConfig.socketAddress}`);

    socket.on("connect", () => {
      console.log("Connected to server");
    });

    socket.on("lockAdded", (msg) => {
      setCurrentData((prev) => {
        return prev.map((item) => {
          if (item.pkey === msg.pkey) {
            return { ...item, isLockedBy: msg.name };
          } else if (item.isLockedBy === msg.name) {
            return { ...item, isLockedBy: "" };
          }
          return item;
        });
      });

      //update lockedBy in IDB
      addLockByPkey(msg.pkey, msg.name);
    });

    socket.on("locksRemoved", (msg) => {
      setCurrentData((prev) => {
        return prev.map((item) => {
          if (item.isLockedBy === msg.name) {
            return { ...item, isLockedBy: null };
          }
          return item;
        });
      });

      //update lockedBy in IDB
      removeLocksByUser(msg.name);
    });

    // Cleanup function to disconnect the socket when the component unmounts
    return () => {
      socket.disconnect();
    };
  }, []);

  const toggleListHandler = useCallback(() => {
    setIsQuaterlyTotalsOpen(!isQuaterlyTotalsOpen);
  }, [isQuaterlyTotalsOpen]);

  // finding height of thead

  const [rowRefs] = useState({
    theadRef: useRef(null),
    forecastRef: useRef(null),
    filterRef: useRef(null),
  });

  const [rowHeights, setRowHeights] = useState({
    theadHeight: 0,
    forecastHeight: 0,
    filterHeight: 0,
  });

  useEffect(() => {
    const theadHeight = rowRefs.theadRef.current
      ? rowRefs.theadRef.current.offsetHeight
      : 0;

    const forecastHeight = rowRefs.forecastRef.current
      ? rowRefs.theadRef.current.offsetHeight +
        rowRefs.forecastRef.current.offsetHeight
      : 0;

    const filterHeight = rowRefs.filterRef.current
      ? rowRefs.filterRef.current.offsetHeight
      : 0;

    setRowHeights({
      theadHeight,
      forecastHeight,
      filterHeight,
    });
  }, [checkedStates, isQuaterlyTotalsOpen, isTableRendered]);

  // finding width of first seven columns
  const [columnRefs] = useState({
    customerCode: useRef(null),
    masterCode: useRef(null),
    businessUnit: useRef(null),
    altfillID: useRef(null),
    SKUDescription: useRef(null),
    caseSize: useRef(null),
    totals: useRef(null),
  });
  const memoizedColumnRefs = useMemo(() => columnRefs, [columnRefs]);

  const [columnWidths, setColumnWidths] = useState({
    customerCode: 0,
    masterCode: 0,
    businessUnit: 0,
    altfillID: 0,
    SKUDescription: 0,
    caseSize: 0,
    totals: 0,
  });

  useEffect(
    () => {
      const curstomerCodeWidth = columnRefs.customerCode.current
        ? columnRefs.customerCode.current.offsetWidth
        : 0;

      const masterCodeWidth = columnRefs.masterCode.current
        ? columnRefs.masterCode.current.offsetWidth + curstomerCodeWidth
        : curstomerCodeWidth;

      const businessUnitWidth = columnRefs.businessUnit.current
        ? columnRefs.businessUnit.current.offsetWidth + masterCodeWidth
        : masterCodeWidth;

      const altfillIDWidth = columnRefs.altfillID.current
        ? columnRefs.altfillID.current.offsetWidth + businessUnitWidth
        : businessUnitWidth;

      const SKUDescriptionWidth = columnRefs.SKUDescription.current
        ? columnRefs.SKUDescription.current.offsetWidth + altfillIDWidth
        : altfillIDWidth;

      const caseSizeWidth = columnRefs.caseSize.current
        ? columnRefs.caseSize.current.offsetWidth + SKUDescriptionWidth
        : SKUDescriptionWidth;

      const totalsWidth = columnRefs.totals.current
        ? columnRefs.totals.current.offsetWidth + caseSizeWidth
        : caseSizeWidth;

      setColumnWidths({
        customerCode: curstomerCodeWidth,
        masterCode: masterCodeWidth,
        businessUnit: businessUnitWidth,
        altfillID: altfillIDWidth,
        SKUDescription: SKUDescriptionWidth,
        caseSize: caseSizeWidth,
        totals: totalsWidth,
      });
    },
    [checkedStates, isTableRendered,columnRefs]
    
  );

  let calendarWeeks = useCallback(
    ctxCalenderData?.map((ele) => ele?.startweek),
    [ctxCalenderData]
  );
  return (
    <>
      {initialLoading && (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "100vh",
          }}
        >
          <ThreeCircles
            color="#002D73"
            height={50}
            width={50}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      )}
      {!initialLoading && (
        <div ref={rowRefs.filterRef}>
          <Filter
            checkedStates={checkedStates}
            setCheckedStates={setCheckedStates}
            toggleQuaterlyTotals={toggleListHandler}
            currentData={currentData}
            setCurrentData={setCurrentData}
            setCurrentTotal={setCurrentTotal}
            userData={userData}
            loading={loading}
            setLoading={setLoading}
            setNoOfWeeks={setNoOfWeeks}
            customerList={customerList}
            productList={productList}
            businessUnits={businessUnits}
            masterCodes={masterCodes}
            currentCustomer={currentCustomer}
            setCurrentCustomer={setCurrentCustomer}
            setCtxCalenderData={setCtxCalenderData}
            setCtxStdCalenderData={setCtxStdCalenderData}
            ctxStdCalenderData={ctxStdCalenderData}
            ctxCalenderData={ctxCalenderData}
            setBusinessUnits={setBusinessUnits}
            setMasterCodes={setMasterCodes}
            gridStates={gridStates}
            setGridStates={setGridStates}
            checkboxes={checkboxes}
            setCheckboxes={setCheckboxes}
            selectWhereTotalIsZero={selectWhereTotalIsZero}
            setSelectWhereTotalIsZero={setSelectWhereTotalIsZero}
            selectWhereTotalIsNotZero={selectWhereTotalIsNotZero}
            setSelectWhereTotalIsNotZero={setSelectWhereTotalIsNotZero}
            currentYear={currentYear}
            setCurrentYear={setCurrentYear}
          />
        </div>
      )}
      {loading || initialLoading ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "100vh",
          }}
        >
          <ThreeCircles
            color="#002D73"
            height={50}
            width={50}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      ) : (
        <>
          <ToastContainer className="!z-[9999999]" />

          {currentData?.length == 0 ? (
            <div className="w-[45%] mx-auto nodata rounded-lg my-32 flex flex-row justify-between p-8 gap-8 ">
              <div className="">
                <Image
                  src={NodataImg}
                  className=""
                  width={400}
                  alt="No data found"
                />
              </div>
              <div className="flex flex-col items-center text-2xl font-bold justify-center">
                We couldn't find any matching results for your search. Please
                try refining your search
              </div>
            </div>
          ) : (
            <WhatifTable
              setCtxStdCalenderData={setCtxStdCalenderData}
              ctxStdCalenderData={ctxStdCalenderData}
              checkedStates={checkedStates}
              isQuaterlyTotalsOpen={isQuaterlyTotalsOpen}
              columnWidths={columnWidths}
              rowHeights={rowHeights}
              columnRefs={memoizedColumnRefs}
              rowRefs={rowRefs}
              noOfWeeks={noOfWeeks}
              calendarWeeks={calendarWeeks}
              setIsTableRendered={setIsTableRendered}
              customerDataTotals={currentTotal}
              currentData={currentData}
              ctxCalenderData={ctxCalenderData}
              setModal={setModal}
              gridStates={gridStates}
              checkboxes={checkboxes}
              currentYear={currentYear}
            />
          )}
        </>
      )}
    </>
  );
};

export default WhatifLayout;
