-- 3. GET ONE PRODUCT DETAILS
-- @ProductID is the parameter.

SELECT
    p.[id] AS ProductID,
    p.[name] AS ProductName,
    p.[product_code] AS ProductCode,
    p.[type_id] AS TypeID,
    t.[name] AS TypeName,
    p.[size_required] AS IsSizeRequired,
    p.[size_label] AS SizeLabel,
    p.[quantity] AS PackageQuantity,
    p.[comments] AS Comments,
    p.[is_active] AS IsActive,
    -- Create a comma-separated list of available size IDs (e.g., "1,3,5")
    (SELECT STRING_AGG(size_id, ',') 
     FROM [dbo].[product_available_sizes] 
     WHERE [product_id] = p.[id] AND [is_active] = 1) AS AvailableSizeIDs,
    -- Create a comma-separated list of available size labels (e.g., "Small, Medium, Large")
    (SELECT STRING_AGG(ps.size_label, ', ') 
     FROM [dbo].[product_available_sizes] pas
     INNER JOIN [dbo].[product_sizes] ps ON pas.size_id = ps.id
     WHERE pas.[product_id] = p.[id] AND pas.[is_active] = 1) AS AvailableSizeLabels
FROM
    [dbo].[products] p
    LEFT JOIN [dbo].[type_of_product] t ON p.[type_id] = t.[id]
WHERE
    p.[id] = @ProductID;