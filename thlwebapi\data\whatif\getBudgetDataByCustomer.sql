DECLARE @CurrentDate DATE;

DECLARE @CurrentFinancialYearFirstDate CHAR(8);

DECLARE @NextFinancialYearFirstDate CHAR(8);

DECLARE @CurrentFinancialYear INT;

SET
    @CurrentFinancialYear = CASE
        WHEN MONTH(GETDATE()) >= 10 THEN YEAR(GETDATE()) + 1
        ELSE YEAR(GETDATE())
    END;

SET
    @CurrentDate = CASE
        WHEN @financialYear = @CurrentFinancialYear THEN GETDATE() -- Use current date if year is current
        ELSE CONVERT(
            DATE,
            CAST(@financialYear - 1 AS CHAR(4)) + '1001',
            112
        ) -- 1 Oct of the input year if it's a past year
    END;

-- Calculate the first date of the current financial year
SET
    @CurrentFinancialYearFirstDate = CASE
        WHEN MONTH(@CurrentDate) >= 10 THEN CONVERT(
            CHAR(8),
            CAST(YEAR(@CurrentDate) AS CHAR(4)) + '1001',
            112
        )
        ELSE CONVERT(
            CHAR(8),
            CAST(YEAR(@CurrentDate) - 1 AS CHAR(4)) + '1001',
            112
        )
    END;

-- Calculate the first date of the next financial year
SET
    @NextFinancialYearFirstDate = CASE
        WHEN MONTH(@CurrentDate) >= 10 THEN CONVERT(
            CHAR(8),
            CAST(YEAR(@CurrentDate) + 1 AS CHAR(4)) + '1001',
            112
        )
        ELSE CONVERT(
            CHAR(8),
            CAST(YEAR(@CurrentDate) AS CHAR(4)) + '1001',
            112
        )
    END;
;with default_calendar AS (
    SELECT
        DISTINCT [calendar_name],
        [fiscalyear],
        [fiscalquarter],
        [fiscalweek],
        [startweek],
        [endweek]
    FROM
        [FLR_DEV_TEST_off_BI_lookup].[dbo].[off_cal_start_end_week]
    where
        calendar_name = 'off financial calendar'
        and startweek >= @CurrentFinancialYearFirstDate
        and [endweek] < (
            SELECT
                DISTINCT [fcastendate]
            FROM
                [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_fcastenddate]
        )
),

Budget_data AS(
    select
        blr.budget_date,blr.hocustcode,
        cast(
            case
                when blr.hocustcode in ('RIVERF', 'ABELON', 'AB&COL')
                and alt.weight_g > 1000 then budget_qty * alt.wgtouter
                else budget_qty
            end as decimal(18, 0)
        ) [Volume],
        cast(
            budget_qty * alt.countsize * price as decimal(18, 2)
        ) as [Value],
        cast(bw.budget_PROFIT as decimal(18, 2)) as [Gross Profit],
        cast(
            case
                when blr.hocustcode in ('RIVERF', 'ABELON', 'AB&COL')
                and alt.weight_g > 1000 then price / alt.wgtouter
                else price
            end as decimal(18, 2)
        ) [Unit Price],
        cast(budget_units as decimal(18, 0)) as Units,
        concat(
            alt.altfilid,
            '|',
            prodnum,
            '|',
            blr.hocustcode,
            '|',
            product_desc
        ) as pkey
    from
        [FLR_DEV_TEST_off_BI_lookup].[dbo].[budget_latest_revision] blr
        join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_altfil] alt on blr.altfilid = alt.altfilid
        join [FLR_DEV_TEST_off_BI_lookup].[dbo].[budget_dy_weekly_additional_totals] bw on blr.altfilid = bw.altfilid
        and blr.budget_date = bw.budget_date
        and blr.hocustcode = bw.hocust_code
    where
        blr.budget_date >= @CurrentFinancialYearFirstDate
        and blr.budget_date < @NextFinancialYearFirstDate
),
weekly_budget_data as (
    select
        bd.budget_date,bd.hocustcode,
        default_calendar.fiscalweek,default_calendar.fiscalquarter,default_calendar.fiscalyear,
        bd.Volume AS Weekly_Budget_Volume,
        bd.[Unit Price] AS Weekly_Avg_Budget_Unit_Price,
        bd.[Value] AS Weekly_Budget_Value,
        bd.[Gross Profit] AS Weekly_Budget_Gross_Profit,
        bd.Units AS Weekly_Budget_Units,
        bd.pkey
    from
        Budget_data bd
        left JOIN default_calendar ON bd.budget_date >= default_calendar.startweek
        AND bd.budget_date <= default_calendar.endweek
    GROUP BY
        fiscalweek,fiscalquarter,fiscalyear,bd.hocustcode,
        pkey,
        bd.Volume,
        bd.[Unit Price],
        bd.[Value],
        bd.[Gross Profit],
        bd.Units,
        bd.budget_date
)

select * from weekly_budget_data where hocustcode=@cust_code order by fiscalweek