SELECT
    p.[id],
    r.[id] AS reason_id,
    r.[reason] AS reason,
    p.[delivery_date],
    p.[originator],
    p.[type],
    p.[product_code],
    mp.[name] AS product_name,
    mp.[id] AS master_product_id,
    mp.[is_new] AS mp_is_new,
    mp.[code] AS master_product_code,
    pg.[group_name] AS group_name,
    pg.[is_new] AS group_is_new,
    pg.[group_id] As group_id,
    p.[product_description],
    p.[mark_variaty],
    mv.[id] AS mark_variety_id,
    mv.[name] AS mark_variety_name,
    mv.[is_new] AS mark_variety_is_new,
    p.[units_in_outer],
    p.[cases_per_pallet],
    p.[outer_net_weight],
    p.[outer_gross_weight],
    p.[sub_product_code],
    p.[temperature_grade],
    tg.[name] AS temperature_grade_name,
    tg.[id] as temperature_grade_id,
    p.[class_required],
    p.[organic_certificate],
    p.[is_classified_allergic_fsa14],
    p.[brand],
    b.[name] AS brand_name,
    b.[id] AS brand_id,
    b.[is_new] AS b_is_new,
    b.[code] AS brand_code,
    p.[is_other_date_code_type],
    p.[end_customer],
    ec.[name] AS end_customer_name,
    ec.[id] AS end_customer_id,
    ec.[is_new] AS end_customer_is_new,
    ec.[code] AS end_customer_code,
    p.[customer_description],
    p.[finished_pack_size],
    p.[is_box_type_colours],
    p.[box_type_other_comments],
    p.[packaging_types],
    p.[description_net_film],
    p.[punnet_tray_type],
    p.[machine_format],
    mf.[name] AS machine_format_name,
    mf.[id] AS machine_format_id,
    p.[pack_label_type],
    p.[end_label_description],
    p.[promotional_label_desc],
    p.[label_scan_grade],
    p.[occ_box_end],
    p.[ean_pack_label],
    p.[tpnd_trading_unit],
    p.[tpnb_base_unit],
    p.[display_until],
    p.[best_before],
    p.[f_code],
    p.[requestor],
    p.[supplier_site_code],
    bt.[box_type] AS box_type_name,
    bt.[id] AS box_type_id,
    bt.[is_new] AS box_type_is_new,
    bt.[code] AS box_type_code,
    p.[request_no],
    p.[status],
    p.[submitted_to_iss],
    p.[cancelled_date],
    p.[cancelled_by],
    p.[cancelled_reason]
FROM
    [dbo].[products] p
LEFT JOIN [dbo].[master_products] mp ON p.[product_code] = mp.[id]
LEFT JOIN [dbo].[mark_variety] mv ON p.[mark_variaty] = mv.[id]
LEFT JOIN [dbo].[temperature_grade] tg ON p.[temperature_grade] = tg.[id]
LEFT JOIN [dbo].[brands] b ON p.[brand] = b.[id]
LEFT JOIN [dbo].[end_customers] ec ON p.[end_customer] = ec.[id]
LEFT JOIN [dbo].[box_types] bt ON p.[is_box_type_colours] = bt.[id]
LEFT JOIN [dbo].[machine_format] mf ON p.[machine_format] = mf.[id]
LEFT JOIN [dbo].[reason] r ON p.[reason] = r.[id]
LEFT JOIN [dbo].[product_groups] pg ON pg.group_id = p.group_id
WHERE
    p.[id] =@id;