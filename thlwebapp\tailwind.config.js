/** @type {import('tailwindcss').Config} */

function withOpacity(variableName) {
  return ({ opacityValue }) => {
    if (opacityValue !== undefined) {
      return `rgba(var(${variableName}), ${opacityValue})`;
    }
    return `rgb(var(${variableName}))`;
  };
}

export default {
  // darkMode: 'class', 
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    screens: {
      'sm': '640px',
      // => @media (min-width: 640px) { ... }

      'md': '765px',
      // => @media (min-width: 765px) { ... }

      'lg': '1024px',
      // => @media (min-width: 1024px) { ... }
      
      'xl': '1280px',
      // => @media (min-width: 1280px) { ... }
      
      '2xl': '1536px',
      // => @media (min-width: 1536px) { ... }
      
      '3xl': '1920px',
      // => @media (min-width: 1536px) { ... }
    },
    extend: {
      fontFamily: {
        poppinsblack: ["poppinsblack"],
        poppinsblackitalic: ["poppinsblackitalic"],
        poppinsbold: ["poppinsbold"],
        poppinsbolditalic: ["poppinsbolditalic"],
        poppinsextrabold: ["poppinsextrabold"],
        poppinsextrabolditalic: ["poppinsextrabolditalic"],
        poppinsextralight: ["poppinsextralight"],
        poppinsextralightitalic: ["poppinsextralightitalic"],
        poppinsitalic: ["poppinsitalic"],
        poppinslight: ["poppinslight"],
        poppinslightitalic: ["poppinslightitalic"],
        poppinsmedium: ["poppinsmedium"],
        poppinsmediumitalic: ["poppinsmediumitalic"],
        poppinsregular: ["poppinsregular"],
        poppinssemibold: ["poppinssemibold"],
        poppinssemibolditalic: ["poppinssemibolditalic"],
        poppinsthin: ["poppinsthin"],
        poppinsthinitalic: ["poppinsthinitalic"],
      },
      colors: {
        'black':"#000000",
        'black2':"#333333",
        'black3':"#444444",
        'white': "#FFFFFF",
        'whiter':"#F4F4F4",
        'whiter2':"#F3F8FE",
        'whiter3':"#ECEDF4",
        'whiter4':"#EFF0F6",
        'whitish-gray1':'#D9D9D9',
        'whitish-gray2':'#F3F8FF',
        'statusGray':"#9A9A9A",
        'dark-gray':'#727272',
        'dark-gray2':'#505050',
        'dark-gray3':'#969696',
        'dark-gray4':'#8D8D8D',
        'dark-5':'#929292',
        'light-gray':"#D6D6D6",
        'light-gray2':"#B0B0B0",
        'light-gray3':"#D3D3D3",
        'lighter-gray':"#AFAFAF",
        'line-gray':"#CBCBCB",
        'line-gray2':"#DDDDDD",
        'line-gray3':"#B9B9B9",
        'cream-gray':"#C1C1C1",
        'cream-gray2':"#F1F1F1",
        'gray-gray2':"#F1F1F1",
        'theme-blue':"#002D73",
        'theme-blue2':'#0066FF',
        'bright-red':"#F93647",
        'bright-green':"#3EAB58",
        'bright-yellow':"#FBB522",
        'background-green':"#90EE90",
        'color-green':"#3EAB58",
        'save-green':"#25AE65",
        'backround-yellow':"#FBE6B9",
        'color-yellow':"#FBB522",
        'background-blue':"#C9EDFF",
        'color-blue':"#00B1EB",
        'background-purple':"#D190EE",
        'color-purple':"#6E3EAB",
        'complete-status':"#3eab58",

        'bestatus-1':"#FF9A03",//be estimate
        'bestatus-2':"#33CA7F",//be confirm
        'bestatus-3':"#FFDF37",//be needs updating
        'bestatus-4':"#FB4646",//supplier issues from be
        'be-status': '#FF9A03', //be estimate
        'confirm-status':"#33CA7F",//be confirm and promo confirm
        'wip-status':"#FFDF37",//promo wip, be needs updating
        'issue-status':"#FB4646",//supplier issues from be and task type
        'volumechange-status':"#B3BBDD",//amendint run rate volume
        'pricechange-status':"#2CB0FA",//update price
        'whiteblue':"#fff",//update price but not having the start week
        'seablue':"#00E0D5",
        'locked-products':"#D3EAFF",
        'no-productsbg':"#ddd",
        'cancelled-status':'#ff6c09',
        'qtydiff-status': '#FFCB47',
        'needsupdate-status':'#FFF5D0',
   
      },
      textColor: {
        skin: {
          primary: withOpacity("--color-primary"),
          a11y: withOpacity("--color-a11y"),
        },
      },
      backgroundColor: {
        skin: {
          primary: withOpacity("--color-primary"),
          a11y: withOpacity("--color-a11y"),
        },
      },
      borderColor: {
        skin: {
          primary: withOpacity("--color-primary"),
          a11y: withOpacity("--color-a11y"),
        },
      },
      accentColor: {
        skin: {
          primary: withOpacity("--color-primary"),
          // a11y: withOpacity("--color-a11y"),
        },
      },
      outlineColor: {
        skin: {
          primary: withOpacity("--color-primary"),
          // a11y: withOpacity("--color-a11y"),
        },
      },
      fill: {
        skin: {
          primary: withOpacity("--color-primary"),
          // a11y: withOpacity("--color-a11y"),
        },
      },
      shadowColor: {
        skin: {
          primary: withOpacity("--color-primary"),
          // a11y: withOpacity("--color-a11y"),
        },
      },
    },
  },
  plugins: [],
};

// const darkColors = {
//   'black': "#FFFFFF",
//   'black2': "#CCCCCC",
//   'black3': "#B3B3B3",
//   'white': "#000000",
//   'whiter': "#1A1A1A",
//   'whiter2': "#1D212D",
//   'whiter3': "#121212",
//   'whiter4': "#0E0E10",
//   'whitish-gray1': '#4D4D4D',
//   'whitish-gray2': '#1D212D',
//   'dark-gray': '#A3A3A3',
//   'dark-gray2': '#C0C0C0',
//   'dark-gray3': '#6F6F6F',
//   'dark-gray4': '#727272',
//   'dark-5': '#6D6D6D',
//   'light-gray': "#292929",
//   'light-gray2': "#4F4F4F",
//   'light-gray3': "#2C2C2C",
//   'lighter-gray': "#505050",
//   'line-gray': "#343434",
//   'line-gray2': "#222222",
//   'line-gray3': "#444444",
//   'cream-gray': "#3E3E3E",
//   'cream-gray2': "#0F0F0F",
//   'gray-gray2': "#0F0F0F",
//   'theme-blue': "#97A3D3",
//   'theme-blue2': '#6699FF',
//   'bright-red': "#FF5964",
//   'bright-green': "#65E765",
// };
