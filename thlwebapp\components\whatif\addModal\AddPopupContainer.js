import React, { useContext } from "react";
import { ModalContext } from "../providers/ModalProvider";
import AddPopup from "../AddPopup";

function AddPopupContainer({userData,
  customerList}) {
  const { modalConfig, setModal, currentYear } = useContext(ModalContext);
  return (
    <>
      {modalConfig.isModalOpen && (
        <AddPopup
        userData={userData}
        customerList={customerList}
          onCancel={() => {
            setModal(false);
          }}
          currentYear={currentYear}
        />
      )}
    </>
  );
}

export default AddPopupContainer
