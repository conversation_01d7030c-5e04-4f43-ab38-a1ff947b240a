DECLARE @OutputTable TABLE (
    id INT,
    quantity INT,
    order_id VARCHAR(50),
    reason_id INT,
    subreason_id INT,
    comment VARCHAR(255),
    added_by VARCHAR(255),
    added_timestamp DATETIME,
    cust_code VARCHAR(50)
);
INSERT INTO
    [dbo].[sl_reasons] (
        [quantity],
        [order_id],
        [reason_id],
        [subreason_id],
        [comment],
        [added_by],
        [added_timestamp],
        [cust_code]
    )
OUTPUT 
    INSERTED.id,
    INSERTED.quantity,
    INSERTED.order_id,
    INSERTED.reason_id,
    INSERTED.subreason_id,
    INSERTED.comment,
    INSERTED.added_by,
    INSERTED.added_timestamp,
    INSERTED.cust_code
INTO @OutputTable
VALUES
    (
        @quantity,
        @orderId,
        @reasons,
        @subReason,
        @comment,
        @added_by,
        GETDATE(),
        @cust_code
    );

    SELECT * FROM @OutputTable;

