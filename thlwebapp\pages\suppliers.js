import Layout from "@/components/Layout";
import React, {
  useMemo,
  useState,
  useRef,
  useCallback,
  useEffect,
  Fragment,
} from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faSearch,
  faClose,
  faInfo,
  faXmark,
} from "@fortawesome/free-solid-svg-icons";
import { AgGridReact } from "ag-grid-react";
//import 'ag-grid-enterprise';
import "ag-grid-community/styles//ag-grid.css";
import "ag-grid-community/styles//ag-theme-alpine.css";
import actionRenderer from "../utils/renderer/actionRenderer";
import nameRenderer from "../utils/renderer/nameRenderer";
import statusRenderer from "../utils/renderer/statusRenderer";
import Link from "next/link";
import { useRouter } from "next/router";
import { apiConfig } from "@/services/apiConfig";
import Cookies from "js-cookie";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useLoading } from "@/utils/loaders/loadingContext";
import { usePermissions } from "@/utils/rolePermissionsContext";
import { Dialog, Transition } from "@headlessui/react";
import exportExcel from "../utils/exportExcel";
import supplierCodeRenderer from "@/components/supplierCodeRenderer";
import { getFetchOptions } from "@/utils/fetchOptions";
import { logout } from "@/utils/secureStorage";
import { extractCompanyFromEmail } from "@/utils/extractCompanyFromEmail";
import { ThreeCircles } from "react-loader-spinner";

const suppliers = ({ userData }) => {
  const router = useRouter();
  const { permissions, updatePermissions } = usePermissions();
  const [pageSize, setPageSize] = useState(15);
  const [allRowData, setAllRowData] = useState([]);
  const [rowData, setRowData] = useState([]);
  const [isFiltered, setIsFiltered] = useState(false);
  const [isFilteredName, setIsFilteredName] = useState(false);
  const gridRef = useRef();
  const { setIsLoading } = useLoading();
  const [supplierRoles, setSupplierRoles] = useState([]);
  const [multipleFilterInternalData, setMultipleFilterInternalData] = useState(
    []
  );
  const [containsCancelledSupplier, setContainsCancelledSupplier] =
    useState(false);

  const [exportDisabled, setExportDisabled] = useState(true);
  const [supplierCheckedValue, setSupplierCheckedValue] = useState(false);
  const [isCommonError, setCommonError] = useState("");
  const [isOpenOption, setIsOpenOption] = useState(false);
  const [isUnExportable, setIsUnExportable] = useState(false);
  const [unExportableSuppliernames, setUnExportableSupplierNames] =
    useState("");
  const [supplierUniqueCodeToast, setSupplierUniqueCodeToast] = useState(false);
  const [supplierCodeValid, setSupplierCodeValid] = useState(true);
  const [selectedExportType, setSelectedExportType] = useState("");
  const [prophetId, setProphetId] = useState("");
  const [emailStatusPopup, setEmailStatusPopup] = useState(false);
  const [popupMessage, setPopUpMessage] = useState("");
  const [internalExportSuccess, setInternalExportSuccess] = useState(false);
  const [ISSExportSuccess, setISSExportSuccess] = useState(false);
  const [blockScreen, setBlockScreen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState("default"); // State to track selected status
  const [loading, setLoading] = useState(true);

  const closeOptionModal = () => {
    setIsOpenOption(false);
  };

  const handleExportType = (e) => {
    setSelectedExportType(e.target.value);
  };

  const closeEmailPopup = () => {
    setEmailStatusPopup(false);

    if (internalExportSuccess && ISSExportSuccess) {
      setSelectedStatus("default");
      setStatusChange();
    }
  };

  useEffect(() => {
    if (containsCancelledSupplier) {
      toast.error(
        "Cannot export cancelled supplier."
      );
      setExportDisabled(true);
    }
  }, [containsCancelledSupplier]);

  useEffect(() => {
    if (supplierUniqueCodeToast && !supplierCodeValid) {
      toast.error(
        "Supplier code is not unique and not valid, kindly make sure the supplier code is unique and valid."
      );
    } else if (supplierUniqueCodeToast) {
      toast.error(
        "Supplier code is not unique, kindly make sure the supplier code is unique."
      );
    } else if (!supplierCodeValid) {
      toast.error("Supplier code is not valid");
    }
  }, [supplierUniqueCodeToast, supplierCodeValid]);

  // Helper function to map company names to prophet IDs
  const getCompanyProphetId = (adCompanyName) => {
    if (adCompanyName == "FPP" || adCompanyName == "Fresh Produce Partners") {
      return 4;
    } else if (adCompanyName == "EFC" || adCompanyName == "Ethical Food Company") {
      return 3;
    } else if (adCompanyName == "DPS" || adCompanyName == "Direct Produce Supplies") {
      return 1;
    } else if (adCompanyName == "DPS MS") {
      return 2;
    }
    return 0;
  };

  const [company, setCompany] = useState("");

  useEffect(() => {
    if (typeof document !== "undefined") {
      document.title = "Suppliers";
    }
    if (typeof window !== "undefined") {
      // Get company from userData instead of cookies
      const companyFromUser = userData?.company || extractCompanyFromEmail(userData?.email);
      const ADCompanyName = userData?.companyName || userData?.ADCompanyName;
      if(ADCompanyName=="Direct Produce Supplies" || ADCompanyName=="DPS"){
        localStorage.setItem("prophet",1)
      }else if(ADCompanyName=="Fresh Produce Partners"){
        localStorage.setItem("prophet",4)
      }else if(ADCompanyName=="Ethical Food Company"){
        localStorage.setItem("prophet",3)
      }else if(ADCompanyName=="DPS MS"){
        localStorage.setItem("prophet",2)
      }else if(ADCompanyName=="Integrated Service Solutions Ltd"){
        localStorage.setItem("prophet",5)
      }else{
        localStorage.setItem("prophet",0)
      }
      if (companyFromUser) {
        setCompany(companyFromUser);
      }
      localStorage.removeItem("current");
      localStorage.removeItem("allowedSections");
    }
    Cookies.remove("prophet");
    Cookies.remove("prophets");
    setIsLoading(false);
    
    getData(userData)
      .then((data) => {
        if (data === null) {
          return;
        }
        const formattedData = data?.map((row) => {
          const roleIds = row?.role_ids
            ? JSON.parse(row?.role_ids).map((ele) => ele.role_id)
            : [];
          const roleJson = JSON.parse(row?.role_json);
          const formattedRow = {
            isActive: row?.is_active,
            prophets: row?.prophet_ids ? JSON.parse(row?.prophet_ids) : [],
            supplier_code: row?.prophet_ids
              ? JSON.parse(row?.prophet_ids)[0].prophet_code
              : [],
            company_name: row?.name ? row?.name : "Not Entered",
            country_code: row?.country_code,
            payment_type: row?.payment_type,
            payment_type_name: row?.payment_type_name,
            currency_name: row?.currency_name,
            currency_id: row?.iss_currency_id,
            global_gap_number: row?.global_gap_number,
            chile_certificate_number: row?.chile_certificate_number,
            red_tractor: row?.red_tractor,
            organic_certificate_number: row?.organic_certificate_number,
            puc_code: row?.puc_code,
            address_line_1: row?.address_line_1,
            address_line_2: row?.address_line_2,
            address_line_3: row?.address_line_3,
            address_line_4: row?.address_line_4,
            postal_code: row?.postal_code,
            id: row?.id,
            currency: row?.currency ? row?.currency : "Not Entered",
            currency_code: row?.currency_code ? row?.currency_code : "",
            Compliance: row?.compliance,
            Financials: row?.financial,
            General: row?.technical,
            Procurement: row?.procurement,
            requestor: row?.requestor_name,
            requestor_email: row?.requestor_email,
            companies: row?.prophet_names ? row?.prophet_names : "Not Entered",
            role: row?.role_names ? row?.role_names : "Not Entered",
            roleJson: row?.role_json ? JSON.parse(row?.role_json) : [],
            roleId: row?.role_ids ? JSON.parse(row?.role_ids) : [],
            roleIds: row?.role_ids
              ? JSON.parse(row?.role_ids).map((ele) => {
                  return ele.role_id;
                })
              : [],
            supplier_type: row?.supplier_type_label,
            contacts_json: row?.contacts_json,
            distribution_points_json: row?.distribution_points_json
              ? JSON.parse(row?.distribution_points_json)
              : [],
            vat_number: row?.vat_number,
            payment_terms: row?.payment_terms,
            sort_bic: row?.decryptedSort_Bic,
            name_branch: row?.decryptedName_branch,
            account_number: row?.decryptedAccountNumber,
            vatable: row?.vatable,
            iss_ledger_code: row?.iss_ledger_code,
            internal_ledger_code: row?.internal_ledger_code,
            intermediatery_account_number:
              row?.decryptedIntermediatery_account_number,
            bacs_currency_code: row?.bacs_currency_code,
            has_iban: row?.has_iban,
            isProducerSupplier: row?.product_supplier,
            isEmergencyRequest: row?.emergency_request,
            supplier_group: row?.sendac_groups_json,
            supplierLinks: row?.supplier_links_json,
            status: row?.label,
            role_num: row?.role_nums,
            edi: row?.edi,
          };
          if (
            (roleIds.includes(1) || roleIds?.includes(6)) &&
            row?.emergency_request &&
            row?.financial !== "Completed"
          ) {
            formattedRow.isEmergencyAndFinanceNotComplete = true;
          } else {
            formattedRow.isEmergencyAndFinanceNotComplete = false;
          }
          return formattedRow;
        });
        setAllRowData(formattedData);
        const filteredData =
          formattedData.length > 0
            ? formattedData?.filter(
                (row) => row.status !== "Cancelled" && row.status !== "Exported"
              )
            : [];

        setRowData(filteredData);
        setLoading(false);

        const fetchRolePermissions = async () => {
          try {
            let serverAddress = apiConfig.serverAddress;
            const response = await fetch(
              `${serverAddress}suppliers/get-role-permissions`,
              getFetchOptions()
            );

            if (!response.ok) {
              if (response.status === 401) {
                toast.error("Your session has expired. Please log in again.");
                setTimeout(async () => {
                  await logout();
                  router.push('/login');
                }, 3000);
              }
              throw new Error(`Request failed with status ${response.status}`);
            }

            const result = await response.json();
            const rolePermissions = {};
            for (const row of result) {
              const sectionsArray = row?.sections
                ?.split(",")
                ?.filter((section) => section?.trim() !== ""); // Split sections string into an array and remove empty values
              rolePermissions[row?.role_id] = sectionsArray;
            }
            updatePermissions(rolePermissions);
          } catch (error) {
            console.error(error);
          }
        };

        fetchRolePermissions();
      })
      .catch((error) => {
        console.log(error);
      });
  }, [userData]); // Add userData as dependency

  function getData() {
    // Get company data from userData (session) instead of cookies
    const company = userData?.company || extractCompanyFromEmail(userData?.email);
    const ADCompanyName = userData?.companyName || userData?.ADCompanyName;
    
    let serverAddress = apiConfig.serverAddress;
    let prophetId = getCompanyProphetId(ADCompanyName);
    
    return fetch(`${serverAddress}suppliers/get-suppliers/${company}/${prophetId}`, getFetchOptions())
      .then(async (res) => {
        if (res.status == 502) {
          setBlockScreen(true);
          return;
        }
        setBlockScreen(false);
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
          return null;
        } else if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async () => {
            await logout();
            router.push('/login');
          }, 3000);
          return null;
        }
        if (res.status === 200) {
          return res.json();
        }
        throw new Error("Failed to fetch data");
      })
      .catch((error) => {
        setCommonError(error.message);
      });
  }

  function deleteAll() {
    setIsLoading(true);
    let serverAddress = apiConfig.serverAddress;
    return fetch(`${serverAddress}suppliers/delete-all`, getFetchOptions('DELETE'))
      .then(async (res) => {
        if (res.status === 400) {
          setIsLoading(false);
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (res.status === 401) {
          setIsLoading(false);
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async () => {
            await logout();
            router.push('/login');
          }, 3000);
        }
        if (res.status === 200) {
          toast.info("Delete successfull");
          setIsLoading(false);
          router.reload();
        }
        throw new Error("Failed to delete");
      })
      .catch((error) => {
        setIsLoading(false);
        console.error(error);
      });
  }

  const handlePageSizeChange = (event) => {
    const newPageSize = parseInt(event.target.value, 15);
    setPageSize(newPageSize);
    gridRef.current.api.paginationSetPageSize(newPageSize);
  };

  const defaultColDef = useMemo(() => ({
    sortable: true,
    filter: false,
    resizable: true,
    flex: 1,
  }));

  const gridOptions = {
    responsive: true,
  };

  const CustomCellRenderer = (params) => {
    const truncatedText = params?.value;
    return <span title={params?.value}>{truncatedText}</span>;
  };

  const setStatusChange = () => {
    setSelectedStatus('default')
    setIsLoading(true);
    setTimeout(function () {
      getData()
        .then((data) => {
          if (data === null) {
            return;
          }
          const formattedData = data?.map((row) => {
            const roleIds = row?.role_ids
              ? JSON.parse(row?.role_ids).map((ele) => ele.role_id)
              : [];
            const roleJson = JSON.parse(row?.role_json);

            const formattedRow = {
              isActive: row?.is_active,
              prophets: row?.prophet_ids ? JSON.parse(row?.prophet_ids) : [],
              supplier_code: row?.prophet_ids
                ? JSON.parse(row?.prophet_ids)[0].prophet_code
                : [],
              company_name: row?.name ? row?.name : "Not Entered",
              id: row?.id,
              currency: row?.currency ? row?.currency : "Not Entered",
              Compliance: row?.compliance,
              Financials: row?.financial,
              General: row?.technical,
              Procurement: row?.procurement,
              requestor: row?.requestor_name,
              companies: row?.prophet_names
                ? row?.prophet_names
                : "Not Entered",
              role: row?.role_names ? row?.role_names : "Not Entered",
              roleId: row?.role_ids ? JSON.parse(row?.role_ids) : [],
              roleIds: row?.role_ids
                ? JSON.parse(row?.role_ids).map((ele) => {
                    return ele.role_id;
                  })
                : [],
              country_code: row?.country_code,
              payment_type: row?.payment_type,
              payment_type_name: row?.payment_type_name,
              currency_name: row?.currency_name,
              currency_id: row?.iss_currency_id,
              global_gap_number: row?.global_gap_number,
              chile_certificate_number: row?.chile_certificate_number,
              red_tractor: row?.red_tractor,
              organic_certificate_number: row?.organic_certificate_number,
              puc_code: row?.puc_code,
              address_line_1: row?.address_line_1,
              address_line_2: row?.address_line_2,
              address_line_3: row?.address_line_3,
              address_line_4: row?.address_line_4,
              postal_code: row?.postal_code,
              currency_code: row?.currency_code ? row?.currency_code : "",
              requestor_email: row?.requestor_email,
              companies: row?.prophet_names
                ? row?.prophet_names
                : "Not Entered",
              vatable: row?.vatable,
              roleJson: row?.role_json ? JSON.parse(row?.role_json) : [],
              supplier_type: row?.supplier_type_label,
              isProducerSupplier: row?.product_supplier,
              isEmergencyRequest: row?.emergency_request,
              supplier_group: row?.sendac_groups_json,
              supplierLinks: row?.supplier_links_json,
              status: row?.label,
              role_num: row?.role_nums,
              contacts_json: row?.contacts_json,
              distribution_points_json: row?.distribution_points_json
                ? JSON.parse(row?.distribution_points_json)
                : [],
              vat_number: row?.vat_number,
              payment_terms: row?.payment_terms,
              sort_bic: row?.decryptedSort_Bic,
              name_branch: row?.decryptedName_branch,
              account_number: row?.decryptedAccountNumber,
              iss_ledger_code: row?.iss_ledger_code,
              internal_ledger_code: row?.internal_ledger_code,
              intermediatery_account_number:
                row?.decryptedIntermediatery_account_number,
              bacs_currency_code: row?.bacs_currency_code,
              has_iban: row?.has_iban,
              edi: row?.edi,
            };

            if (
              (roleIds?.includes(1) || roleIds?.includes(6)) &&
              row?.emergency_request &&
              row?.financial !== "Completed"
            ) {
              formattedRow.isEmergencyAndFinanceNotComplete = true;
            } else {
              formattedRow.isEmergencyAndFinanceNotComplete = false;
            }
            return formattedRow;
          });
          setAllRowData(formattedData);

          const filteredData =
            formattedData.length > 0
              ? formattedData?.filter(
                  (row) =>
                    row.status !== "Completed" &&
                    row.status !== "Cancelled" &&
                    row.status !== "Exported"
                )
              : [];

          setRowData(filteredData);
          setLoading(false);
          setIsLoading(false);
        })
        .catch((error) => {
          console.log(error);
          setIsLoading(false);
        });
    }, 3000);
  };

  const CustomTooltipComponent = ({ value }) => (
    <div title={value}>{value}</div>
  );

  const columnDefs = [
    {
      headerName: "Supplier Name",
      field: "company_name",
      checkboxSelection: (params) => {
        return params.data.status === "Cancelled"
          ? { checked: false, disabled: true }
          : true;
      },
      cellRenderer: nameRenderer,
      headerCheckboxSelection: true,
      flex: "8%",
      filter: true,
      cellRendererParams: {
        setSuppliers: setRowData,
        setIsFiltered: setIsFiltered,
        setIsFilteredName: setIsFilteredName,
      },
    },
    {
      headerName: "Supplier Code",
      cellRenderer: supplierCodeRenderer,
      field: "supplier_code",
      flex: "3%",
    },
    {
      headerName: "Roles",
      field: "role",
      cellRenderer: CustomCellRenderer,
      tooltipComponent: CustomTooltipComponent,
      flex: "3%",
    },
    {
      headerName: "Companies",
      field: "companies",
      cellRenderer: CustomCellRenderer,
      tooltipComponent: CustomTooltipComponent,
      flex: "4%",
    },
    {
      headerName: "Currency",
      field: "currency",
      flex: "3%",
      cellRenderer: CustomCellRenderer,
      tooltipComponent: CustomTooltipComponent,
      cellStyle: (params) => {
        if (params.value == "Not Entered") {
          return { color: "#B31312" };
        }
        return null;
      },
    },
    {
      headerName: "Requestor",
      cellRenderer: CustomCellRenderer,
      tooltipComponent: CustomTooltipComponent,
      field: "requestor",
      flex: "4%",
    },
    {
      headerName: "General",
      cellRenderer: statusRenderer,
      field: "General",
      flex: "3%",
    },
    {
      headerName: "Financial",
      cellRenderer: statusRenderer,
      field: "Financials",
      flex: "3%",
    },
    {
      headerName: "Compliance",
      field: "Compliance",
      cellRenderer: statusRenderer,
      flex: "4%",
    },
    {
      headerName: "Procurement",
      field: "Procurement",
      cellRenderer: statusRenderer,
      flex: "4%",
    },
    {
      headerName: "Status",
      field: "status",
      cellRenderer: statusRenderer,
      cellStyle: () => ({ justifyContent: "center" }),
      flex: "4%",
      hide: false,
    },
    {
      field: "",
      cellRenderer: (params) =>
        actionRenderer(params, userData, company), // Remove token parameter
      flex: "2%",
      cellStyle: () => ({}),
      sortable: false,
      cellRendererParams: {
        setUpdateStatusChange: setStatusChange,
      },
    },
    {
      field: "role_num",
      hide: true,
    },
  ];

  const onFilterTextBoxChanged = useCallback(() => {
    gridRef.current.api.setQuickFilter(
      document.getElementById("filter-text-box").value
    );
  }, []);

  const handleFilterToggle = useCallback(
    (e) => {
      const { value } = e.target;
      setSelectedStatus(value);

      let filteredData = [];
      if (value == "default") {
        filteredData = allRowData.filter(
          (row) => row.status !== "Cancelled" && row.status !== "Exported"
        );
      } else {
        filteredData = allRowData.filter((row) => row.status === value);
      }
      setRowData(filteredData);
      setLoading(false);
      setExportDisabled(filteredData.length === 0);
    },
    [allRowData]
  );

  const exportFilteredData = async () => {
    let export_ISSresponse = false;
    let exportInternal_response = false;
    const gridApi = gridRef.current.api;

    let filteredData = [];
    const isInternal = selectedExportType === "internalExport";
    
    if (isInternal) {
      gridApi.forEachNodeAfterFilter((node) => {
        if (
          (node.data !== undefined && node.data?.status === "Completed") ||
          node.data?.status === "Exported" ||
          (node.data?.isEmergencyRequest && node.data?.General === "Complete")
        ) {
          filteredData = [
            [
              {
                "Supplier Active": "test",
                "Haulage cube local": "test",
                "Haulage cube name": "test",
                "update guesstimates type": "test",
                "Organization ID": "",
                "Enforce department": "",
                "Sendac Group": "",
                "Supplier name": "",
                "Supplier type": "",
                "User Lookup 2": "",
                "Address Line 1": "",
                "Address Line 2": "",
                "Address Line 3": "",
                "Address Line 4": "",
                "Post code": "",
                "Country code": "",
                "Payee supplier code": "",
                "Invoice supplier": "",
                "Head office": "",
                "Settlement days": "",
                "Bank General Ledger Code": "",
                "Currency number": "",
                "Currency number / name": "",
                "Bank general ledger code": "",
                "Payment type": "",
                "Country code": "",
                Vatable: "",
                vatable: "",
                "Update guesstimates type": "",
                "Area Number": "",
                Buyer: "",
                "Multiple lot indicator": "",
                "multiple lot indicator": "",
                "Generate Pallet Loading Plan": "",
                "Distribution point for supplier": "",
                "Payment terms": "",
                "Department Number": "",
              },
            ],
            // ... additional data structures
          ];
        }
      });
    } else {
      gridApi.forEachNodeAfterFilter((node) => {
        let rolesArray = node.data.roleId.map((ele) => {
          return ele.role_id;
        });
        if (
          (node.data !== undefined && node.data?.status === "Completed") ||
          node.data?.status === "Exported" ||
          (node.data?.isEmergencyRequest && node.data?.General === "Complete")
        ) {
          const filteredExportData = {
            "Supplier Active": node?.data?.isActive ? 1 : 0,
            "Supplier code": node?.data?.prophets[0]?.prophet_code?.trim(),
            "EDI Partner": "",
            "Supplier name": node?.data?.company_name?.trim(),
            "Country Code": node?.data?.country_code,
            "Distribution Point for Supplier": 6,
            "Bank Ledger Code": node?.data?.currency_id,
            "Area Number": 170,
            Vatable: 0,
            Buyer: 1,
            "Billing type": 0,
            "Payment type": node?.data?.payment_type,
            "Currency number": node?.data?.currency_id,
            GGN: node?.data?.global_gap_number,
            "Organic cert": node?.data?.organic_certificate_number,
            "Regional cert": node?.data?.chile_certificate_number,
            "Head office": node?.data?.prophets[0]?.prophet_code?.trim(),
            "Address line 1": node?.data?.address_line_1,
            "Address line 2": node?.data?.address_line_2,
            "Address line 3": node?.data?.address_line_3,
            "Address line 4": node?.data?.address_line_4,
            "Postal code": node?.data?.postal_code,
            status: node?.data?.status,
            id: node?.data?.id,
          };
          filteredData.push(filteredExportData);
        }
      });
    }

    if (filteredData.length === 0) {
      toast.error("No filtered data to export.", {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: false,
        draggable: true,
        progress: undefined,
        theme: "light",
      });
    } else {
      if (supplierCheckedValue) {
        if (true) {
          const allStatesData = [
            ulpFilData,
            supplierActiveData,
            roleData,
            sendacGroupData,
            bankAc,
            senBnk,
            contactData,
            organizationData,
            organizationRoleData,
            sheetSupplierId,
          ];
          exportInternal_response = await exportExcel(
            allStatesData,
            true,
            null, // Remove token parameter
            company,
            userData,
            prophetId,
            userData?.email,
            ""
          );
        }
        if (!isUnExportable) {
          const allStatesData = [multipleFilterISSData, roleData];

          export_ISSresponse = await exportExcel(
            allStatesData,
            false,
            null, // Remove token parameter
            company,
            userData,
            prophetId,
            userData?.email,
            ""
          );
        }

        setEmailStatusPopup(true);
        if (export_ISSresponse && exportInternal_response) {
          setPopUpMessage(`Email sent to Finance Department and ISS admin`);
          setISSExportSuccess(true);
          setInternalExportSuccess(true);
        } else if (exportInternal_response && isUnExportable) {
          setPopUpMessage(
            `Email sent to Finance Department, but suppliers ${unExportableSuppliernames} not exported as Hauliers and Expense roles not allowed to be exported to ISS`
          );
          setInternalExportSuccess(true);
        } else if (export_ISSresponse) {
          setISSExportSuccess(true);
          setPopUpMessage(
            "Email sent to ISS Admin Team, but not to Finance Department"
          );
        } else {
          setPopUpMessage(
            "Email not sent to either Finance Department or ISS Admin Team"
          );
        }
      }
    }

    setSelectedExportType("");
    gridRef.current.api.deselectAll();
  };

  // ... (continuing with the rest of the state variables and functions)
  const [supplierActiveData, setSupplierActiveData] = useState([
    ["sendac (Supplier file)"],
  ]);
  const [roleData, setRoleData] = useState([
    ["sendacrole (Supplier role file)"],
  ]);
  const [contactData, setContactData] = useState([
    ["contactdet (Supplier personnel contact details)"],
  ]);
  const [organizationData, setOrganizationData] = useState([
    ["organization (Organization)"],
  ]);
  const [organizationRoleData, setOrganizationRoleData] = useState([
    ["orgroles (Organization Roles)"],
  ]);
  const [sendacGroupData, setSendacGroupData] = useState([
    ["sendacgroup (Sendac group file)"],
  ]);
  const [bankAc, setBankAc] = useState([
    ["bankac (Bank account details table)"],
  ]);
  const [multipleFilterISSData, setMultipleFilterISSData] = useState([
    ["Supplier Data"],
  ]);
  const [senBnk, setSenBnk] = useState([["senbnk (Supplier bank link table)"]]);
  const [ulpFilData, setUlpFilData] = useState([["UlpFil"]]);
  const [sheetSupplierId, setSheetSupplierId] = useState([["Supplier Id"]]);

  const handleGridReady = (params) => {
    params.api.setColumnDefs(columnDefs);
  };

  const extractContacts = (supplierCode, contactsJsonStr, supplierName) => {
    try {
      const contacts = contactsJsonStr ? JSON.parse(contactsJsonStr) : [];
      if (Array.isArray(contacts)) {
        return contacts.map((contact) => ({
          "Supplier code": supplierCode ? supplierCode : "",
          "Contact ID": "",
          Name: supplierName || "",
          "Email Address": contact.email_id || "",
          "Telephone number": contact.telephone || "",
          "Cell phone number": "",
          "Fax number": "",
          "Instant Message": "",
          "Physical Address": "",
          "Postal Address": "",
          "Row verision": "",
          "Created timestamp": "",
        }));
      }
    } catch (error) {
      console.error("Error parsing contacts_json:", error);
    }
    return [
      {
        "Contact ID": "",
        Name: supplierName || "",
        "Email Address": "",
        "Telephone number": "",
        "Cell phone number": "",
        "Fax number": "",
        "Instant Message": "",
        "Physical Address": "",
        "Postal Address": "",
        "Row verision": "",
        "Created timestamp": "",
      },
    ];
  };

  const extractSendacGroup = (sendacGroupJson) => {
    try {
      const sendacGroups = sendacGroupJson ? JSON.parse(sendacGroupJson) : [];
      if (Array.isArray(sendacGroups)) {
        return sendacGroups?.map((group) => ({
          "Supplier group": "",
          Description: group?.created_by ? group?.label : "",
        }));
      }
    } catch (error) {
      console.error("Error parsing contacts_json:", error);
    }
  };

  const multipleSendRoleOnRoleNums = (data, role_num) => {
    const roleNums = role_num?.split(",").map((num) => num.trim());
    const roleNames = data?.role?.split(",").map((name) => name.trim());

    return roleNums.map((num, index) => ({
      Sendacroleid: "",
      "Supplier code": data?.prophets[0]?.prophet_code
        ? data.prophets[0].prophet_code.trim()
        : "",
      Description: data?.prophets[0]?.prophet_code
        ? data.prophets[0].prophet_code.trim()
        : "",
      "Supplier Code Supplier Name": data.company_name,
      Type: num,
      "Type Description": roleNames[index],
      "Supplier code Global gap number": data?.global_gap_number,
      "Created timestamp": "",
      Active: 1,
    }));
  };

  function getGLCode(internal_ledger_code) {
    if (internal_ledger_code) {
      return internal_ledger_code;
    } else return "";
  }

  const [incompleteToast, setIncompleteToast] = useState(false);
  
  useEffect(() => {
    if (incompleteToast) {
      toast.error("Supplier details are incomplete.", {
        position: "top-right",
        autoClose: 1000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: false,
        draggable: true,
        progress: undefined,
        theme: "light",
      });
    }
  }, [incompleteToast]);

  useEffect(() => {
    if (containsCancelledSupplier) {
      toast.error("Cannot export cancelled supplier.", {
        position: "top-right",
        autoClose: 1000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: false,
        draggable: true,
        progress: undefined,
        theme: "light",
      });
    }
  }, [containsCancelledSupplier]);

  const handleCheckboxEvent = (event) => {
    const getRowData = event.data;
    const isSelected = event.node.selected;
    const selectedRows = gridRef.current.api.getSelectedRows();
    const prophet_id = getRowData.prophets[0].prophet_id;
    setProphetId(prophet_id);
    
    const extractedValues = selectedRows.map(
      ({ status, isEmergencyRequest, General }) => ({
        status,
        isEmergencyRequest,
        General,
      })
    );

    const exportDisabled = extractedValues.some(
      ({ status, isEmergencyRequest, General }) => {
        return !(
          status === "Completed" ||
          status === "Exported" ||
          (isEmergencyRequest && General === "Complete")
        );
      }
    );

    const canExport = extractedValues.every(({ isEmergencyRequest }) => {
      return !(
        !isEmergencyRequest &&
        (userData?.role_id != 1 ||
          userData?.role_id != 2 ||
          userData?.role_id != 5 ||
          userData?.role_id != 6)
      );
    });

    const isExportableBasedOnCodeUnique = selectedRows.every((row) => {
      const codeCount = row?.prophets[0]?.code_count;
      const prophetCode = row?.prophets[0]?.prophet_code;
      if (codeCount && codeCount > 1 && prophetCode && prophetCode !== "") {
        return false;
      } else if (
        codeCount &&
        codeCount == 1 &&
        prophetCode &&
        prophetCode !== ""
      ) {
        return true;
      }
      return false;
    });

    const doesContainCancelledSupplier = selectedRows.some(
      (row) => row.status === "Cancelled"
    );

    const isExportValid = selectedRows.every((row) => {
      const supCode = row?.prophets[0]?.prophet_code;
      const prophet_id = row?.prophets[0]?.prophet_id;
      const isSupplierAccount =
        row?.roleIds?.includes(1) || row?.roleIds?.includes(6);
      let currency =
        row?.currency == "$" ? `\\${row?.currency}` : row?.currency;
      let actualCurr;
      if (currency && currency == "Not Entered") {
        actualCurr = "";
      } else {
        actualCurr = currency;
      }

      let isValid = true;

      if (isSupplierAccount) {
        if (prophet_id == 1) {
          let regexPattern;
          regexPattern = new RegExp(`^[A-Z0-9]{4}[A-Z0145678]${actualCurr}$`);
          isValid = regexPattern.test(supCode);
        } else if (prophet_id == 2) {
          let regexPattern;
          regexPattern = new RegExp(`^([A-Z0]{4})9${actualCurr}$`);
          isValid = regexPattern.test(supCode);
        } else if (prophet_id == 3) {
          let regexPattern = new RegExp(`^[A-Z0-9]{4}[A-Z01345678][A-Z0-9]*$`);
          isValid = regexPattern.test(supCode) && supCode.length == 6;
        } else if (prophet_id == 4) {
          let regexPattern;
          regexPattern = new RegExp(`^([A-Z0]{4})2${actualCurr}$`);
          isValid = regexPattern.test(supCode);
        }
      }
      return isValid;
    });

    if (selectedRows.length > 0) {
      if (
        !canExport &&
        userData?.role_id != 1 &&
        userData?.role_id != 2 &&
        userData?.role_id != 5 &&
        userData?.role_id != 6
      ) {
        setExportDisabled(true);
      } else if (doesContainCancelledSupplier) {
        setContainsCancelledSupplier(true);
        setExportDisabled(true);
      } else if (!isExportableBasedOnCodeUnique) {
        setSupplierUniqueCodeToast(true);
        setExportDisabled(true);
      } else if (!isExportValid) {
        setSupplierCodeValid(false);
        setExportDisabled(true);
      } else {
        setExportDisabled(exportDisabled);
      }
    } else {
      setExportDisabled(true);
    }

    let isUnExportableToISS = false;
    let supplierNames = [];
    selectedRows.forEach((row) => {
      if (
        !row?.roleIds?.includes(1) &&
        !row?.roleIds?.includes(2) &&
        !row?.roleIds?.includes(3) &&
        !row?.roleIds?.includes(4)
      ) {
        isUnExportableToISS = true;
        supplierNames.push(row.company_name);
      }
    });
    const supplierNamesString = supplierNames.join(", ");
    setIsUnExportable(isUnExportableToISS);
    setUnExportableSupplierNames(supplierNamesString);

    if (
      getRowData?.status == "Completed" ||
      getRowData?.status == "Exported" ||
      (getRowData?.isEmergencyRequest &&
        getRowData.status != "Cancelled" &&
        getRowData?.General === "Complete")
    ) {
      let regional_cert = "";
      if (
        getRowData?.roleIds?.includes(2) ||
        getRowData?.roleIds?.includes(3)
      ) {
        if (getRowData.country_code == "UK") {
          regional_cert = getRowData?.red_tractor;
        } else if (getRowData.country_code == "ZA") {
          regional_cert = getRowData?.puc_code;
        } else if (getRowData.country_code == "CL") {
          regional_cert = getRowData?.chile_certificate_number;
        }
      }

      let currencyId = "";
      let currencyName = "";
      if (
        getRowData?.roleIds?.includes(1) ||
        getRowData?.roleIds?.includes(6)
      ) {
        currencyId = getRowData?.currency_id;
        currencyName = getRowData?.currency_name;
      } else {
        currencyId = 1;
        currencyName = "Sterling";
      }

      function getCorrespondingUserLookup(curr) {
        if (curr == "GBP") {
          return "GBPBACS";
        } else if (curr == "EUR") {
          return "EUROSEPA";
        } else if (curr == "USD") {
          return "USDPRIORITY";
        } else {
          return "";
        }
      }
      const filteredISSExportData = {
        "Supplier Active": "N/A",
        "Supplier code": getRowData?.prophets[0]?.prophet_code?.trim(),
        "EDI Partner": "N/A",
        "Supplier name": getRowData?.company_name?.trim(),
        "EDI ANA number": getRowData?.edi ?? "N/A",
        "Producer (supplier)": "N/A",
        "Department number": "N/A",
        "Currency number": getRowData?.currency_id
          ? getRowData?.currency_id
          : 1,
        "Global gap number": getRowData?.global_gap_number,
        "Grower group": "N/A",
        "Defra county number": "N/A",
        "Date start": "N/A",
        "Date end": "N/A",
        "Organic Cert": getRowData?.organic_certificate_number,
        "Regional Cert": regional_cert,
        "Head office": getRowData?.prophets[0]?.prophet_code?.trim(),
        "Country Code": getRowData?.country_code,
        "Distribution point for supplier":
          getRowData?.distribution_points_json?.length > 0
            ? getRowData?.distribution_points_json[0].from_dp
            : "N/A",
        "Bool 2": "N/A",
        "Bool 3": "N/A",
        "Address line 1": getRowData?.address_line_1,
        "Address line 2": getRowData?.address_line_2,
        "Address line 3": getRowData?.address_line_3,
        "Address line 4": getRowData?.address_line_4,
        "Post code": getRowData?.postal_code,
        "Currency Number": getRowData?.currency_id
          ? getRowData?.currency_id
          : 1,
        "Bank general ledger code": getRowData?.iss_ledger_code
          ? getRowData?.iss_ledger_code
          : "12200",
        "Bank general ledger code Currency number if bank":
          getRowData?.currency_id ? getRowData?.currency_id : 1,
        "Settlement days": getRowData?.payment_terms,
        "Department Number":
          getRowData?.prophets[0]?.prophet_id == 1
            ? 1
            : getRowData?.prophets[0]?.prophet_id == 2
            ? 9
            : getRowData?.prophets[0].prophet_id == 3
            ? 3
            : getRowData?.prophets[0].prophet_id == 4
            ? 2
            : "N/A",
        "Area Number": "1",
        Vatable:
          getRowData?.vatable != null ? (getRowData?.vatable ? "1" : "0") : "0",
        Buyer: "1",
        "Billing type": "0",
        "Payment type": getRowData?.payment_type ? getRowData?.payment_type : 2,
        "Expense general ledger code": "N/A",
        "Authorise on register": "N/A",
        "Use % authorise rule": 5,
        "User text 1": "N/A",
        "Mandatory altfil on service jobs": "N/A",
        "Organization ID": "N/A",
        id: getRowData?.id,
        isEmergencyAndFinanceNotComplete:
          getRowData?.isEmergencyAndFinanceNotComplete,
      };

      const newSupplierActiveData = {
        "Supplier Active": getRowData?.isActive ? 1 : 0,
        "Haulage cube local": "",
        "Haulage cube name": "",
        "update guesstimates type": 1,
        "Organization ID": "",
        "Vat number 1": getRowData?.vat_number,
        "Organic cert": getRowData?.organic_certificate_number,
        "Regional cert": regional_cert,
        "Global gap number": getRowData?.global_gap_number,
        "Enforce department": "",
        "Sendac Group": getRowData?.supplier_group
          ? JSON.parse(getRowData?.supplier_group)[0].value
          : "",
        "Supplier code": getRowData?.prophets[0]?.prophet_code?.trim(),
        "Supplier name": getRowData.company_name,
        "Supplier type": getRowData?.supplier_type,
        "User Lookup 2": "",
        "Address Line 1": getRowData?.address_line_1,
        "Address Line 2": getRowData?.address_line_2,
        "Address Line 3": getRowData?.address_line_3,
        "Address Line 4": getRowData?.address_line_4,
        "Post code": getRowData?.postal_code,
        "Country code": getRowData?.country_code,
        "Payee supplier code": "",
        "Invoice supplier": "",
        "Head office": "",
        "Settlement days": getRowData?.payment_terms,
        "Bank general ledger code Currency number if bank": currencyId,
        "Currency number": currencyId,
        "Currency number Currency name": currencyName,
        "Bank general ledger code": getGLCode(getRowData?.internal_ledger_code),
        "payment type": getRowData?.payment_type,
        "Payment type name": getRowData?.payment_type_name,
        "country code": getRowData?.country_code,
        Vatable:
          getRowData?.vatable != null ? (getRowData?.vatable ? "1" : "0") : "0",
        "vatable desc":
          getRowData?.vatable != null
            ? getRowData?.vatable
              ? "Vatable"
              : "None vatable"
            : "None vatable",
        "Area Number":
          getRowData?.prophets[0].prophet_id == 3 ||
          getRowData?.prophets[0].prophet_id == 4
            ? 1
            : 7,
        Buyer: 1,
        "Multiple lot indicator": "0",
        "multiple lot indicator desc": "By Lot",
        "Generate Pallet Loading Plan": "",
        "Distribution point for supplier": 6,
        "Payment terms": "",
        "Department Number":
          getRowData?.prophets[0]?.prophet_id == 1
            ? 1
            : getRowData?.prophets[0]?.prophet_id == 2
            ? 9
            : getRowData?.prophets[0].prophet_id == 3
            ? 3
            : getRowData?.prophets[0].prophet_id == 4
            ? 2
            : "",
        "Allow credit rebates": "",
        "Alternative DP for supplier": 1,
        "Actual posting stops purchase charges": "",
        "Authorise on register": "",
        "User text 1": "",
        "User lookup 1":
          getRowData?.prophets[0].prophet_id == 3 ||
          getRowData?.prophets[0].prophet_id == 4
            ? getCorrespondingUserLookup(getRowData?.currency_code)
            : "",
        "Receive orders from edi": "",
        "Send invoices from edi": "",
        "Send orders from edi": "",
        "EDI partner":
          getRowData?.prophets[0].prophet_id == 3 ||
          getRowData?.prophets[0].prophet_id == 4
            ? 2000
            : "",
        "Generic code":
          getRowData?.prophets[0].prophet_id == 3 ||
          getRowData?.prophets[0].prophet_id == 4
            ? "STOCK"
            : "",
        "EDI ANA number": getRowData?.edi ?? "N/A",
        "User % authorize rule": 5,
      };

      let UlpFil = {};

      if (
        getRowData?.distribution_points_json?.length > 0 &&
        (getRowData?.distribution_points_json[0].from_dp === null ||
          getRowData?.distribution_points_json[0].from_dp === undefined)
      ) {
        UlpFil = {
          "Distribution point": "",
          Description:
          company=="dpsltd"?
            getRowData?.distribution_points_json?.length > 0 &&
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined)
              ? getRowData?.distribution_points_json[0].name
              : "":getRowData?.prophets[0]?.prophet_code?.trim(),
          "Service Supplier Code":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            getRowData?.prophets[0]?.prophet_code?.trim():"",
          "Default expected stock status":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            "0": "",
          "Default received stock status":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            "0": "",
          "Pallets in packhouse":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            "0": "",
          "Default haulier":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            "ZZZZZ": "",
          "Default expected location id":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            21: "",
          "Default receiving location id":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            21: "",
          "Packhouse location id":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            21: "",
          "Default pick location id":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            21: "",
          "Despatch location id":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            21: "",
          "Default waste location id":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            21: "",
          "Default pre-pick location id":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            21: "",
          "Default returns location id":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            21: "",
          Address: "",
          "Service supplier code": "",
          "EDI Reference Code": "",
          "EDI ANA Code": "",
          "User Integer 1":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            1: "",
          "Movement resource group": "",
          "Handheld application used":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            "0": "",
          "Pallets in procure/receiving":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            "0": "",
          "Operational depo": "",
          "Enabled for masterfile sending": "",
          "Connected registed depot": "",
          "EDI Transmission type of depo": "",
          "Container loading depo":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            "0": "",
          "Airport depot": "",
          "Sms notification": "",
          Port: "",
          Dormant: "",
          Active:
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            1: "",
          "Ingredient distribution point": "",
          "Show in CE":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            1: "",
          "Charge direction": "",
          "Pallet receive time": "",
          "User string 3": "",
          "Direct DP":
          company=="dpsltd"?
            getRowData?.distribution_points_json?.length > 0 &&
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined)
              ? getRowData?.distribution_points_json[0]?.direct_dp
                ? 1
                : "0"
              : "": "",
          "Include on XML":
          company=="dpsltd"?
            (getRowData?.distribution_points_json[0].from_dp === null ||
              getRowData?.distribution_points_json[0].from_dp === undefined) &&
            "0": "",
        };
      }

      // const newRoleData = {
      //   Sendacroleid: "",
      //   "Supplier code": getRowData?.prophets[0]?.prophet_code?.trim(),
      //   Description: "",
      //   "Supplier Code Supplier Name": getRowData.company_name,
      //   Type: "",
      //   "Type Description": getRowData?.["role names"],
      //   "Supplier code Global gap number": getRowData?.global_gap_number,
      //   "Created timestamp": "",
      //   Active: "",
      // };

      const newRoleData = multipleSendRoleOnRoleNums(
        getRowData,
        getRowData?.role_num
      );

      const extractedSendacGroup = extractSendacGroup(
        getRowData?.supplier_group
      );

      let sort_code = "";
      let account_number = "";
      let swiftBicCode = "";
      let iban = "";

      const swiftBicRegex =
        /^([A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}|[A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}[A-Z0-9]{3})$/;

      if (swiftBicRegex.test(getRowData?.sort_bic) && getRowData?.has_iban) {
        sort_code = "000000";
        account_number = getRowData?.account_number?.slice(-8);
        swiftBicCode = getRowData?.sort_bic;
        iban = getRowData?.account_number;
      } else if (
        !getRowData?.has_iban &&
        swiftBicRegex.test(getRowData?.sort_bic)
      ) {
        sort_code = "000000";
        account_number = getRowData?.account_number;
        swiftBicCode = getRowData?.sort_bic;
      } else {
        sort_code = getRowData?.sort_bic;
        account_number = getRowData?.account_number;
      }

      const bankac = {
        "Supplier code": getRowData?.prophets[0]?.prophet_code?.trim(),
        "Record id": "",
        "Bank sort code": sort_code,
        "Account number": account_number,
        "Country code":
          getRowData?.country_code == "UK" ? "GB" : getRowData?.country_code,
        "Account holder": getRowData.company_name,
        "Currency number": currencyId,
        "BACS currency": getRowData?.bacs_currency_code,
        "Address Line 1": "",
        "Address Line 2": "",
        "BIC/Swift address": swiftBicCode,
        "Internation bank reference code": iban,
        "Account user id": "",
        "Post code": "",
      };

      const senbnk = {
        "Supplier code": "",
        Bankacid: "",
        "Header bank record id": "",
        "Intermediary bank account id": "",
        "Intermediary bank account id Internation bank reference code": "",
      };

      // Parse and map contacts
      const contacts = extractContacts(
        getRowData?.prophets[0]?.prophet_code?.trim(),
        getRowData.contacts_json,
        getRowData?.company_name
      );

      const newOrganizationData = {
        "Organization ID": "",
        "Organization Name": getRowData?.prophets[0]?.prophet_code
          ? getRowData?.prophets[0]?.prophet_code?.trim()
          : "",
      };

      const newOrganizationRoleData = {
        "Organization ID": "",
        "Organization Code": "",
        "Role Type ID": "",
        Selected: "",
        "Organisation ID": "",
        "Role Type ID": "",
        "Contact ID": "",
        "Contact ID Email Address": "",
        "Contact ID Telephone": "",
        "Contact ID Fax": "",
      };

      const sheetSuppliersId = {
        id: getRowData?.id,
        supplierName: getRowData?.company_name,
        isEmergencyAndFinanceNotComplete:
          getRowData?.isEmergencyAndFinanceNotComplete,
        supplierCode: getRowData?.prophets[0]?.prophet_code
          ? getRowData?.prophets[0]?.prophet_code?.trim()
          : "",
      };

      if (isSelected) {
        setSupplierActiveData((prev) => [...prev, newSupplierActiveData]);
        setRoleData((prev) => [...prev, ...newRoleData]);
        setContactData((prev) => [...prev, ...contacts]);
        setOrganizationData((prev) => [...prev, newOrganizationData]);
        setOrganizationRoleData((prev) => [...prev, newOrganizationRoleData]);
        setSheetSupplierId((prev) => [...prev, sheetSuppliersId]);
        setSendacGroupData((prev) => [...prev, ...extractedSendacGroup]);
        setBankAc((prev) => [...prev, bankac]);
        setSenBnk((prev) => [...prev, senbnk]);
        if (Object.keys(UlpFil).length > 0) {
          setUlpFilData((prev) => [...prev, UlpFil]);
        }

        setMultipleFilterISSData((prev) => [...prev, filteredISSExportData]);
      } else {
        setMultipleFilterISSData((prev) =>
          prev.filter((item) => item.id !== getRowData.id)
        );
        setSupplierActiveData((prev) =>
          prev.filter(
            (item, index) =>
              index === 0 ||
              item["Supplier code"] !==
                getRowData?.prophets[0]?.prophet_code?.trim()
          )
        );
        setUlpFilData((prev) =>
          prev.filter(
            (item, index) =>
              index === 0 ||
              item["Service Supplier Code"] !==
                getRowData?.prophets[0]?.prophet_code?.trim()
          )
        );
        setRoleData((prev) =>
          prev.filter(
            (item, index) =>
              index === 0 ||
              item["Supplier code"] !==
                getRowData?.prophets[0]?.prophet_code?.trim()
          )
        );
        setContactData((prev) =>
          prev.filter((item, index) => {
            if (contacts.length > 0) {
              return (
                index === 0 ||
                item["Supplier code"] !== contacts[0]["Supplier code"]
              );
            } else {
              return true;
            }
          })
        );
        setOrganizationData((prev) =>
          prev.filter(
            (item, index) =>
              index === 0 ||
              item["Organization Name"] !==
                getRowData?.prophets[0]?.prophet_code?.trim()
          )
        );
        setOrganizationRoleData((prev) =>
          prev.filter(
            (item, index) => index === 0 || item["Organization ID"] !== ""
          )
        );
        setSendacGroupData((prev) =>
          prev.filter(
            (item, index) =>
              index === 0 ||
              item["Description"] !== extractedSendacGroup[0]?.Description
          )
        );
        setBankAc((prev) =>
          prev.filter(
            (item, index) =>
              index === 0 ||
              item["Supplier code"] !==
                getRowData?.prophets[0]?.prophet_code?.trim()
          )
        );
        setSenBnk((prev) =>
          prev.filter(
            (item, index) => index === 0 || item["Supplier code"] !== ""
          )
        );
        setSheetSupplierId((prev) =>
          prev.filter(
            (item, index) => index === 0 || item["id"] !== getRowData?.id
          )
        );
      }
      setSupplierCheckedValue(
        supplierActiveData.length > 0 ||
          roleData.length > 0 ||
          contactData.length > 0 ||
          organizationData.length > 0 ||
          organizationRoleData.length > 0 ||
          bankAc.length > 0 ||
          senBnk.length > 0 ||
          sendacGroupData.length > 0 ||
          ulpFilData.length > 0 ||
          multipleFilterISSData.length > 0
      );
    } else {
      if (event.node.selected) {
        if (doesContainCancelledSupplier) {
          setContainsCancelledSupplier(true);
          return;
        }
        setIncompleteToast(true);
        setTimeout(() => {
          setIncompleteToast(false);
        }, 3000);
      }
    }
  };

  const clearFiltersHandler = () => {
    setRowData(allRowData);
    setLoading(false);
    setIsFiltered(false);
    setIsFilteredName("");
    setProphetId("");
  };

  return (
    <>
      <ToastContainer limit={1} />
      <Layout userData={userData} blockScreen={blockScreen}>
        <div className="mr-20 md:mr-12 lg:mr-14">
          <div className="flex flex-row md:flex-col lg:flex-row justify-between">
            <div className="flex items-center">
              <div className="flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2">
                <div className="flex">
                  <input
                    id="default-checkbox"
                    type="radio"
                    value="default"
                    checked={selectedStatus === "default"}
                    className="w-5 h-5 text-blue border-theme-blue2 rounded"
                    onChange={handleFilterToggle}
                  />
                  <label htmlFor="default-checkbox" className="p-0 ml-3 labels">
                    Default
                  </label>
                </div>
              </div>
              <div className="flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2">
                <div className="flex">
                  <input
                    id="export-checkbox"
                    type="radio"
                    value="Exported"
                    checked={selectedStatus === "Exported"}
                    className="w-5 h-5 text-blue border-theme-blue2 rounded"
                    onChange={handleFilterToggle}
                  />
                  <label htmlFor="export-checkbox" className="p-0 ml-3 labels">
                    Exported
                  </label>
                </div>
              </div>
              <div className="flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2">
                <div className="flex">
                  <input
                    id="completed-radio"
                    type="radio"
                    value="Completed"
                    checked={selectedStatus === "Completed"}
                    className="w-5 h-5 text-blue border-theme-blue2 rounded"
                    onChange={handleFilterToggle}
                  />
                  <label htmlFor="completed-radio" className="p-0 ml-3 labels">
                    Completed
                  </label>
                </div>
              </div>
              <div className="flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2">
                <div className="flex">
                  <input
                    id="cancelled-radio"
                    type="radio"
                    value="Cancelled"
                    checked={selectedStatus === "Cancelled"}
                    className="w-5 h-5 text-blue border-theme-blue2 rounded"
                    onChange={handleFilterToggle}
                  />
                  <label htmlFor="cancelled-radio" className="p-0 ml-3 labels">
                    {" "}
                    Cancelled
                  </label>
                </div>
              </div>

              {isFiltered && (
                <>
                  <span className="p-3 py-1 flex items-center capitalize ml-5">
                    <b className="mr-3">Filtered On: </b> {isFilteredName}
                  </span>
                  <button
                    type="button"
                    onClick={clearFiltersHandler}
                    className="flex h-[20px] border bg-red-500 text-white border-red-500 button rounded-md items-center !px-1 !py-1 ml-1"
                  >
                    <FontAwesomeIcon
                      icon={faClose}
                      className="fw-bold"
                      size="lg"
                    />
                  </button>
                </>
              )}
            </div>
            <div className="flex items-center gap-6">
              <label className="relative block w-[47vh] text-gray-400 mt-0 pt-0">
                <span className="absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black">
                  <FontAwesomeIcon icon={faSearch} className="fw-bold" />
                </span>
                <input
                  type="text"
                  id="filter-text-box"
                  placeholder="Search"
                  onInput={onFilterTextBoxChanged}
                  className="block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none"
                />
              </label>
              <button
                onClick={() => exportFilteredData()}
                className=" border text-skin-primary border-skin-primary button rounded-md items-center !py-1"
                disabled={exportDisabled ? true : false}
              >
                Export
              </button>
              {userData.email == "<EMAIL>" && (
                <button
                  onClick={deleteAll}
                  className="border text-skin-primary border-skin-primary button rounded-md items-center !py-1"
                >
                  Delete
                </button>
              )}
              <div className="flex items-center">
                <Link href="/supplier/add">
                  <button className="button border border-skin-primary bg-skin-primary text-white rounded-md whitespace-nowrap">
                    Add Supplier
                  </button>
                </Link>
              </div>
            </div>
          </div>
          <div className="my-5">
            {loading ? (
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "100vh",
                }}
              >
                <ThreeCircles
                  color="#002D73"
                  height={50}
                  width={50}
                  visible={true}
                  ariaLabel="oval-loading"
                  secondaryColor="#0066FF"
                  strokeWidth={2}
                  strokeWidthSecondary={2}
                />
              </div>
            ) : (
              <>
                <div
                  className="relative ag-theme-alpine !rounded-md"
                  style={{ height: "calc(100vh - 151px)" }}
                >
                  <AgGridReact
                    rowData={rowData}
                    ref={gridRef}
                    columnDefs={columnDefs}
                    defaultColDef={defaultColDef}
                    suppressRowClickSelection
                    rowSelection="multiple"
                    pagination={true}
                    paginationPageSize={pageSize}
                    onPageSizeChanged={handlePageSizeChange}
                    tooltipShowDelay={0}
                    tooltipHideDelay={1000}
                    onGridReady={handleGridReady}
                    onRowSelected={handleCheckboxEvent}
                    gridOptions={gridOptions}
                  />
                  <div className="flex justify-start mt-2 pagination-style">
                    <label
                      htmlFor="page-size-select pagination"
                      className="inputs"
                    >
                      Show{" "}
                      <select
                        id="page-size-select"
                        onChange={handlePageSizeChange}
                        value={pageSize}
                        className="focus:outline-none"
                      >
                        <option value={10}>10</option>
                        <option value={15}>15</option>
                        <option value={25}>25</option>
                        <option value={50}>50</option>
                        <option value={100}>100</option>
                      </select>{" "}
                      Entries
                    </label>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </Layout>
      
      {/* Modal components remain the same */}
      <Transition appear show={isOpenOption} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={setIsOpenOption}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-white bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                  <div className="bg-white w-[500px]">
                    <div className="flex w-full bg-skin-primary h-[40px] items-center justify-between">
                      <h2 className="text-white items-center font-poppinsemibold pl-4 text-[20px]">
                        Select the export type
                      </h2>
                      <FontAwesomeIcon
                        icon={faClose}
                        className="pr-4 text-white cursor-pointer"
                        onClick={closeOptionModal}
                      />
                    </div>
                    <div className="flex justify-around items-center px-5">
                      <div className="flex items-center pl-0 pt-5">
                        <input
                          name="exportType"
                          id="internalExport"
                          type="radio"
                          className="mr-4"
                          value="internalExport"
                          checked={selectedExportType === "internalExport"}
                          onChange={handleExportType}
                        />
                        <label
                          className={`font-poppinsregular text-[16px] text-charcoal-gray`}
                          htmlFor="internalExport"
                        >
                          Internal
                        </label>
                      </div>
                      <div className="flex items-center pl-4 pt-5">
                        <input
                          type="radio"
                          name="exportType"
                          id="ISS"
                          className="mr-4"
                          value="ISS"
                          checked={selectedExportType === "ISS"}
                          onChange={handleExportType}
                        />
                        <label
                          htmlFor="ISS"
                          className={`font-poppinsregular text-[16px] text-charcoal-gray`}
                        >
                          ISS
                        </label>
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-center pb-4 pr-4 mt-2">
                    <button
                      onClick={exportFilteredData}
                      disabled={!selectedExportType}
                      className="font-circularstdbook rounded-md w-[100px] p-1 leading-5 mt-1 py-2 text-center hover:opacity-80 bg-skin-primary text-white"
                    >
                      Select
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
      
      <Transition appear show={emailStatusPopup} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeEmailPopup}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                  <div className="relative bg-white rounded-lg shadow">
                    <div className="flex items-start justify-between p-8 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                        <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                          <FontAwesomeIcon icon={faInfo} />{" "}
                        </span>{" "}
                        Status Message
                      </h3>
                      <button
                        onClick={closeEmailPopup}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                        data-modal-hide="default-modal"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />{" "}
                      </button>
                    </div>
                    <div className="p-8 py-0 space-y-6">
                      <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                        {popupMessage}
                      </p>
                    </div>
                    <div className="flex items-end p-6 space-x-2 justify-end">
                      <button
                        onClick={closeEmailPopup}
                        data-modal-hide="default-modal"
                        type="button"
                        className="border text-dark-gray focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Ok
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default suppliers;

export const getServerSideProps = async (context) => {
  try {
    // Use secure session validation
    const sessionId = context.req.cookies.thl_session;

    if (!sessionId) {
    return {
      redirect: {
          destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
        permanent: false,
      },
    };
  }

    // Validate session with our backend API
    const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8081';
    
    try {
      const response = await fetch(`${apiBase}/api/auth/me`, {
        method: 'GET',
        headers: {
          'Cookie': `thl_session=${sessionId}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        // Session invalid or expired - redirect to login
        return {
          redirect: {
            destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
            permanent: false,
          },
        };
      }

      const { user } = await response.json();

  return {
    props: {
          userData: user,
        },
      };

    } catch (fetchError) {
      console.error('Session validation failed:', fetchError);
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
          permanent: false,
        },
      };
    }

  } catch (error) {
    console.error('Authentication error:', error);
    return {
      redirect: {
        destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
        permanent: false,
      },
    };
  }
};
