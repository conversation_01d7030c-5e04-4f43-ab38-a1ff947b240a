"use strict";

const utils = require("../utils");
const config = require("../../config");
const sql = require("mssql");
const logger = require("../../utils/logger");
const { forEach } = require("lodash");
const { sendEmail } = require("../../utils/email");

const createNewSubProductCode = async (productsData, productId = null) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const result = await pool
      .request()
      .input("code", sql.VarChar, productsData.newSubProdCode)
      .input(
        "master_product_code",
        sql.VarChar,
        String(
          productsData.masterProductCode[0]?.code ||
            productsData[0]?.master_product_code_code
        )
      )
      .query(sqlQueries.createNewSubProductCode);

    logger.info({
      username: productsData.username ?? productsData.actionedByName,
      type: "success",
      description: `User with email ${
        productsData.useremail ?? productsData.actionedByEmail
      } created new sub product code  in request no ${
        productsData.request_no
      }, and name of originator ${productsData.nameOfOriginator}`,
      item_id: productId ?? null,
      module_id: 2,
    });
    return result;
  } catch (error) {
    logger.error({
      username: productsData.username,
      type: "error",
      description: `User with email ${productsData.useremail} failed to create sub product code: ${error.message}`,
      item_id: null,
      module_id: 2,
    });
    console.error(error);
    return error.message;
  }
};

const getCreatedNewSubProductCode = async (
  productsData,
  requestNumber = null
) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const result = await pool
      .request()
      .input("code", sql.VarChar, productsData.newSubProdCode)
      .input("code", sql.VarChar, productsData[0]?.master_product_code_code)
      .query(sqlQueries.getNewCreatedSubproductcode);
  } catch (error) {
    logger.error({
      username: productsData.username,
      type: "error",
      description: `User with email ${productsData.useremail} failed to create sub product code: ${error.message}`,
      item_id: null,
      module_id: 2,
    });
    console.error(error);
    return error.message;
  }
};



const getOrderRequests = async () => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("ppeConsumables");
    // console.log("company", company, type_id);
    const { recordset } = await pool
      .request()
      // .input("company", sql.VarChar, company)
      // .input("type_id", sql.Int, type_id)
      // .input("fetch_all", sql.Bit, company)
      .query(sqlQueries.getAllRequest);
    return recordset;
  } catch (error) {
    console.log(error);
    return error.message;
  }
};


const getProducts = async () => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("ppeConsumables");
    // console.log("company", company, type_id);
    const { recordset } = await pool
      .request()
      // .input("company", sql.VarChar, company)
      // .input("type_id", sql.Int, type_id)
      // .input("fetch_all", sql.Bit, company)
      .query(sqlQueries.getAllProducts);
    return recordset;
  } catch (error) {
    console.log(error);
    return error.message;
  }
};

const addProduct = async (productData) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("ppeConsumables");
    console.log("productData", productData);

    const result = await pool
      .request()
      .input("ProductName", sql.VarChar, productData.ProductName)
      .input("ProductCode", sql.VarChar, productData.ProductCode)
      .input("TypeID", sql.Int,productData.TypeID)
      .input("IsSizeRequired", sql.Bit,productData.IsSizeRequired)
      .input("SizeLabel", sql.VarChar, productData.SizeLabel)
      .input("PackageQuantity", sql.VarChar, productData.PackageQuantity)
      .input("Comments", sql.VarChar, productData.Comments)
      .input("SizeIDs", sql.VarChar, productData.SizeIDs) // Comma-separated string of Size IDs
      .input("namePrintable", sql.Bit,productData.NamePrintable) // Comma-separated string of Size IDs
      .input("NewSizeNames", sql.VarChar,productData.NewSizeNames) // Comma-separated string of Size IDs
      .query(sqlQueries.addProduct);

    return result.recordset[0]; // Assuming the stored procedure returns the new product ID
  } catch (error) {
    console.error("Error adding product:", error);
    return { error: error.message };
  }
};

module.exports = {
  getOrderRequests,
  getProducts,
  addProduct,
}