import { createContext, useContext, useState, useEffect } from 'react';

const SecureThemeContext = createContext();

export const SecureThemeProvider = ({ children, initialTheme = '#022D71' }) => {
  const [themeColor, setThemeColor] = useState(initialTheme);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize theme from session data
  useEffect(() => {
    const initializeTheme = async () => {
      try {
        const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8081';
        // Get theme from session via API call
        const response = await fetch(`${apiBase}/api/auth/me`, {
          method: 'GET',
          credentials: 'include',
        });

        if (response.ok) {
          const { user } = await response.json();
          if (user?.theme) {
            setThemeColor(user.theme);
          }
        }
      } catch (error) {
        console.error('Error loading theme:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeTheme();
  }, []);

  // Apply theme to CSS variables
  useEffect(() => {
    if (typeof window !== "undefined" && themeColor) {
      const existingStyleElement = document.getElementById("theme-style");
      if (existingStyleElement) {
        existingStyleElement.remove();
      }

      const $style = document.createElement("style");
      $style.id = "theme-style";
      document.head.appendChild($style);

      // Convert hex to RGB
      const getRGBColor = (hex, type) => {
        let color = hex.replace(/#/g, "");
        var r = parseInt(color.substr(0, 2), 16);
        var g = parseInt(color.substr(2, 2), 16);
        var b = parseInt(color.substr(4, 2), 16);
        return `--color-${type}: ${r}, ${g}, ${b};`;
      };

      const getAccessibleColor = (hex) => {
        let color = hex.replace(/#/g, "");
        var r = parseInt(color.substr(0, 2), 16);
        var g = parseInt(color.substr(2, 2), 16);
        var b = parseInt(color.substr(4, 2), 16);
        var yiq = (r * 299 + g * 587 + b * 114) / 1000;
        return yiq >= 128 ? "#000000" : "#FFFFFF";
      };

      const primaryColor = getRGBColor(themeColor, "primary");
      const textColor = getRGBColor(getAccessibleColor(themeColor), "a11y");

      $style.innerHTML = `:root {${primaryColor} ${textColor}}`;
    }
  }, [themeColor]);


  return (
    <SecureThemeContext.Provider value={{ 
      themeColor, 
      setThemeColor,
      isLoading 
    }}>
      {children}
    </SecureThemeContext.Provider>
  );
};

export const useSecureTheme = () => {
  const context = useContext(SecureThemeContext);
  if (!context) {
    throw new Error('useSecureTheme must be used within a SecureThemeProvider');
  }
  return context;
}; 