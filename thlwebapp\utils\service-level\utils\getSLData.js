import { apiConfig } from "@/services/apiConfig";
import Cookies from "js-cookie";
import { toast } from "react-toastify";

export async function getSLData(url) {
 
  return await fetch(`${apiConfig.serverAddress}servicelevel/${url}`, {
    method: "GET",
    credentials: "include",
  })
    .then(async (res) => {
     
      if (res.status === 400) {
        toast.error(
          "There was an error with your request. Please check your data and try again."
        );
        return [];
      } else if (res.status === 401) {
        toast.error("Your session has expired. Please log in again.");
        return null;
      }
      if (res.status === 200) {
        return res.json();
      }
      throw new Error("Failed to fetch data");
    })
    .catch((error) => {
      console.log(error);
    });
}
