DECLARE @CurrentDate DATE;

DECLARE @CurrentQuarter INT;

DECLARE @currentWeek INT;
DECLARE @currentCalendarYear INT;
DECLARE @currentStartWeek DATE;

DECLARE @CurrentFinancialYear INT;
SET @CurrentFinancialYear = CASE 
    WHEN MONTH(GETDATE()) >= 10 THEN YEAR(GETDATE()) + 1
    ELSE YEAR(GETDATE())
END;
SET @currentCalendarYear = YEAR(GETDATE());

SET @CurrentDate = 
    CASE 
        WHEN @financialYear = @CurrentFinancialYear THEN GETDATE() -- Use current date if year is current
        ELSE CONVERT(DATE, CAST(@financialYear - 1 AS CHAR(4)) + '1001', 112) -- 1 Oct of the input year if it's a past year
    END;

SELECT
    @currentWeek = fiscalweek
FROM
    FLR_DEV_TEST_off_BI_lookup.dbo.off_cal_start_end_week
WHERE
    calendar_name = 'OFF Financial Calendar'
    AND fiscalyear = @CurrentFinancialYear
    AND GETDATE() BETWEEN startweek
    AND endweek;


SELECT
    @CurrentQuarter = fiscalquarter,
    @currentStartWeek = startweek
FROM
    FLR_DEV_TEST_off_BI_lookup.dbo.off_cal_start_end_week
WHERE
    calendar_name = 'OFF Financial Calendar'
    AND fiscalyear = @financialYear
    AND @CurrentDate BETWEEN startweek
    AND endweek;

SELECT
    [fiscalyear],
    [fiscalquarter],
    [fiscalweek],
    FORMAT(startweek, 'yyyy-MM-dd') AS startweek,
    FORMAT(endweek, 'yyyy-MM-dd') AS endweek,
    @CurrentQuarter AS currentQuarter,
    @currentWeek AS currentWeek,
@currentCalendarYear AS currentCalendarYear,
    FORMAT(@currentStartWeek, 'yyyy-MM-dd') AS currentStartWeek
FROM
    FLR_DEV_TEST_off_BI_lookup.dbo.off_cal_start_end_week
WHERE
    calendar_name = 'OFF Financial Calendar'
    AND ((fiscalyear = @financialYear
    AND fiscalquarter = @CurrentQuarter) OR (@CurrentQuarter = 4 AND fiscalyear = @financialYear + 1
    AND fiscalquarter = '1'))
ORDER BY
    startweek;