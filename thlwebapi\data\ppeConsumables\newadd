BEGIN TRY
    BEGIN TRANSACTION;

    -- A. Insert the main product details
    INSERT INTO [Iss_ppe_consumables_portal].[dbo].[products] (
        [name],
        [product_code],
        [type_id],
        [size_required],
        [size_label],
        [quantity],
        [comments],
        [is_active],
        [name_printable]
    )
    VALUES (
        @ProductName,
        @ProductCode,
        @TypeID,
        @IsSizeRequired,
        @SizeLabel,
        @PackageQuantity,
        @Comments,
        1,
        @namePrintable
    );

    -- B. Get the ID of the newly created product
    DECLARE @NewProductID INT = SCOPE_IDENTITY();

    -- C. Handle sizes if required
    IF (@IsSizeRequired = 1)
    BEGIN
        -- Example: @NewSizeNames = 'Small,Medium,Large'
        ;WITH NewSizes AS (
            SELECT LTRIM(RTRIM(value)) AS SizeName
            FROM STRING_SPLIT(@NewSizeNames, ',')
        )
        -- 1. Insert any sizes that don't already exist
        INSERT INTO [Iss_ppe_consumables_portal].[dbo].[product_sizes] ([size], [is_active])
        SELECT ns.SizeName, 1
        FROM NewSizes ns
        LEFT JOIN [Iss_ppe_consumables_portal].[dbo].[product_sizes] ps
            ON ps.[size] = ns.SizeName
        WHERE ps.id IS NULL;  -- Only insert if not already present

        -- 2. Link product to sizes (existing + newly inserted)
        INSERT INTO [Iss_ppe_consumables_portal].[dbo].[product_available_sizes] (
            [product_id],
            [size_id],
            [is_active]
        )
        SELECT
            @NewProductID,
            ps.id,
            1
        FROM NewSizes ns
        INNER JOIN [Iss_ppe_consumables_portal].[dbo].[product_sizes] ps
            ON ps.[size] = ns.SizeName;
    END

    COMMIT TRANSACTION;

    -- Return success message with the new ID
    PRINT 'Product created successfully!';
    PRINT 'New Product ID: ' + CAST(@NewProductID AS VARCHAR);
    SELECT @NewProductID AS NewProductID, 'Success' AS Status;

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    PRINT 'Error occurred: ' + ERROR_MESSAGE();
    SELECT -1 AS NewProductID, 'Failed: ' + ERROR_MESSAGE() AS Status;
END CATCH
