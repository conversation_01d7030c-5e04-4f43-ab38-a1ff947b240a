SELECT
    DISTINCT 
    SPLG.mascode_desc AS [value],
 SPLG.mascode_desc AS [label]
FROM
 FLR_DEV_TEST_dps_BI_lookup.[dbo].sales_dm_stage_product_lvl_grouping SPLG
    JOIN FLR_DEV_TEST_dps_BI_lookup.[dbo].vw_dps_service_lvl_tc_sales_additional ds ON SPLG.prodnum = ds.product_number
    JOIN FLR_DEV_TEST_dps_BI_lookup.[dbo].sales_dm_stage_customer cust_customer on cust_customer.custcode = ds.delcustcode
WHERE
    cust_customer.hocustcode NOT IN ('PPACK', 'REJISS', 'SMOVE')
    AND altfilid > 0
    AND Prov_Order_Flag = 0
    AND delivery_date >= @start_date
    AND delivery_date <= @end_date
    AND cust_customer.category_no = 1
    AND deptcode = 1
 AND (
  @cust_code = 'All Customers'
  OR hocustcode = @cust_code
 )
  
order by [label]
 