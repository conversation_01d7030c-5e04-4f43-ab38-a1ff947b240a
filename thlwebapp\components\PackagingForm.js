import React, { useCallback, useEffect, useRef, useState } from "react";
import Image from "next/image";
import prophet<PERSON>ogo from "@/public/images/ProphetLogo.png";
import Select from "react-select";
import { useLoading } from "@/utils/loaders/loadingContext";
import { toast } from "react-toastify";
import { useRouter } from "next/router";
import Cookies from "js-cookie";
import exportExcelData from "@/utils/exportExcel";
import { Fragment } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faInfo, faXmark } from "@fortawesome/free-solid-svg-icons";
import { apiConfig } from "@/services/apiConfig";
import { debounce, drop } from "lodash";
import { logout } from "@/utils/secureStorage";

const customSelectStyles = {
  // Default style
  control: (base) => ({
    ...base,
    height: "28px",
    minHeight: "28px",
  }),
  // Style when the condition is true
  option: (base, { data }) => ({
    ...base,
    color: data.is_new == true ? "red" : "",
  }),

  valueContainer: (provided, state) => ({
    ...provided,
    height: "28px",
    padding: "0 6px",
  }),

  input: (provided, state) => ({
    ...provided,
    margin: "0px",
  }),
  indicatorSeparator: (state) => ({
    display: "none",
  }),
  indicatorsContainer: (provided, state) => ({
    ...provided,
    height: "28px",
  }),
};

const PackgingForm = ({ dropdowns, userData, packagingData, pageType }) => {
  const [showMandatoryWarning, setShowMandatoryWarning] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [isClicked, setIsClicked] = useState(false);
  const router = useRouter();
  const { setIsLoading } = useLoading();
  const tooltipRef = useRef(null);
  const [isEdit, setIsEdit] = useState(false);
  const [prophetId, setProphetId] = useState("1");
  const [allDropdown, setAlldropDown] = useState({});
  const [nameOfOriginator, setNameOfOriginator] = useState("");
  const [EmailOfOriginator, setEmailOfOriginator] = useState("");
  const [reload, setReload] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isAdminSectionDisabled, setIsAdminSectionDisabled] = useState(true);
  const [isRequestFormDisabled, setIsRequestFormDisabled] = useState(false);
  const [actionIdForGivenDraft, setActionIdForGivenDraft] = useState(1);
  const [validSubmitMode, setValidSubmitMode] = useState(false);

  const [status, setStatus] = useState("Draft");

  const [packagingReason, setPackagingReason] = useState(null);
  const [isValidPackagingReason, setIsValidPackagingReason] = useState("");
  const [isReasonReplacement, setIsReasonReplacement] = useState(false);
  const [isSubProdCodeNewCode, setIsSubProdCodeNewCode] = useState(false);

  const [launchDate, setLaunchDate] = useState(() => new Date());
  const [isValidLaunchDate, setisValidLaunchDate] = useState("");

  const [pacakagingType, setPacakagingType] = useState(null);
  const [isValidPacakagingType, setIsValidPacakagingType] = useState("");

  const [materialType, setMaterialType] = useState(null);
  const [isValidMaterialType, setIsValidMaterialType] = useState("");

  const [materialColour, setMaterialColour] = useState(null);
  const [isValidMaterialColour, setIsValidMaterialColour] = useState("");

  const [recyclableOPRL, setRecyclableOPRL] = useState(null);
  const [isValidRecyclableOPRL, setIsValidRecyclableOPRL] = useState("");

  const [sustainablePaper, setSustainablePaper] = useState(null);
  const [isValidSustainablePaper, setIsValidSustainablePaper] = useState("");

  const [tradingBussiness, setTradingBussiness] = useState(null);
  const [isValidTradingBussiness, setIsValidTradingBussiness] = useState("");

  const [supplierText, setSupplierText] = useState("");
  const [isValidSupplierText, setIsValidSupplierText] = useState("");

  const [packagingComment, setPackagingComment] = useState("");
  const [issAdminComment, setIssAdminComment] = useState("");

  const [componentWeight, setComponentWeight] = useState("");
  const [isValidComponentWeight, setIsValidComponentWeight] = useState("");

  const [packagingDimensions, setPackagingDimensions] = useState("");
  const [isValidPackagingDimensions, setIsValidPackagingDimensions] =
    useState("");

  const [packagingName, setPackagingName] = useState("");
  const [isValidPackagingName, setIsValidPackagingName] = useState("");

  const [recyclableContent, setRecyclableContent] = useState("");
  const [isValidRecyclableContent, setIsValidRecyclableContent] =
    useState(true);

  const [masterProductCode, setMasterProductCode] = useState(null);
  const [isValidMasterProductCode, setIsValidMasterProductCode] = useState("");

  const [endCustomer, setEndCustomer] = useState(null);
  const [isValidEndCustomer, setIsValidEndCustomer] = useState("");

  const [subProdCode, setSubProdCode] = useState(null);
  const [newSubProdCode, setNewSubProdCode] = useState(null);
  const [generatedSubProdCode, setGeneratedSubProdCode] = useState(null);
  const [subProdCodeData, setSubProdCodeData] = useState(null);
  const [isValidSubProdCode, setIsValidSubProdCode] = useState("");

  const [existingPackagingCode, setExistingPackagingCode] = useState(null);
  const [existingPackagingCodeData, setExistingPackagingCodeData] =
    useState(null);
  const [isValidExistingPackagingCode, setIsValidExistingPackagingCode] =
    useState("");

  // const [nameOfOriginatorEmail, setNameOfOriginatorEmail] = useState("");
  const [requestNum, setRequestNum] = useState("");
  const [requestNumber, setRequestNumber] = useState("");
  const [packagingRequestId, setPackagingRequestId] = useState("");

  const [isOpen, setIsOpen] = useState(false);

  const [packagingReasonError, setPackagingReasonError] = useState(false);
  const [launchDateError, setLaunchDateError] = useState(false);
  const [masterProductCodeError, setMasterProductCodeError] = useState(false);
  const [existingPackagingCodeError, setExistingPackagingCodeError] =
    useState(false);
  const [endCustomerError, setEndCustomerError] = useState(false);
  const [packagingNameError, setPackagingNameError] = useState(false);
  const [pacakagingTypeError, setPacakagingTypeError] = useState(false);
  const [materialTypeError, setMaterialTypeError] = useState(false);
  const [materialColourError, setMaterialColourError] = useState(false);
  const [packagingDimensionsError, setPackagingDimensionsError] =
    useState(false);
  const [componentWeightError, setComponentWeightError] = useState(false);
  const [recyclableOPRLError, setRecyclableOPRLError] = useState(false);
  const [sustainablePaperError, setSustainablePaperError] = useState(false);
  const [recyclableContentError, setRecyclableContentError] = useState(false);
  const [tradingBussinessError, setTradingBussinessError] = useState(false);
  const [subProdCodeError, setSubProdCodeError] = useState(false);
  const [suggestedSubProdCodeError, setSuggestedSubProdCodeError] = useState(false);

  //for the empty errors (recyclableContentTouched)
  const [sustainablePaperTouched, setSustainablePaperTouched] = useState(false);
  const [componentWeightTouched, setComponentWeightTouched] = useState(false);
  const [recyclableOPRLTouched, setRecyclableOPRLTouched] = useState(false);
  const [packagingDimensionsTouched, setPackagingDimensionsTouched] =
    useState(false);
  const [materialColourTouched, setMaterialColourTouched] = useState(false);
  const [materialTypeTouched, setMaterialTypeTouched] = useState(false);
  const [pacakagingTypeTouched, setPacakagingTypeTouched] = useState(false);
  const [packagingNameTouched, setPackagingNameTouched] = useState(false);
  const [subProdCodeTouched, setSubProdCodeTouched] = useState(false);
  const [endCustomerTouched, setEndCustomerTouched] = useState(false);
  const [existingPackagingCodeTouched, setExistingPackagingCodeTouched] =
    useState(false);
  const [masterProductCodeTouched, setMasterProductCodeTouched] =
    useState(false);
  const [launchDateTouched, setLaunchDateTouched] = useState(false);
  const [packagingReasonTouched, setPackagingReasonTouched] = useState(false);
  const [tradingBussinessTouched, setTradingBussinessTouched] = useState(false);
  const [recyclableContentTouched, setRecyclableContentTouched] = useState(false); //this is for recyclable content%
  const [suggestedSubCodeTouched, setSuggestedSubCodeTouched] = useState(false); //this is for recyclable content%


  function closeModal() {
    setIsOpen(false);
  }

  //#region validate

  const validatePackagingFormFields = () => {
    let hasErrors = false;

    // Packaging Reason
    if (!packagingReason || packagingReason.length === 0) {
      setPackagingReasonError(true);
      setPackagingReasonTouched(true);
      hasErrors = true;
    } else {
      setPackagingReasonError(false);
    }

    // Launch Date
    if (!launchDate) {
      setLaunchDateError(true);
      setLaunchDateTouched(true);
      hasErrors = true;
    } else {
      setLaunchDateError(false);
    }

    // Master Product Code
    if (!masterProductCode || masterProductCode.length === 0) {
      setMasterProductCodeError(true);
      setMasterProductCodeTouched(true);
      hasErrors = true;
    } else {
      setMasterProductCodeError(false);
    }

    // Existing Packaging Code
    if ((!existingPackagingCode || existingPackagingCode.length === 0) && isReasonReplacement) {
      setExistingPackagingCodeError(true);
      setExistingPackagingCodeTouched(true);
      hasErrors = true;
    } else {
      setExistingPackagingCodeError(false);
    }

    // End Customer
    if (!endCustomer || endCustomer.length === 0) {
      setEndCustomerError(true);
      setEndCustomerTouched(true);
      hasErrors = true;
    } else {
      setEndCustomerError(false);
    }

    // Packaging Name
    if (!packagingName) {
      setPackagingNameError(true);
      setPackagingNameTouched(true);
      hasErrors = true;
    } else {
      setPackagingNameError(false);
    }

    // Packaging Type
    if (!pacakagingType || pacakagingType.length === 0) {
      setPacakagingTypeError(true);
      setPacakagingTypeTouched(true);
      hasErrors = true;
    } else {
      setPacakagingTypeError(false);
    }

    // Material Type
    if (!materialType || materialType.length === 0) {
      setMaterialTypeError(true);
      setMaterialTypeTouched(true);
      hasErrors = true;
    } else {
      setMaterialTypeError(false);
    }

    // Material Colour
    if (!materialColour || materialColour.length === 0) {
      setMaterialColourError(true);
      setMaterialColourTouched(true);
      hasErrors = true;
    } else {
      setMaterialColourError(false);
    }

    // Packaging Dimensions
    if (!packagingDimensions) {
      setPackagingDimensionsError(true);
      setPackagingDimensionsTouched(true);
      hasErrors = true;
    } else {
      setPackagingDimensionsError(false);
    }

    // Component Weight
    if (!componentWeight) {
      setComponentWeightError(true);
      setComponentWeightTouched(true);
      hasErrors = true;
    } else {
      setComponentWeightError(false);
    }

    // Recyclable OPRL
    if (!recyclableOPRL || recyclableOPRL.length === 0) {
      setRecyclableOPRLError(true);
      setRecyclableOPRLTouched(true);
      hasErrors = true;
    } else {
      setRecyclableOPRLError(false);
    }

    // Sustainable Paper
    if (!sustainablePaper || sustainablePaper.length === 0) {
      setSustainablePaperError(true);
      setSustainablePaperTouched(true);
      hasErrors = true;
    } else {
      setSustainablePaperError(false);
    }

    // Recyclable Content
    if (recyclableContent < 0) {
      setRecyclableContentError(true);
      setRecyclableContentTouched(true); // specific for this field
      hasErrors = true;
    } else {
      setRecyclableContentError(false);
    }

    // Trading Business
    if (!tradingBussiness || tradingBussiness.length === 0) {
      setTradingBussinessError(true);
      setTradingBussinessTouched(true);
      hasErrors = true;
    } else {
      setTradingBussinessError(false);
    }

    // Sub Product Code
    if ((!subProdCode || subProdCode.length === 0) && !isReasonReplacement) {
      setSubProdCodeError(true);
      setSubProdCodeTouched(true);
      hasErrors = true;
    } else {
      setSubProdCodeError(false);
    }
    // if ((!subProdCode || subProdCode.length === 0) && !isReasonReplacement) {
    //   setSuggestedSubProdCodeError(true);
    //   setSubProdCodeTouched(true);
    //   hasErrors = true;
    // } else {
    //   setSuggestedSubProdCodeError(false);
    // }

    if (hasErrors) {
      setValidSubmitMode(false);
    } else {
      setValidSubmitMode(true);
    }

    return hasErrors;
  };
  //#endregion validate

  const reasonHandler = (data) => {
    setPackagingReason(data ? data : null);
    if (data === null) {
      setIsValidPackagingReason(false);
      setSubProdCode(null);
      setIsReasonReplacement(false);
      setIsSubProdCodeNewCode(false);
      setNewSubProdCode(null);
      setExistingPackagingCodeTouched(false);
      setExistingPackagingCode(null);
      setPackagingReason(null);
    } else if (data.value == 2) {
      // Replacement
      setIsValidPackagingReason(true);
      setIsSubProdCodeNewCode(false);
      setIsReasonReplacement(true);
      setNewSubProdCode(null);
      setSubProdCode(null);
    } else if (data.value == 3) {
      // Contingency
      setIsValidPackagingReason(true);
      setIsSubProdCodeNewCode(false);
      setIsReasonReplacement(false);
      setNewSubProdCode(null);
      setExistingPackagingCode(null);
      setSubProdCode(null);
      setExistingPackagingCodeTouched(false);
    } else if (data.value == 1) {
      // New
      setIsValidPackagingReason(true);
      setSubProdCode({ value: 0, label: "New Code" });
      setIsSubProdCodeNewCode(true);

      const generatedSubProductCode = `${packagingDimensions ?? "0"}PACK`;
      setGeneratedSubProdCode(generatedSubProductCode);
      setNewSubProdCode(generatedSubProductCode);
      setIsReasonReplacement(false);
      setExistingPackagingCode(null);
      setIsSubProdCodeNewCode(false);
      setExistingPackagingCodeTouched(false);
    }
  }; 
  //#region makeSelectHandler
  const makeSelectHandler = (setValue, setIsValid) => (data) => {
    setValue(data ? data : []);
    setIsValid(!!data);

    //#enfregion makeSelectHandler
  
    if (setValue === setSubProdCode) {
      if (data?.value === 0) {
        setIsSubProdCodeNewCode(true);
      } else {
        setIsSubProdCodeNewCode(false);
      }
    }

  };

  //TODO needs to be used during the code creation process
  const checkIfSubProductCodeExists = useCallback(
    debounce((value) => {
      if (value) {
        if (subProdCodeData?.find((item) => item.label === value)) {
          setSubProductCodeValueAlreadyExists(true);
          console.log("This sub product conde value already exists.");
        } else {
          console.log("no sub product code value exists"); // Clear the message if value is unique
        }
      } else {
        // console.log("value does not exist");
        setSubProductCodeValueAlreadyExists(false); // Handle case when subProductCode is missing
      }
    }, 500),
    [subProdCode]
  );

  //#region sub pro code
  const handleSelectedSubproductCodeDropdown = async (data) => {
    setSubProdCode(data ? data : []), setIsValidSubProdCode(!!data);
    if (data === null) {
      // console.log('Selection cleared!');
      setIsSubProdCodeNewCode(false);
      setNewSubProdCode(null);
    }
    if (data) {
      if (data?.value === 0) {
        // setIsSubProdCodeNewCode(true);
        let generatedSubProductCode = "";
        generatedSubProductCode = `${
          packagingDimensions ? packagingDimensions : "0"
        }PACK`;
        setGeneratedSubProdCode(generatedSubProductCode);
        setNewSubProdCode(generatedSubProductCode);
      } else {
        // console.log("in else statement");
        setIsSubProdCodeNewCode(false);
        setNewSubProdCode("");
      }
      setIsValidSubProdCode(true);
    } else {
      setIsValidSubProdCode(false);
    }
  };

  const textFeildHandler = (setValue, setIsValid) => (data) => {
    const value = data?.target?.value || "";

    setValue(value);
    setIsValid(!!value);
  };
  const trimmedTextFeildHandler = (setValue, setIsValid) => (data) => {
    const trimmedValue = trimInputText(data.target.value);
    setValue(trimmedValue || "");
    setIsValid(!!trimmedValue);
  };

  const makeValidityHandler = (setIsValid) => (value) => {
    setIsValid(!!value);
  };

  const trimInputText = (input) => {
    return input.trim();
  };

  //extract excel
  const extractExcel = async () => {
    const filteredExportData = [
      [
        "SPDFIL",
        {
          "User boolean 1": "NULL",
          "Master product code": masterProductCode?.code,
          "Commodity code": '',
          "User text 4"	: '',
          "User text 5"	: '',
          "User text 6"	: '',
          "Intrastat weight mass": '',
          "Sub product code"	: subProdCode?.label === "New Code" ? newSubProdCode.slice(0,18) : subProdCode?.label.slice(0,18),
          "Mark/variety"	: packagingName.slice(0,20),
          "Count or size"	: packagingDimensions,
          "Sort group number"	: 999,//(MISC)
          "User Text 3"	: '',
          "Product number"	: '',
          "Units in outer"	: 1,
          "Packs per pallet"	: 99999,
          "Sell packs per pallet": 1,
          "Weight of outer"	: componentWeight,//TODO kd to g function
          "Product distribution point"	: 100, //(UNASSIGNED)
          "Buyer"	: 127,
          "Temperature grade"	: 1,//(+3)
          "Temperature grade "	: ' ',//TODO AUTOMATE FROM PREVIOUS
          "Product type"	: 2,//(PACKAGING)
          "Product type "	: ' ',//TODO AUTOMATE FROM PREVIOUS
          "Active"	: 1,//'(YES)'
          "Use by days"	: '0',
          "User Text 1"	: '',
          "User Text 2"	: '',
          "User text 8"	: '',
          "Large description 1"	: materialType?.label,
          "User numeric 5": recyclableContent === 0 ? '0' : recyclableContent,
        },
      ],
    ];

    const export_response = await exportExcelData(
      filteredExportData, //data
      false,              //isInternal
      userData.token,                 //token,
      "iss",              //company, //only iss therefore passing iss directly //
      userData, 
      "",                 //prophet_id
      EmailOfOriginator,  //requestor_email
      false,              //isMultiple
      true,               //isProductExtract
      true,               //onProductSubmit
"The following Packaging Request has been exported for setup in Prophet. Once the setup is complete, please update the request status to 'Completed'.",      issAdminComment,               //productEmailCommentPlaceholder
      requestNumber ? requestNumber : null,  //request_no
      false,               //variety exprot
      true,               //PAckaging exprot
    );
  };

  //#region MasterProduct
  const handleMasterProductChange = (data) => {
    let serverAddress = apiConfig.serverAddress;
    setMasterProductCode(data);
    setMasterProductCodeError(false);
    // if(){
    //   setSubProdCode("");
    //   setExistingPackagingCode("");
    // }

    if (data === null) {
      setIsValidMasterProductCode(false);
    }

    if (data) {
      fetch(
        `${serverAddress}products/get-subproduct-code/${
          data.code
        }?prophetId=${5}`,
        {
          method: "GET",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      )
        .then((res) => {
          if (res.status === 400) {
            toast.error(
              "There was an error with your request. Please check your data and try again."
            );
          } else if (res.status === 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(async() => {
              await logout();
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              router.push(redirectUrl);
            }, 3000);
          }

          if (res.status === 200) {
            return res.json();
          }
          return Promise.reject(res);
        })
        .then((data) => {
          if (data) {
            let subProductsList = [{ value: 0, label: "New Code" }];
            subProductsList.push(...data);
            setExistingPackagingCodeData(data);
            setSubProdCodeData(subProductsList);
          }
        })
        .catch((error) => {
          console.log(error);
        });

      setIsValidMasterProductCode(true);
    } else {
      setIsValidMasterProductCode(false);
    }

    return;
  };
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (tooltipRef.current && !tooltipRef.current.contains(event.target)) {
        setIsClicked(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  async function getData(company, userData) {
    let serverAddress = apiConfig.serverAddress;


    return fetch(`${serverAddress}products/get-request-number/5`, {
      //just passing '1' as it's product type for RM
      method: "GET",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      credentials: "include",
    })
      .then(async (res) => {
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async() => {
            await logout();
            const redirectUrl = `/login?redirect=${encodeURIComponent(
              window.location.pathname
            )}`;
            router.push(redirectUrl);
          }, 3000);
        }
        if (res.status === 200) {
          return res.json();
        }
        throw new Error("Failed to fetch data");
      })
      .then((json) => {
        console.log(json);
        if (json[0].request_number) {
          const nextRequestNumber = parseInt(json[0].request_number) + 1;
          const nextRequetNumberStr = nextRequestNumber.toString();
          const generateSequence =
            nextRequetNumberStr.length <= 6
              ? nextRequetNumberStr.padStart(6, "0")
              : nextRequetNumberStr;
          const existingRequestNumber = generateSequence;
          return existingRequestNumber;
        } else {
          const formattedSequenceNumber = String(0).padStart(5, "0") + 1;
          const newRequestNumber = formattedSequenceNumber;
          return newRequestNumber;
        }
      })
      .catch((error) => {
        return error.message;
      });
  }

  const handleCreateRequest = (data, product_no) => {
    let serverAddress = apiConfig.serverAddress;

    try {
      setLoading(true);
      fetch(`${serverAddress}products/add-update-packaging-request`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(data),
      })
        .then((res) => {
          if (res.status === 200) {
            return res.json();
          } else if (res.status === 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(async() => {
              await logout();
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              router.push(redirectUrl);
            }, 3000);
            return null;
          } else {
            toast.error("Failed to save packaging form.");
            setLoading(false);
          }
        })
        .then((json) => {
          if (json && json.msg) {
            toast.success(json.msg, {
              position: "top-right",
            });
            setTimeout(() => {
              router.replace("/products");
            }, 2000);
          }
        });
    } catch (error) {
      console.error("Failed to save Packaging form:", error);
      setLoading(false);
    }
  };

  //#region save data
  const handleValidate = async (type = null) => {

    // const company_name = Cookies.get("company");
    const company_name = userData?.company;
    const product_no = await getData(company_name, userData);
    // console.log(saveData)


    const saveData = {
      //* api data to be sent to db
      request_no: requestNum ? requestNum : null,
      PKrequest_no: requestNumber ? requestNumber : null,
      packaging_request_id: packagingRequestId ? packagingRequestId : null,
      actionId: type === "submit" ? 2 : actionIdForGivenDraft,
      actionedByEmail: userData.email,
      actionedByName: userData.name, //nameOfOriginator,
      comment: packagingComment,
      company: "ISS", //later
      change_launch_date: launchDate ? launchDate : null,
      component_weight: componentWeight,
      colour_of_material: materialColour?.value,
      dimesion_size: packagingDimensions,
      existing_packaging_code: existingPackagingCode?.value,
      end_customer: endCustomer?.value,
      master_product_code_id: masterProductCode?.value,
      master_product_code_code: masterProductCode?.code,
      paper_from_sustainble_forestry: sustainablePaper?.value,
      prophetId: 5, //later
      packaging_name: packagingName,
      recyclable_to_oprl: recyclableOPRL?.value,
      recyclable_content: recyclableContent,
      supplier: supplierText,
      type_of_packaging: pacakagingType?.value,
      type_of_material: materialType?.value,
      trading_business: tradingBussiness?.value,
      reason_for_request: packagingReason?.value,
      comment: isRequestFormDisabled ? issAdminComment : packagingComment,
      createNewSubProductCodeInDB: subProdCode && subProdCode[0]?.value === 0,
      // && !subProductCodeValueAlreadyExists,
      subProdCode: subProdCode?.value,
      subProdCodeLabel: subProdCode?.value === 0 ? newSubProdCode: subProdCode?.label,
      newSubProdCode: newSubProdCode,
      originatorEmail: EmailOfOriginator === '' ? userData.email : EmailOfOriginator,
    };

    // console.log(saveData, saveData);

    await handleCreateRequest(saveData, product_no);

  };
  //#endregion save data

  useEffect(() => {
    setIsLoading(false);
    setTimeout(function () {
      Cookies.set("rawWarning", true, { expires: 365 });
      Cookies.remove("finishWarning");
    }, 2000);

    setAlldropDown(dropdowns);
    if (pageType == "update") {
      setIsEdit(true);
    }
    if (pageType == "add" && userData) {
      setNameOfOriginator(userData.name);
    }
  }, [0, reload]);

  //#region load data
  // get data and load it
  useEffect(() => {
    setLoading(false);
    const company = Cookies.get("company");
    const isAdmin = [1,5].includes(userData.role_id);//[1, 3].includes(data.actionId) 
    const isSuperAdmin = (userData.role_id === 6)
    const isProcurementDept = userData.department_id === 2;
    let prophetID = 1;
    setProphetId(prophetID);

    if (packagingData || packagingData?.length > 0) {
      const packagingStatus = packagingData[0]?.status;
      if (packagingStatus == "Draft") {
        if (isAdmin) {

          setIsAdminSectionDisabled(true);
          setIsRequestFormDisabled(true);
        // } else if (isSuperAdmin){
        //   setIsAdminSectionDisabled(false);
        //   setIsRequestFormDisabled(false);
        } else if (isProcurementDept || isSuperAdmin) {

          setIsAdminSectionDisabled(true);
          setIsRequestFormDisabled(false);
        }
        else {
          setIsAdminSectionDisabled(true);
          setIsRequestFormDisabled(true);
        }
      } else if (packagingStatus == "Pending Review") {
        if (isAdmin || isSuperAdmin) {
          setIsAdminSectionDisabled(false);
          setIsRequestFormDisabled(true);
        // } else if (isSuperAdmin){
        //   setIsRequestFormDisabled(false);
        //   setIsAdminSectionDisabled(false);
        } else if (isProcurementDept) {
          setIsRequestFormDisabled(true);
          setIsAdminSectionDisabled(true);
        }
        else {
          setIsAdminSectionDisabled(true);
          setIsRequestFormDisabled(true);
        }
      } else if (packagingStatus == "Prophet Setup Completed") {
        setIsAdminSectionDisabled(true);
        setIsRequestFormDisabled(true);
      } else if (packagingStatus == "Cancelled") {
        setIsRequestFormDisabled(true);
        setIsAdminSectionDisabled(true);
      }
      //filter master product 5
      if (packagingData[0]?.master_product_id) {
        handleMasterProductChange({
          value: packagingData[0]?.master_product_id,
          label: packagingData[0]?.master_product_name,
          code: packagingData[0]?.master_product_code,
        });
      }
      if (packagingData[0]?.status) {
        setStatus(packagingData[0]?.status);
      }
      if (packagingData[0]?.request_id) {
        setRequestNum(packagingData[0]?.request_id);
      }
      if (packagingData[0]?.id) {
        setPackagingRequestId(packagingData[0]?.id);
      }
      if (packagingData[0]?.reason) {
        setPackagingReason({
          value: packagingData[0]?.reason,
          label: packagingData[0]?.reason_name,
        });
      }
      if (packagingData[0]?.change_launch_date) {
        setLaunchDate(
          packagingData[0]?.change_launch_date
            ? new Date(packagingData[0]?.change_launch_date).toLocaleDateString(
                "en-CA",
                {
                  year: "numeric",
                  month: "2-digit",
                  day: "2-digit",
                }
              )
            : ""
        );
      }
      if (packagingData[0]?.type_of_packaging) {
        setPacakagingType({
          value: packagingData[0]?.type_of_packaging,
          label: packagingData[0]?.type_of_packaging_name,
        });
      }
      if (packagingData[0]?.packaging_name) {
        setPackagingName(packagingData[0]?.packaging_name);
      }
      if (packagingData[0]?.colour_of_material) {
        setMaterialColour({
          value: parseInt(packagingData[0]?.colour_of_material),
          label: packagingData[0]?.colour_of_material_name,
        });
      }
      if (packagingData[0]?.existing_packaging_code) {
        setExistingPackagingCode({
          value: packagingData[0]?.existing_packaging_code,
          label: packagingData[0]?.existing_packaging_code_name,
        });
      }
      if (packagingData[0]?.end_customer) {
        setEndCustomer({
          value: packagingData[0]?.end_customer,
          label: packagingData[0]?.end_customer_name,
        });
      }
      if (packagingData[0]?.type_of_material) {
        setMaterialType({
          value: packagingData[0]?.type_of_material,
          label: packagingData[0]?.type_of_material_name,
        });
      }
      if (packagingData[0]?.dimension_size) {
        setPackagingDimensions(packagingData[0]?.dimension_size);
      }
      if (packagingData[0]?.component_weight) {
        setComponentWeight(packagingData[0]?.component_weight);
      }
      if (packagingData[0]?.recyclable_content || (packagingData[0]?.recyclable_content === 0) ) {
        setRecyclableContent(packagingData[0]?.recyclable_content);
      }
      if (packagingData[0]?.recyclable_to_oprl) {
        setRecyclableOPRL({
          value: packagingData[0]?.recyclable_to_oprl,
          label: packagingData[0]?.recyclable_to_oprl_name,
        });
      }
      if (packagingData[0]?.paper_from_sustainable_forestry) {
        setSustainablePaper({
          value: packagingData[0]?.paper_from_sustainable_forestry,
          label: packagingData[0]?.paper_from_sustainable_forestry_name,
        });
      }
      if (packagingData[0]?.supplier) {
        setSupplierText(packagingData[0]?.supplier);
      }
      if (packagingData[0]?.trading_business) {
        setTradingBussiness({
          value: packagingData[0]?.trading_business,
          label: packagingData[0]?.trading_business_name,
        });
      }
      if (packagingData[0]?.request_no) {
        setRequestNumber(packagingData[0]?.request_no);
      }
      if (packagingData[0]?.originator_comment) {
        setPackagingComment(packagingData[0]?.originator_comment);
      }
      if (packagingData[0]?.reason && packagingData[0]?.reason == 2) {
        setIsReasonReplacement(true);
      }
      if (packagingData[0]?.status_id) {
        if (packagingData[0]?.status_id == 12) {
          setActionIdForGivenDraft(2);
        } else if (packagingData[0]?.status_id == 20) {
          setActionIdForGivenDraft(3);
        }
      }
      if (packagingData[0]?.completed_by_comment){
        setIssAdminComment(packagingData[0]?.completed_by_comment);
      }

      if (pageType !== "update" && userData) {
        setNameOfOriginator(userData.name);
        setEmailOfOriginator(userData.email);
      } else {
        setNameOfOriginator(packagingData[0]?.originator_name);
        setEmailOfOriginator(packagingData[0]?.originator_email);
      }

      const subProductCodeItemExistsInDropdown =
      dropdowns.subProductCode?.find(
          (item) => item.value == packagingData[0]?.sub_product_code
        );

      if (
        packagingData[0]?.sub_product_code == 0 &&
        !subProductCodeItemExistsInDropdown 
      ) {
 
        setSubProdCode({
          value: 0, 
          label: "New Code" 
        });
        setIsSubProdCodeNewCode(true);
        setNewSubProdCode(packagingData[0]?.sub_product_code_label)
        setIsValidSubProdCode(false);
      } else if (
        packagingData[0]?.sub_product_code &&
        subProductCodeItemExistsInDropdown
      ) {
        setSubProdCode({
              value: packagingData[0]?.sub_product_code,
              label: packagingData[0]?.sub_product_code_name,
            });
        setIsValidSubProdCode(true);
      }
    } 
    else {
      const todayFormatted = new Date().toLocaleDateString("en-CA", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
      });
      setLaunchDate(todayFormatted);
      
    }
  }, [packagingData]);
  //#endregion load data


  const handleWithoutReason = () => {
    router.push("/products");
  };
  return (
    <>
      <div className=" border-b border-[#d3d3d3] relative panel-container bg-white rounded-t-lg w-[93%] lg:w-[95%] 2xl:w-[calc(100%-70px)] p-4 pb-0">
        <h3 className="font-semibold text-base">Request Information</h3>
      </div>
      <div className="flex flex-row relative panel-container bg-white  w-[93%] lg:w-[95%] 2xl:w-[calc(100%-70px)] p-4 pb-0">
        <div className="w-1/2 border-r border-[#d3d3d3] py-4 pr-12 my-4">
          <div className="flex flex-row w-full gap-12">
            <div className="w-1/2 pb-4">
              <label className="labels mb-2 ">Request Number</label>
              <input
                type="text"
                id="filter-text-box"
                disabled="disabled"
                value={requestNumber}
                placeholder="Will be generated on submit"
                className="block w-full px-4 text-gray-500 placeholder-gray-400 disabled:text-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none"
              />
            </div>
            <div className="w-1/2 pb-4">
              <label className="labels mb-2 ">
                Reason for Request <span className="text-red-500">*</span>
              </label>
              <Select
                placeholder="Select..."
                options={allDropdown.PackagingReason}
                isSearchable={true}
                instanceId="selectbox"
                value={packagingReason}
                onChange={reasonHandler
                  // setPackagingReason,
                  // setIsValidPackagingReason
                }
                onBlur={() => {
                  setPackagingReasonTouched(true);
                  setIsValidPackagingReason(!!packagingReason);
                }}
                styles={customSelectStyles}
                className={`reactSelectCustom w-full `}
                isClearable={true}
                isDisabled={isRequestFormDisabled}
              />

              {/* {packagingReason&&
                (<span className="text-red-500">packagingReason: {packagingReason?.value} {packagingReason?.label}</span>)
              }
              // {packagingReasonTouched&&
              //   (<span className="text-red-500">packagingReasonTouched {packagingReasonTouched}</span>)
              // } */}
              {!packagingReason?.value && packagingReasonTouched && (
                <span className="text-red-500">Please select a reason</span>
              )}
            </div>
          </div>
          <div className="flex flex-row w-full gap-12">
            <div className="w-1/2">
              <label className="labels mb-2 ">Name of Originator</label>
              <input
                type="text"
                id="filter-text-box"
                disabled="disabled"
                value={nameOfOriginator}
                className="block w-full px-4 text-gray-500 placeholder-gray-400 disabled:text-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none"
              />
            </div>
            <div className="w-1/2">
              <label className="labels mb-2 ">
                Change/Launch Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                id="filter-text-box"
                value={launchDate} // Format date for input type="date"
                onChange={(e) => {
                  const selectedDate = e.target.value;
                  setLaunchDate(selectedDate);
                  setisValidLaunchDate(!!selectedDate);
                }}
                onBlur={() => {
                  setLaunchDateTouched(true);
                  setisValidLaunchDate(!!launchDate);
                }}
                min={new Date().toISOString().split("T")[0]}
                className={`inputs block w-full px-4 text-gray-500 placeholder-gray-400 disabled:text-gray-400 border rounded-lg disabled:bg-neutral-100  focus:outline-none ${
                  !launchDate && launchDateTouched ? "border-red-500" : ""
                }`}
                disabled={isRequestFormDisabled}
              />

              {!launchDate && launchDateTouched && (
                <span className="text-red-500">Please select a date</span>
              )}
            </div>
          </div>

          <div className="w-full border-b border-[#d3d3d3] mt-8">
            <h3 className="font-semibold text-base">Packaging Information</h3>
          </div>
          <div className="flex flex-row w-full gap-12 mt-6">
            <div className="w-1/2 pb-4">
              <label className="labels mb-2 flex items-center">
                Packaging Master Codes{" "}
                <span className=" ml-1 text-red-500">*</span>
                <Image
                  alt="prophetLogo"
                  src={prophetLogo}
                  className="ml-1 -mt-1 h-6 w-auto"
                  title="SPDFIL-Master Product Code (1)"
                />
              </label>
              <Select
                className={`reactSelectCustom w-full `}
                isClearable={true}
                isSearchable={true}
                instanceId="selectbox"
                placeholder="Select..."
                value={masterProductCode}
                onChange={(selectedOption) => {
                  handleMasterProductChange(selectedOption);
                }}
                onBlur={() => {
                  setMasterProductCodeTouched(true);
                }}
                options={allDropdown.packagingMasterproductCodes}
                styles={customSelectStyles}
                isDisabled={isRequestFormDisabled}
              />

              {!masterProductCode && masterProductCodeTouched && (
                <span className="text-red-500">Please select a code</span>
              )}
            </div>
            <div className="w-1/2 pb-4" disabled>
              <label className="labels mb-2 flex items-center">
                Existing Packaging Codes{" "}
                <span className="ml-1 text-red-500">*</span>
                <Image
                  alt="prophetLogo"
                  src={prophetLogo}
                  className="ml-1 -mt-1 h-6 w-auto"
                  title="SPDFIL-Sub product code (2)"
                />
              </label>
              <Select
                placeholder="Select..."
                value={existingPackagingCode}
                onChange={(selectedOption) => {
                  setExistingPackagingCode(selectedOption);
                  setIsValidExistingPackagingCode(!!selectedOption);
                }}
                // makeSelectHandler(
                // setExistingPackagingCode,
                // setIsValidExistingPackagingCode
                // )}
                onBlur={() => {
                  setExistingPackagingCodeTouched(true);
                  setIsValidExistingPackagingCode(!!existingPackagingCode);
                }}
                // onChange={(selectedOption) => {
                //   setEndCustomer(selectedOption);
                //   setIsValidEndCustomer(!!selectedOption);
                // }}
                // onBlur={() => {
                //   setEndCustomerTouched(true);
                //   setIsValidEndCustomer(!!endCustomer);
                // }}

                options={existingPackagingCodeData || []}
                isSearchable={true}
                instanceId="selectbox"
                isDisabled={
                  !(isReasonReplacement && masterProductCode) ||
                  isRequestFormDisabled
                }
                styles={customSelectStyles}
                className={`reactSelectCustom w-full `}
                isClearable={true}
              />

              {!existingPackagingCode &&
                isReasonReplacement &&
                existingPackagingCodeTouched && (
                  <span className="ml-1 text-red-500">
                    Please select a code
                  </span>
                )}
            </div>
          </div>
          <div className="flex flex-row w-full gap-12 ">
            <div className="w-1/2 pb-4">
              <label className="labels mb-2 flex items-center">
                End Customer <span className="ml-1 text-red-500">*</span>
              </label>
              <Select
                placeholder="Select..."
                value={endCustomer}
                onChange={(selectedOption) => {
                  setEndCustomer(selectedOption);
                  setIsValidEndCustomer(!!selectedOption);
                }}
                onBlur={() => {
                  setEndCustomerTouched(true);
                  setIsValidEndCustomer(!!endCustomer);
                }}
                options={allDropdown.endCustomer}
                isSearchable={true}
                instanceId="selectbox"
                styles={customSelectStyles}
                className={`reactSelectCustom w-full `}
                isClearable={true}
                isDisabled={isRequestFormDisabled}
              />

              {!endCustomer && endCustomerTouched && (
                <span className="text-red-500">Please select a customer</span>
              )}
            </div>
            <div className="w-1/2 pb-4">
              <label className="labels mb-2 flex items-center">
                Packaging Name <span className="ml-1 text-red-500">*</span>
                <Image
                  alt="prophetLogo"
                  src={prophetLogo}
                  className="ml-1 -mt-1 h-6 w-auto"
                  title="SPDFIL-Mark / variety (3)"
                />
              </label>
              <input
                type="text"
                id="filter-text-box"
                maxLength={20}
                value={packagingName}
                onChange={(e) => {
                  const val = e.target.value;
                  setPackagingName(val);
                  if (val.trim() !== "") {
                    setIsValidPackagingName(true);
                  }
                }}
                placeholder="Enter Packaging Name "
                onBlur={(e) => {
                  setPackagingNameTouched(true);
                  const trimmed = e.target.value.trim();
                  setPackagingName(trimmed);
                  setIsValidPackagingName(trimmed !== "");
                }}
                className={`block w-full px-[9px] text-gray-500 placeholder-gray-400 disabled:text-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none ${
                  !packagingName && packagingNameTouched ? "border-red-500" : ""
                }`}
                disabled={isRequestFormDisabled}
              />

              {!packagingName && packagingNameTouched && (
                <span className="text-red-500">Please enter a name</span>
              )}
            </div>
          </div>
          <div className="flex flex-row w-full gap-12">
            <div className="w-1/2 pb-4">
              <label className="labels mb-2 flex items-center">
                Packaging Type <span className="ml-1 text-red-500">*</span>
              </label>

              <Select
                placeholder="Select..."
                options={allDropdown.PackagingType}
                isSearchable={true}
                instanceId="selectbox"
                styles={customSelectStyles}
                value={pacakagingType}
                onChange={(selectedOption) => {
                  setPacakagingType(selectedOption);
                  setIsValidPacakagingType(!!selectedOption);
                }}
                onBlur={() => {
                  setPacakagingTypeTouched(true);
                  setIsValidPacakagingType(!!pacakagingType);
                }}
                className={`reactSelectCustom w-full `}
                isClearable={true}
                isDisabled={isRequestFormDisabled}
              />

              {!pacakagingType && pacakagingTypeTouched && (
                <span className="text-red-500">Please select a type</span>
              )}
            </div>
            <div className="w-1/2 pb-4">
              <label className="flex labels mb-2">
                Type of Material <span className="ml-1 text-red-500">*</span>
                <Image
                  alt="prophetLogo"
                  src={prophetLogo}
                  className="ml-1 -mt-1 h-6 w-auto"
                  title="SPDFIL- Large description 1"
                />
              </label>
              <Select
                placeholder="Select..."
                options={allDropdown.PackagingMaterialTypes}
                isSearchable={true}
                instanceId="selectbox"
                styles={customSelectStyles}
                value={materialType}
                onChange={(selectedOption) => {
                  setMaterialType(selectedOption);
                  setIsValidMaterialType(!!selectedOption);
                }}
                onBlur={() => {
                  setMaterialTypeTouched(true);
                  setIsValidMaterialType(!!materialType);
                }}
                className={`reactSelectCustom w-full `}
                isClearable={true}
                isDisabled={isRequestFormDisabled}
              />

              {!materialType && materialTypeTouched && (
                <span className="text-red-500">Please select a material</span>
              )}
            </div>
          </div>
          <div className="flex flex-row w-full gap-12">
            <div className="w-1/2 pb-4">
              <label className="flex labels mb-2">
                Colour of Material <span className="ml-1 text-red-500">*</span>
                {/* <Image
                  alt="prophetLogo"
                  src={prophetLogo}
                  className="ml-1 -mt-1 h-6 w-auto"
                  title="SPDFIL- Large description 1"
                /> */}
              </label>
              <Select
                placeholder="Select..."
                options={allDropdown.PackagingMaterialColors}
                isSearchable={true}
                instanceId="selectbox"
                styles={customSelectStyles}
                value={materialColour}
                onChange={(selectedOption) => {
                  setMaterialColour(selectedOption);
                  setIsValidMaterialColour(!!selectedOption);
                }}
                onBlur={() => {
                  setMaterialColourTouched(true);
                  setIsValidMaterialColour(!!materialColour);
                }}
                className={`reactSelectCustom w-full `}
                isClearable={true}
                isDisabled={isRequestFormDisabled}
              />

              {!materialColour && materialColourTouched && (
                <span className="text-red-500">Please select a colour</span>
              )}
            </div>
            <div className="w-1/2 pb-4">
              <label className="labels mb-2 flex items-center">
                Dimension of Packaging in mm{" "}
                <span className="ml-1 text-red-500">*</span>
                <Image
                  alt="prophetLogo"
                  src={prophetLogo}
                  className="ml-1 -mt-1 h-6 w-auto"
                  title="SPDFIL-Count or Size"
                />
              </label>
              <input
                type="text"
                maxLength={20}
                placeholder="Enter Dimension of Packaging"
                id="filter-text-box"
                value={packagingDimensions}
                onChange={(e) => {
                  const val = e.target.value.toUpperCase();
                  setPackagingDimensions(val);
                  if (val.trim() !== "") {
                    setIsValidPackagingDimensions(true);
                  }
                }}
                onBlur={(e) => {
                  setPackagingDimensionsTouched(true);
                  const trimmed = e.target.value.trim().toUpperCase();
                  setPackagingDimensions(trimmed);
                  setIsValidPackagingDimensions(trimmed !== "");
                }}
                className={`block w-full px-[9px] text-gray-500 placeholder-gray-400 disabled:text-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none ${
                  !packagingDimensions && packagingDimensionsTouched
                    ? "border-red-500"
                    : ""
                }`}
                disabled={isRequestFormDisabled}
              />

              {!packagingDimensions && packagingDimensionsTouched && (
                <span className="text-red-500">Please enter dimensions</span>
              )}
            </div>
          </div>
          <div className="flex flex-row w-full gap-12 ">
            <div className="w-1/2 pb-4">
              <label className="labels mb-2 flex items-center">
                Component Weight (g)<span className="ml-1 text-red-500">*</span>
                <Image
                  alt="prophetLogo"
                  src={prophetLogo}
                  className="ml-1 -mt-1 h-6 w-auto"
                  title="SPDFIL-Weight of outer (32)"
                />
              </label>
              <input
                type="number"
                max={10000000}
                min={0}
                id="filter-text-box"
                placeholder="Enter Weight"
                maxLength={20}
                value={componentWeight}
                onChange={(e) => {
                  const val =
                    e.target.value > 10000000 ? "10000000" : e.target.value;
                  setComponentWeight(val);
                  if (val.trim() !== "") {
                    setIsValidComponentWeight(true);
                  }
                }}
                onBlur={(e) => {
                  setComponentWeightTouched(true);
                  const trimmed = e.target.value.trim();
                  setComponentWeight(trimmed);
                  setIsValidComponentWeight(trimmed !== "");
                }}
                className={`block w-full px-[9px] text-gray-500 placeholder-gray-400 disabled:text-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none ${
                  !componentWeight && componentWeightTouched
                    ? "border-red-500"
                    : ""
                }`}
                disabled={isRequestFormDisabled}
              />

              {!componentWeight && componentWeightTouched && (
                <span className="text-red-500">Please enter weight</span>
              )}
            </div>
            <div className="w-1/2 pb-4 relative">
              <div className="mb-2 flex items-center relative">
                <span className="labels">
                  Recyclable to OPRL <span className="text-red-500">*</span>
                </span>

                <div className="ml-2 w-5 h-5 relative group">
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsClicked(!isClicked);
                    }}
                    className="w-full h-full"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 512 512"
                      className="w-full h-full fill-current text-gray-600 hover:text-black cursor-pointer"
                      ref={tooltipRef}
                    >
                      <path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM216 336l24 0 0-64-24 0c-13.3 0-24-10.7-24-24s10.7-24 24-24l48 0c13.3 0 24 10.7 24 24l0 88 8 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-80 0c-13.3 0-24-10.7-24-24s10.7-24 24-24zm40-208a32 32 0 1 1 0 64 32 32 0 1 1 0-64z" />
                    </svg>
                  </button>

                  <div
                    className={`absolute top-full left-0 mt-2 w-80 bg-white border border-gray-300 shadow-md rounded-md p-3 text-sm z-10 ${
                      isClicked ? "block" : "hidden group-hover:block"
                    }`}
                  >
                    Recyclability to OPRL Guidelines? For more information visit{" "}
                    <a
                      href="https://oprl.org.uk/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 underline"
                    >
                      Link
                    </a>
                    .
                  </div>
                </div>
              </div>

              <Select
                placeholder="Select..."
                options={allDropdown.RecyclableOPRL}
                isSearchable={true}
                instanceId="selectbox"
                styles={customSelectStyles}
                value={recyclableOPRL}
                onChange={(selectedOption) => {
                  setRecyclableOPRL(selectedOption);
                  setIsValidRecyclableOPRL(!!selectedOption);
                }}
                onBlur={() => {
                  setRecyclableOPRLTouched(true);
                  setIsValidRecyclableOPRL(!!recyclableOPRL);
                }}
                className={`reactSelectCustom w-full `}
                isClearable={true}
                isDisabled={isRequestFormDisabled}
              />

              {!recyclableOPRL && recyclableOPRLTouched && (
                <span className="text-red-500">Please select an option</span>
              )}
            </div>
          </div>
          <div className="flex flex-row w-full gap-12">
            <div className="w-1/2 pb-4">
              <label className="labels mb-2 flex items-center">
                Paper from Sustainable Forestry{" "}
                <span className="ml-1 text-red-500">*</span>
              </label>
              <Select
                placeholder="Select..."
                options={allDropdown.SustainableForestryPaper}
                isSearchable={true}
                instanceId="selectbox"
                styles={customSelectStyles}
                value={sustainablePaper}
                onChange={(selectedOption) => {
                  setSustainablePaper(selectedOption);
                  setIsValidSustainablePaper(!!selectedOption);
                }}
                onBlur={() => {
                  setSustainablePaperTouched(true);
                  setIsValidSustainablePaper(!!sustainablePaper);
                }}
                className={`reactSelectCustom w-full `}
                isClearable={true}
                isDisabled={isRequestFormDisabled}
              />

              {!sustainablePaper && sustainablePaperTouched && (
                <span className="text-red-500">Please select an option</span>
              )}
            </div>
            <div className="w-1/2 pb-4">
              <label className="labels mb-2 flex items-center">
                Recyclable Content %{" "}
                <span className="ml-1 text-red-500">*</span>
              </label>
              <input
                type="number"
                max={100}
                min={0}
                name="recyclable_content"
                placeholder="Enter recyclable content %"
                value={recyclableContent}
                onChange={(e) => {
                  const val = e.target.value > 100 ? "100" : e.target.value;
                  setRecyclableContent(val);

                  if (val.trim() !== "") {
                    setIsValidRecyclableContent(true);
                  }
                }}
                onBlur={(e) => {
                  setRecyclableContentTouched(true);
                  const trimmed = e.target.value.trim();
                  setRecyclableContent(trimmed);
                  setIsValidRecyclableContent(trimmed !== "");
                }}
                className={`block w-full px-[9px] text-gray-500 placeholder-gray-400 disabled:text-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none ${
                  !recyclableContent && recyclableContentTouched ? "border-red-500" : ""
                }`}
                disabled={isRequestFormDisabled}
              />

              {!(recyclableContent > 0) && recyclableContentTouched && (
                <span className="text-red-500">Please enter a percentage</span>
              )}
            </div>
          </div>
        </div>

        <div className="w-1/2 py-4 pl-12 my-4">
          <div className="flex flex-row w-full gap-12">
            <div className="w-1/2 pb-4">
              <label className="labels mb-2 ">Supplier</label>
              <input
                type="text"
                id="filter-text-box"
                maxLength={100}
                placeholder="Enter Supplier"
                value={supplierText}
                onChange={textFeildHandler(
                  setSupplierText,
                  setIsValidSupplierText
                )}
                onBlur={trimmedTextFeildHandler(
                  setSupplierText,
                  setIsValidSupplierText
                )}
                className={`block w-full px-[9px] text-gray-500 placeholder-gray-400 disabled:text-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none ${
                  isRequestFormDisabled ? "disabled:bg-slate-600" : ""
                }
                `}
                disabled={isRequestFormDisabled}
              />
            </div>
            <div className="w-1/2 pb-4">
              <label className="labels mb-2 ">
                Trading Business <span className="text-red-500">*</span>
              </label>
              <Select
                placeholder="Select..."
                options={allDropdown.TradingBusiness}
                isSearchable={true}
                instanceId="selectbox"
                styles={customSelectStyles}
                value={tradingBussiness}
                onChange={(selectedOption) => {
                  setTradingBussiness(selectedOption);
                  setIsValidTradingBussiness(!!selectedOption);
                }}
                onBlur={() => {
                  setTradingBussinessTouched(true);
                  setIsValidTradingBussiness(!!tradingBussiness);
                }}
                className={`reactSelectCustom w-full `}
                isClearable={true}
                isDisabled={isRequestFormDisabled}
              />

              {!tradingBussiness && tradingBussinessTouched && (
                <span className="text-red-500">Please select a business</span>
              )}
            </div>
          </div>
          <div className="flex flex-row w-full gap-12 ">
            <div className="w-1/2 pb-4">
              <label className="labels mb-2 flex items-center">
                Sub Product Code <span className="ml-1 text-red-500">*</span>
                <Image
                  alt="prophetLogo"
                  src={prophetLogo}
                  className="ml-1 -mt-1 h-6 w-auto"
                  title="SPDFIL-Sub product code (2)"
                />
              </label>
              <Select
                placeholder="Select..."
                value={subProdCode}
                onChange={handleSelectedSubproductCodeDropdown}
                onBlur={() => {setSubProdCodeTouched(true);}}
                options={subProdCodeData}
                isSearchable={true}
                instanceId="selectbox"
                styles={customSelectStyles}
                className={`reactSelectCustom w-full ${isFocused} ${
                  subProdCodeError ? "border-red-500" : ""
                }`}
                isClearable={true}
                isDisabled={
                  !packagingReason
                    ? true
                    : !masterProductCode ||
                      isRequestFormDisabled ||
                      isSubProdCodeNewCode ||
                      isReasonReplacement
                }
              />
              {!subProdCode?.label && !isReasonReplacement && packagingReason && subProdCodeTouched && (
                <span className="text-red-500"> Please Select a Code</span>
              )}
            </div>
            <div className="w-1/2 pb-4">
              <label className="labels mb-2 flex items-center">
                Sub Product Code (Suggested){" "}
                <span className="ml-1 text-red-500">*</span>
                <Image
                  alt="prophetLogo"
                  src={prophetLogo}
                  className="ml-1 -mt-1 h-6 w-auto"
                  title="SPDFIL-Sub product code (2)"
                />
              </label>
              <input
                type="text"
                id="filter-text-box"
                maxLength={50}
                value={newSubProdCode || ""}
                onChange={(e) => {
                  setNewSubProdCode(e.target.value.toUpperCase()),
                    setIsValidSubProdCode(true);
                }}
                onBlur={(e) => {
                  let trimmedValue = trimInputText(e.target.value);
                  if (trimmedValue.length > 50) {
                    trimmedValue = trimmedValue.slice(0, 50);
                  }
                  setNewSubProdCode(trimmedValue);
                  setIsValidSubProdCode(trimmedValue);
                  setSuggestedSubCodeTouched(true);
                }}
                disabled={
                  isReasonReplacement ||
                  isRequestFormDisabled 
                  // || !newSubProdCode
                  || subProdCode?.value !== 0
                  // packagingReason?.value === 2
                }
                placeholder="Generated from logic..."
                className={`block w-full px-[9px] text-gray-500 placeholder-gray-400 disabled:text-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none `}
              />
              {!newSubProdCode && !isReasonReplacement && suggestedSubCodeTouched && subProdCode?.value === 0 && (
                <span className="text-red-500"> Please Enter a Code</span>
              )}
            </div>
          </div>
          <div className="flex flex-col w-full pb-4">
            <label className="labels mb-2 flex items-center">Comment</label>
            <textarea
              value={packagingComment}
              onChange={(e) => {
                setPackagingComment(e.target.value);
              }}
              onBlur={(e) => {
                const trimmedValue = trimInputText(e.target.value);
                setPackagingComment(trimmedValue);
              }}
              className={`block w-full p-2 px-3  resize-none  placeholder-gray-400  h-24 border rounded-lg appearance-none  focus:outline-none ${
                isRequestFormDisabled ? " text-gray-500" : ""
              }`}
              disabled={isRequestFormDisabled}
            ></textarea>
          </div>
          <div className="w-full border-b border-[#d3d3d3] mt-8 ">
            <h3 className="font-semibold text-base">ISS Admin Team</h3>
          </div>
          <div className="flex flex-col w-full pb-4 mt-6">
            <label className="labels mb-2 flex items-center">ISS Setup</label>
            <div className="flex flex-row gap-4">
              <div className="flex">
                <input
                  id="iss-completed"
                  name="iss-set-up"
                  type="radio"
                  onClick={() => setActionIdForGivenDraft(3)}
                  value={actionIdForGivenDraft == 3}
                  checked={actionIdForGivenDraft == 3}
                  className="mt-[2px] w-4 h-4 text-skin-a11y bg-gray-100 border-light-gray  focus:ring-blue-500 focus:outline-none accent-skin-primary"
                  disabled={isAdminSectionDisabled}
                />
                <label
                  htmlFor="iss-completed"
                  className="p-0 ml-2 me-5 text-blackcolor"
                  disabled={isAdminSectionDisabled}
                >
                  Completed
                </label>
              </div>
              <div className="flex">
                <input
                  id="iss-to-be-setup"
                  name="iss-set-up"
                  type="radio"
                  onClick={() => setActionIdForGivenDraft(2)}
                  value={true}
                  checked={actionIdForGivenDraft == 2}
                  className="mt-[2px] w-4 h-4 text-skin-a11y bg-gray-100 border-light-gray focus:ring-blue-500 accent-skin-primary"
                  disabled={isAdminSectionDisabled}
                />
                <label
                  htmlFor="iss-to-be-setup"
                  className="p-0 ml-2 me-5 text-blackcolor"
                >
                  To be Setup
                </label>
              </div>
            </div>
          </div>
          <div className="flex flex-col w-full pb-4">
            <label className="labels mb-2 flex items-center">ISS Comment</label>
            <textarea
              value={issAdminComment}
              onChange={(e) => {
                setIssAdminComment(e.target.value);
              }}
              disabled={isAdminSectionDisabled}
              onBlur={(e) => {
                const trimmedValue = trimInputText(e.target.value);
                setIssAdminComment(trimmedValue);
              }}
              className={`block w-full p-2 px-3 resize-none placeholder-gray-400  h-24 border rounded-lg appearance-none  focus:outline-none ${
                !isRequestFormDisabled ? "" : "text-gray-500"
              }`}
            ></textarea>
          </div>
          <div className="flex flex-row justify-between">
            <button
              disabled={
                isAdminSectionDisabled ||
                !(packagingData && packagingData[0]?.status_id === 12)
              }
              onClick={extractExcel}
              className="border border-skin-primary text-skin-primary rounded-md py-1 px-8"
            >
              Extract
            </button>
            <button
              type="button"
              disabled={isAdminSectionDisabled || actionIdForGivenDraft != 3}
              onClick={() => {
                // validatePackagingFormFields();
                // setShowMandatoryWarning(true);
                handleValidate();
                setIsLoading(true);
              }}
              className="border border-skin-primary bg-skin-primary text-white rounded-md py-1 px-8 font-medium"
            >
              Save
            </button>
          </div>
        </div>
      </div>
      <div className="flex flex-row justify-end gap-4 border-t border-[#d3d3d3] pt-4 relative panel-container bg-white rounded-b-lg w-[93%] lg:w-[95%] 2xl:w-[calc(100%-70px)] p-4 ">
        <button
          onClick={handleWithoutReason}
          className="border border-skin-primary text-skin-primary rounded-md py-1 pt-2 px-8"
        >
          Cancel
        </button>
        <button
          className="border border-skin-primary text-skin-primary rounded-md py-1 pt-2 px-8"
          onClick={() => {
            const hasErrors = validatePackagingFormFields();
            setIsOpen(true);
          }}
          disabled={isRequestFormDisabled}
        >
          Save & Exit
        </button>

        <button
          type="button"
          className="flex border border-skin-primary bg-skin-primary text-white rounded-md py-1 pt-2 px-8 font-medium"
          onClick={() => {
            validatePackagingFormFields();
            setShowMandatoryWarning(true);
          }}
          disabled={isRequestFormDisabled}
        >
          Submit
        </button>
      </div>
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                  {/* <!-- Modal content --> */}
                  <div className="relative bg-white rounded-lg shadow">
                    {/* <!-- Modal header --> */}
                    <div className="flex items-start justify-between p-8 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                        <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                          <FontAwesomeIcon icon={faInfo} />{" "}
                        </span>{" "}
                        Warning
                      </h3>
                      <button
                        onClick={closeModal}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                        data-modal-hide="default-modal"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />{" "}
                      </button>
                    </div>
                    {/* <!-- Modal body --> */}
                    <div className="p-6 pt-0">
                      <p className="text-base text-gray-700">
                        {validSubmitMode
                          ? "Are you sure you want to Save & Exit?"
                          : "Mandatory information missing. Do you want to continue?"}
                      </p>
                    </div>
                    {/* <!-- Modal footer --> */}
                    <div className="flex items-end p-6 space-x-2 justify-end">
                      <button
                        onClick={closeModal}
                        data-modal-hide="default-modal"
                        type="button"
                        className="border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Cancel
                      </button>
                      <button
                        onClick={() => {
                          handleValidate("save");
                          setIsLoading(true);
                          closeModal();
                        }}
                        data-modal-hide="default-modal"
                        type="button"
                        className="text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Continue
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition appear show={showMandatoryWarning} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-20"
          onClose={() => setShowMandatoryWarning(false)}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-[40%] transform overflow-hidden rounded-xl bg-white text-left align-middle shadow-xl transition-all">
                  <div className="bg-white rounded-lg shadow">
                    <div className="flex items-start justify-between p-6 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-black-600 items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="w-6 mr-3 text-skin-primary fill-current"
                          viewBox="0 0 512 512"
                        >
                          <path d="M248.4 84.3c1.6-2.7 4.5-4.3 7.6-4.3s6 1.6 7.6 4.3L461.9 410c1.4 2.3 2.1 4.9 2.1 7.5c0 8-6.5 14.5-14.5 14.5l-387 0c-8 0-14.5-6.5-14.5-14.5c0-2.7 .7-5.3 2.1-7.5L248.4 84.3zm-41-25L9.1 385c-6 9.8-9.1 21-9.1 32.5C0 452 28 480 62.5 480l387 0c34.5 0 62.5-28 62.5-62.5c0-11.5-3.2-22.7-9.1-32.5L304.6 59.3C294.3 42.4 275.9 32 256 32s-38.3 10.4-48.6 27.3zM288 368a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zm-8-184c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 96c0 13.3 10.7 24 24 24s24-10.7 24-24l0-96z" />
                        </svg>
                        Warning
                      </h3>
                      <button
                        onClick={() => setShowMandatoryWarning(false)}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />
                      </button>
                    </div>

                    <div className="p-8 py-0 space-y-6">
                      <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                        {validSubmitMode
                          ? "Are you sure you want to submit?"
                          : "Mandatory information missing/incorrect. Please fill out all the details and then submit."}
                      </p>
                    </div>

                    <div className="flex justify-end p-6 space-x-2">
                      <button
                        onClick={() => {
                          setShowMandatoryWarning(false);
                          if (validSubmitMode) {
                            setActionIdForGivenDraft(2);
                            handleValidate("submit", 2);
                            setIsLoading(true);
                          }
                        }}
                        type="button"
                        className={`border border-skin-primary px-6 py-2 font-medium rounded-md ${
                          validSubmitMode
                            ? "  text-white bg-skin-primary"
                            : " text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 text-md  text-center"
                        } `}
                      >
                        {validSubmitMode ? "Yes" : "Ok"}
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default PackgingForm;