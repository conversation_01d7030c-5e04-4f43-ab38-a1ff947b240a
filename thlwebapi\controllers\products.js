"use strict";

const { map } = require("mssql");
const productsData = require("../data/products");
const logger = require("../utils/logger");

const getRequestNumber = async (req, res) => {
  try {
    const product_request_type = req?.params?.product_request_type;
    const requestNumber = await productsData.getRequestNumber(
      product_request_type
    );
    res.send(requestNumber);
  } catch (error) {
    console.error(error);
    res.status(400).send(error.message);
  }
};

const getProductGroups = async (req, res) => {
  try {
    const type = req?.params?.type;
    const product_groups = await productsData.getProductGroups(type);
    res.send(product_groups);
  } catch (error) {
    console.error(error);
    res.status(400).send(error.message);
  }
};

const insertRequestNumber = async (req, res) => {
  try {
    const data = req.body;
    const created = await productsData.createRequestNumber(
    data.company,
    data.request_no,
    data.product_request_type
    );
    res.send(created);
  } catch (error) {
    console.error(error);
    res.status(400).send(error.message);
  }
};

const addRawMaterials = async (req, res) => {
  try {
    const data = req.body;
    const created = await productsData.createRawMaterials(data);
    if (created && created.rowsAffected && created.rowsAffected[0] === 1) {
      if (data.createNewSubProductCodeInDB && data.subProductCode) {
        const newSubProductCodeCreated =
          await productsData.createNewSubProductCode(data);
        if (
          newSubProductCodeCreated &&
          newSubProductCodeCreated.rowsAffected &&
          newSubProductCodeCreated.rowsAffected[0] === 1
        ) {
          return res.status(200).json({
            msg: `Raw materials request ${
              data.isSubmitted ? "submitted" : "created"
            } successfully`,
          });
          // return res.status(200).json({msg:`sub product code saved successfully`})
        } else {
          return res
            .status(500)
            .json({ msg: "Failed to create sub product code" });
        }
      }
      return res.status(200).json({ msg: "Raw material added successfully" });
    } else {
      return res.status(500).json({ msg: "Failed to add raw material" });
    }
  } catch (error) {
    console.error(error);
    res.status(400).send(error.message);
  }
};

const updateRawMaterials = async (req, res) => {
  try {
    const data = req.body;
    const id = req.params.id;

    const created = await productsData.updateRawMaterials(data, id);
    if (created && created.rowsAffected && created.rowsAffected[0] === 1) {
      if (data.createNewSubProductCodeInDB && data.subProductCode) {
        const newSubProductCodeCreated =
          await productsData.createNewSubProductCode(data, id);
        if (
          newSubProductCodeCreated &&
          newSubProductCodeCreated.rowsAffected &&
          newSubProductCodeCreated.rowsAffected[0] === 1
        ) {
          return res.status(200).json({
            msg: `Raw materials request ${
              data.isSubmitted ? "submitted" : "updated"
            } successfully`,
          });
          // return res.status(200).json({msg:`sub product code saved successfully`})
        } else {
          return res
            .status(500)
            .json({ msg: "Failed to create sub product code" });
        }
      }
      return res.status(200).json({
        msg: `Raw materials ${
          data.isSubmitted ? "submitted" : "updated"
        } successfully`,
      });
    } else {
      return res.status(500).json({ msg: "Failed to update raw materials" });
    }
  } catch (error) {
    console.error(error);
    res.status(400).send(error.message);
  }
};

const addFinishedProducts = async (req, res) => {
  try {
    const data = req.body;
    const created = await productsData.createFinishedProducts(data);
    if (created && created.rowsAffected && created.rowsAffected[0] === 1) {
    res.status(200).json({ msg: "Finished product added successfully" });
  } else {
      res.status(500).json({ msg: "Failed to add finished product" });
    }
  } catch (error) {
    console.error(error);
    res.status(400).send(error.message);
  }
};

const addUpdateNV = async (req, res) => {
  try {
    const data = req.body;
    const created = await productsData.addUpdateNewVariety(data);
    if (created && created.rowsAffected && created.rowsAffected[0] === 1) {
    let requestNumber;
    if (data.requestNumber == null) {
      requestNumber = created.recordset.map(
        (item) => `${item.company}${item.request_no}`
      )[0];
    }
    res
      .status(200)
      .json({
        msg: "New variety request submitted successfully",
        requestNumber,
      });
  } else {
    res.status(403).json({ msg: "Failed to add new variety product" });
  }
  } catch (error) {
    console.error(error);
    res.status(400).send(error.message);
  }
};

const updateFinishedProducts = async (req, res) => {
  try {
    const data = req.body;
    const id = req.params.id;

  const created = await productsData.updateFinishedProducts(data, id);
  if (created && created.rowsAffected && created.rowsAffected[0] === 1) {
    res.status(200).json({ msg: "Finished product updated successfully" });
  } else {
    res.status(500).json({ msg: "Failed to update finished products" });
  }
  } catch (error) {
    console.error(error);
    res.status(400).send(error.message);
  }
};

const updateAllDropdownValue = async (req, res) => {
  const dropdownData = req.body;
  const data = {};

  try {
    switch (dropdownData.tableName) {
      case "masterProductCode": {
        data.masterProductCode = await productsData.updateCode(
          dropdownData,
          "masterProductCode",
          "updateMasterProductCode"
        );
        break;
      }
      case "markVariety": {
        data.markVariety = await productsData.updateCode(
          dropdownData,
          "markVariety",
          "updateMarkVariety"
        );
        break;
      }
      case "brand": {
        data.brand = await productsData.updateCode(
          dropdownData,
          "brand",
          "updateBrand"
        );
        break;
      }
      case "caliberSize": {
        data.caliberSize = await productsData.updateCode(
          dropdownData,
          "caliberSize",
          "updateCaliberSize"
        );
        break;
      }
      case "variety": {
        data.variety = await productsData.updateCode(
          dropdownData,
          "variety",
          "updateVariety"
        );
        break;
      }
      case "end_customer": {
        data.endCustomer = await productsData.updateCode(
          dropdownData,
          "end_customer",
          "updateEndCustomer"
        );
        break;
      }
      case "newOuterBoxType": {
        data.newOuterBoxType = await productsData.updateCode(
          dropdownData,
          "newOuterBoxType",
          "updateNewOuterBoxType"
        );
        break;
      }
      case "countryOfOrigin": {
        data.countryOfOrigin = await productsData.updateCode(
          dropdownData,
          "countryOfOrigin",
          "updateCountryOfOrigin"
        );
        break;
      }
      default:
        break;
    }

    res.send(data);
  } catch (error) {
    console.error("error:", error);
    res.status(401).send(error.message);
  }
};

const addAllDropdownValue = async (req, res) => {
  const dropdownData = req.body;
  const data = {
    // masterProductCode: null,
    // markVariety: null,
    // brand: null,
    // caliberSize: null,
    // variety: null,
    // endCustomer: null,
    // newOuterBoxType: null,
    // countryOfOrigin: null
  };

  try {
    if (dropdownData.tableName == "masterProductCode") {
      data.masterProductCode = await productsData.addMasterCode(dropdownData);
    }
    if (dropdownData.tableName == "markVariety") {
      data.markVariety = await productsData.addMarkVariety(dropdownData);
    }
    if (dropdownData.tableName == "brand") {
      data.brand = await productsData.addBrand(dropdownData);
    }
    if (dropdownData.tableName == "caliberSize") {
      data.caliberSize = await productsData.addCaliberSize(dropdownData);
    }
    if (dropdownData.tableName == "variety") {
      data.variety = await productsData.addVariety(dropdownData);
    }
    if (dropdownData.tableName == "end_customer") {
      data.endCustomer = await productsData.addEndCustomer(dropdownData);
    }
    if (dropdownData.tableName == "newOuterBoxType") {
      data.newOuterBoxType = await productsData.addBoxType(dropdownData);
    }
    if (dropdownData.tableName == "countryOfOrigin") {
      data.countryOfOrigin = await productsData.addCountryOfOrigin(
        dropdownData
      );
    }

    res.send(data);
  } catch (error) {
    console.error("error:", error);
    res.status(401).send(error.message);
  }
};

const getProductsDropdowns = async (req, res) => {
  const dropDownList = req.body;
  const prophetId = req.query.prophetId || null;
  const productType = req.query.productType;
  const data = {
    masterProductCode: null,
    packagingMasterproductCodes:null,
    markVariety: null,
    temperatureGrade: null,
    intrastatCommodityCode: null,
    classifiedAllergicTypes: null,
    brand: null,
    caliberSize: null,
    variety: null,
    countryOfOrigin: null,
    endCustomer: null,
    organicCertification: null,
    productType: null,
    newOuterBoxType: null,
    packagingTypes: null,
    punnetOrTrayType: null,
    descriptionOfNBRF: null,
    machineFormat: null,
    reason: null,
    sort_group: null,
    subProductCode: null,
    // Packaging Form
    PackagingType: null,
    RecyclableOPRL: null,
    SustainableForestryPaper: null,
    TradingBusiness: null,
    product_packaging_reason: null,
    product_packaging_material_types: null,
    product_packaging_material_colors: null,
  };

  try {
    if (dropDownList.includes("masterProductCode")) {
      data.masterProductCode = await productsData.masterProductCodeList(
        prophetId,productType
      );
    }
    if (dropDownList.includes("packagingMasterproductCodes")) {
      data.packagingMasterproductCodes = await productsData.packagingMasterproductCodeList(
        prophetId
      );
    }
    if (dropDownList.includes("reasonForRequest")) {
      data.reason = await productsData.reasonList();
    }
    if (dropDownList.includes("markVariety")) {
      data.markVariety = await productsData.markVarietyList();
    }
    if (dropDownList.includes("temperatureGrade")) {
      data.temperatureGrade = await productsData.temperatureGradeList();
    }
    if (dropDownList.includes("sort_group")) {
      data.sort_group = await productsData.sortGroupList();
    }
    if (dropDownList.includes("intrastatCommodityCode")) {
      const intrastatCodes = await productsData.intrastatCommodityCodeList();
      const commodity_codes = intrastatCodes?.map((con, key) => {
        return {
          value: con.id,
          label: `${con.label} ${con.value ? `- ${con.value}` : ""}`,
          code: con.code,
          User_text_4: con.User_text_4,
          User_text_5: con.User_text_5,
          User_text_6: con.User_text_6,
        };
      });
      data.intrastatCommodityCode = commodity_codes;
    }
    if (dropDownList.includes("classifiedAllergicTypes")) {
      data.classifiedAllergicTypes =
        await productsData.classifiedAllergicList();
    }
    if (dropDownList.includes("organicCertification")) {
      data.organicCertification =
        await productsData.organicCertificationList();
    }
    if (dropDownList.includes("productType")) {
      data.productType = await productsData.productTypeList();
    }
    if (dropDownList.includes("brand")) {
      data.brand = await productsData.brandsList();
    }
    if (dropDownList.includes("caliberSize")) {
      data.caliberSize = await productsData.caliberSizeList();
    }
    if (dropDownList.includes("variety")) {
      data.variety = await productsData.varietyList();
    }
    if (dropDownList.includes("countryOfOrigin")) {
      data.countryOfOrigin = await productsData.countryOfOrigin();
    }
    if (dropDownList.includes("endCustomer")) {
      data.endCustomer = await productsData.endCustomersList();
    }
    if (dropDownList.includes("newOuterBoxType")) {
      data.newOuterBoxType = await productsData.newOuterBoxType();
    }
    if (dropDownList.includes("packagingTypes")) {
      data.packagingTypes = await productsData.packagingTypesList();
    }
    // if (dropDownList.includes("punnetOrTrayType")) {
    //   data.punnetOrTrayType = await productsData.punnetOrTrayTypeList();
    // }
    // if (dropDownList.includes("descriptionOfNBRF")) {
    //   data.descriptionOfNBRF = await productsData.descriptionOfNBRFList();
    // }
    if (dropDownList.includes("machineFormat")) {
      data.machineFormat = await productsData.machineFormatList();
    }
    if (dropDownList.includes("subProductCode")) {
      data.subProductCode = await productsData.subProductCodeList(prophetId);
    }
    if (dropDownList.includes("PackagingType")) {
      data.PackagingType = await productsData.PackagingTypeList(prophetId);
    }
    if (dropDownList.includes("RecyclableOPRL")) {
      data.RecyclableOPRL = await productsData.RecyclableOPRLList(prophetId);
    }
    if (dropDownList.includes("SustainableForestryPaper")) {
      data.SustainableForestryPaper = await productsData.SustainableForestryPaperList(prophetId);
    }
    if (dropDownList.includes("TradingBusiness")) {
      data.TradingBusiness = await productsData.TradingBusinessList(prophetId);
    }
    if (dropDownList.includes("PackagingReason")) {
      data.PackagingReason = await productsData.PackagingReasonList(prophetId);
    }
    if (dropDownList.includes("PackagingMaterialTypes")) {
      data.PackagingMaterialTypes = await productsData.PackagingMaterialTypesList(prophetId);
    }
    if (dropDownList.includes("PackagingMaterialColors")) {
      data.PackagingMaterialColors = await productsData.PackagingMaterialColorsList(prophetId);
    }
    res.send(data);
  } catch (error) {
    console.error("error getting products dropdown:", error);
    res.status(400).send(error.message);
  }
};

const getProducts = async (req, res) => {
  try {
    const company = req.params.company;
    const type_id = req.params.type_id;
    const products = await productsData.getProducts(
      company,
      type_id
    );
    res.send(products);
  } catch (error) {
    res.status(400).send(error.message);
  }
};
const getRawMaterialsById = async (req, res) => {
  try {
    const productId = req.params.id;

    const products = await productsData.getRawMaterialsById(productId);
  res.send(products);
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const getFinishedProductsById = async (req, res) => {
  try {
    const productId = req.params.id;
    const products = await productsData.getFinishedProductsById(productId);
    res.send(products);
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const getNVProductById = async (req, res) => {
  try {
    const productId = req.params.id;
    const products = await productsData.getNVProductsById(productId);
    res.send(products);
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const updateStatus = async (req, res) => {
  try {
    const product = req.body;
    const products = await productsData.updateStatus(product);
    if (products) {
      res.status(200).send(products);
    } else {
      res.status(400).send("Product not found");
    }
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const updateUnblock = async (req, res) => {
  try {
    const product = req.body;
    const products = await productsData.updateUnblock(product);
    res.send(products);
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const getSubProductsByMasterCode = async (req, res) => {
  try {
    const master_code = req.params.master_code;
    const prophetId = req.query.prophetId;
    const products = await productsData.getSubProductsByMasterCode(
    master_code,
    prophetId
    );
    res.send(products);
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const getFilteredVarietyNames = async (req, res, next) => {
  try {
    const searchString = req?.params?.searchString;
    const prophetId = req?.params?.prophetId;
    const varietyNames = await productsData.getFilteredVarietyName(
      searchString,
      prophetId
    );
    res.send(varietyNames);
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};
const checkVariety = async (req, res, next) => {
  try {
    const { varietyCode, varietyName, prophetId, requestId } = req?.body;
    const result = await productsData.checkVarietyData(
      varietyCode,
      varietyName,
      prophetId,
      requestId
    );
    let existingData = {
      varietyCode: false,
      varietyName: false,
    };
    if (
      result.length>0 && result[0].description == varietyName &&
      result[0].code == varietyCode
    ) {
      existingData.varietyCode = true;
      existingData.varietyName = true;
    } else if (result.length>0 && result[0].description == varietyName) {
      existingData.varietyName = true;
    } else if (result.length>0 && result[0].code == varietyCode) {
      existingData.varietyCode = true;
    }
    res.send(existingData);
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};

//Packaging request
const addUpdatePk = async (req, res) => {
  try {
    const data = req.body;
    const created = await productsData.addUpdatePackagingRequest(data);
    if (created && created.rowsAffected && created.rowsAffected[0] >= 1) {
    let requestNumber;
    // let setCreatedSubproductCode = false
    if (data.request_no == null) {
      requestNumber = created.recordset.map(
        (item) => `${item.company}${item.request_no}`
      )[0];
    }
    if (data.createNewSubProductCodeInDB && data.newSubProdCode) {
      const newSubProductCodeCreated =
        await productsData.createNewSubProductCode(data,requestNumber);
      const createdSubProductCode = await productsData.getCreatedNewSubProductCode(data,requestNumber);
      //update db somehow
        // get id
        // updaet db somehow
        // keep some way to determine its a new code
      if (
        newSubProductCodeCreated &&
        newSubProductCodeCreated.rowsAffected &&
        newSubProductCodeCreated.rowsAffected[0] === 1
      ) {
        //created successfully pass flag
        console.log("created successfully pass flag ")
      } else {
        //unable to create
        console.log("unable to create")
      }
    }
    const typeOfForm = [1, 3].includes(data.actionId) ? "saved" : "subimitted" ;
    // const csf = "cat";
    res
      .status(200)
      .json({
        msg: `Packaging form ${typeOfForm} successfully`,
        requestNumber,
      });
  } else {
    res.status(403).json({ msg: `Failed to ${[1, 3].includes(data.actionId) ? "save" : "subimit"} Packaging form` });
  }
  } catch (error) {
    console.error(error);
    res.status(400).send(error.message);
  }
};

const getPkById = async (req, res) => {
  try {
    const packagingRequestId = req.params.id;
    const packagingRequest = await productsData.getPKrequetById(packagingRequestId);
    res.send(packagingRequest);
  } catch (error) {
    console.error("error fetching packaging requests by id",error)
    res.status(400).send(error.message);
  }
};

module.exports = {
  getFilteredVarietyNames,
  addRawMaterials,
  getProductsDropdowns,
  getProducts,
  getRawMaterialsById,
  updateStatus,
  addFinishedProducts,
  getFinishedProductsById,
  getNVProductById,
  addUpdateNV,
  updateRawMaterials,
  updateFinishedProducts,
  addAllDropdownValue,
  updateUnblock,
  getRequestNumber,
  insertRequestNumber,
  getProductGroups,
  updateAllDropdownValue,
  getSubProductsByMasterCode,
  checkVariety,addUpdatePk,getPkById
};
