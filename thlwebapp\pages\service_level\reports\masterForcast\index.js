import Layout from "@/components/Layout";
import React, { useEffect, useState } from "react";

export default function MasterForcast({ userData }) {
  const [companyName, setCompanyName] = useState("");
  const [ADCompanyName, setADCompanyName] = useState("");
  useEffect(() => {
    // const company = Cookies.get("company");
    const company = userData?.company;
    // const ADCompany = Cookies.get("ADCompanyName");
    const ADCompany = userData?.companyName || userData?.ADCompanyName;
    setCompanyName(company);
    setADCompanyName(ADCompany);
  }, [userData]);
  return (
    <Layout userData={userData}>
      <div className="flex justify-center items-center mr-[55px] mt-5">
        {companyName == "efcltd" ? (
          <iframe
            className="h-[calc(100vh-100px)]"
            title="EFC-Service level report"
            width="100%"
            height="680"
            src="https://app.powerbi.com/reportEmbed?reportId=e574de04-d3db-4b80-a678-323d4aa6237f&autoAuth=true&ctid=0a6ac99f-7136-40dd-9ec3-ddedf46edbc9"
            frameBorder="0"
            allowFullScreen={true}
          ></iframe>
        ) : companyName == "fpp-ltd" ? (
          <iframe
            className="h-[calc(100vh-100px)]"
            title="FPP-Service level report"
            width="100%"
            height="680"
            src="https://app.powerbi.com/reportEmbed?reportId=249f8dad-2677-4ee1-80d9-757e294c961d&autoAuth=true&ctid=0a6ac99f-7136-40dd-9ec3-ddedf46edbc9"
            frameBorder="0"
            allowFullScreen={true}
          ></iframe>
        ) : companyName == "dpsltd" && ADCompanyName == "DPS MS" ? (
          <iframe
            className="h-[calc(100vh-100px)]"
            title="DPS MS-Service level report"
            width="100%"
            height="680"
            src="https://app.powerbi.com/reportEmbed?reportId=ba7d6f9e-b56e-450f-a85d-980a398c09f9&autoAuth=true&ctid=0a6ac99f-7136-40dd-9ec3-ddedf46edbc9"
            frameBorder="0"
            allowFullScreen={true}
          ></iframe>
        ) : companyName == "dpsltd" &&
          (ADCompanyName == "DPS" || ADCompanyName == "Direct Produce Supplies") ? (
          <iframe
            className="h-[calc(100vh-100px)]"
            title="DPS-Service level report"
            width="100%"
            height="680"
            src="https://app.powerbi.com/reportEmbed?reportId=cf2c16db-f849-41ee-ba5e-786a4069cb59&autoAuth=true&ctid=0a6ac99f-7136-40dd-9ec3-ddedf46edbc9"
            frameBorder="0"
            allowFullScreen={true}
          ></iframe>
        ) : companyName == "issproduce" ? (
          <iframe
            className="h-[calc(100vh-100px)]"
            title="ISS Service Level Report"
            width="100%"
            height="680"
            src="https://app.powerbi.com/reportEmbed?reportId=63750cd3-5b3b-4845-b4ca-213be98b6fed&autoAuth=true&ctid=0a6ac99f-7136-40dd-9ec3-ddedf46edbc9"
            frameBorder="0"
            allowFullScreen={true}
          ></iframe>
        ) : (
          <div className="w-[40%] mx-auto nodata rounded-lg my-32 flex flex-row justify-between p-8 gap-8">
            <div className="w-full flex flex-col items-center text-2xl font-bold justify-center">
              Reports for this company are not yet available. Stay tuned!
            </div>
          </div>
        )}
        {/* <iframe title="EFC Service Level Report - DEV" width="1140" height="541.25" src="https://app.powerbi.com/reportEmbed?reportId=d5cf5bbe-ddf9-4a9e-b2d1-3b6cca433741&autoAuth=true&ctid=0a6ac99f-7136-40dd-9ec3-ddedf46edbc9" frameborder="0" allowFullScreen="true"></iframe> */}
      </div>
    </Layout>
  );
}

export const getServerSideProps = async (context) => {
  try {
    // Use secure session validation
    const sessionId = context.req.cookies.thl_session;

    if (!sessionId) {
    return {
      redirect: {
          destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
        permanent: false,
      },
    };
  }

    // Validate session with our backend API
    const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8081';
    
    try {
      const response = await fetch(`${apiBase}/api/auth/me`, {
        method: 'GET',
        headers: {
          'Cookie': `thl_session=${sessionId}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        // Session invalid or expired - redirect to login
        return {
          redirect: {
            destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
            permanent: false,
          },
        };
      }

      const { user } = await response.json();

  return {
    props: {
          userData: user,
        },
      };

    } catch (fetchError) {
      console.error('Session validation failed:', fetchError);
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
          permanent: false,
        },
      };
    }

  } catch (error) {
    console.error('Authentication error:', error);
    return {
      redirect: {
        destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
        permanent: false,
      },
    };
  }
};
