function isDateInPastWeek(dateString) {
  const inputDate = new Date(dateString);

  const currentDate = new Date();
  currentDate.setHours(0, 0, 0, 0);

  // Get the day of the week for the current date (0 is Sunday, 6 is Saturday)
  const currentDayOfWeek = currentDate.getDay();

  // Calculate the start of the current week (last Sunday)
  const startOfCurrentWeek = new Date(currentDate);
  startOfCurrentWeek.setDate(currentDate.getDate() - currentDayOfWeek);

  // Calculate the start and end of the past week
  const startOfPastWeek = new Date(startOfCurrentWeek);
  startOfPastWeek.setDate(startOfCurrentWeek.getDate() - 7);

  const endOfPastWeek = new Date(startOfCurrentWeek);
  endOfPastWeek.setDate(startOfCurrentWeek.getDate() - 1);

  // Check if the input date is within the past week
  return inputDate <= endOfPastWeek;
}

export { isDateInPastWeek };
