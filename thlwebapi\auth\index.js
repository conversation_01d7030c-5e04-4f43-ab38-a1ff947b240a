const { default: axios } = require("axios");
const jwt = require("jsonwebtoken");
const jwksClient = require("jwks-rsa");
const passport = require("passport");
const { BearerStrategy } = require("passport-azure-ad");
const { createPublicKey } = require("crypto");
const { promisify } = require("util");
const { ClientConfigurationError } = require("@azure/msal-node");
const verifyTokenAsync = promisify(jwt.verify);

const MSAL_CONFIG = {
  CLIENT_ID: process.env.CLIENT_ID,
  TENANT_ID: process.env.TENANT_ID,
};

module.exports = function (passport) {
  passport.use(
    new BearerStrategy(
      {
        identityMetadata: `https://login.microsoftonline.com/${MSAL_CONFIG.TENANT_ID}/.well-known/openid-configuration`,
        clientID: MSAL_CONFIG.CLIENT_ID,
        validateIssuer: true,
        loggingLevel: "info",
      },
      (token, done) => {
        if (tokenIsValid(token)) {
          return done(null, token);
        } else {
          return done(null, false, "Token is not valid");
        }
      }
    )
  );
};

const jwksClientV1 = () => {
  return jwksClient({
    jwksUri: `https://login.microsoftonline.com/${MSAL_CONFIG.TENANT_ID}/discovery/keys`, // v1.0
    cache: true,
    cacheMaxEntries: 5,
    cacheMaxAge: 10 * 60 * 1000, // 10 minutes
  });
};

const jwksClientV2 = () => {
  return jwksClient({
    jwksUri: `https://login.microsoftonline.com/${MSAL_CONFIG.TENANT_ID}/discovery/v2.0/keys`, // v2.0
    cache: true,
    cacheMaxEntries: 5,
    cacheMaxAge: 10 * 60 * 1000, // 10 minutes
  });
};

async function verifyToken(req, res, next) {
  if (!req || !req.headers) {
    return false;
  }

  const token = req.headers.authorization?.replace("Bearer ", "");
  if (!token) {
    return false;
  }

  try {
    const decodedForDebug = jwt.decode(token, { complete: true });

    const tokenIssuer = decodedForDebug.payload.iss;
    const tokenAudience = decodedForDebug.payload.aud;
    
    const validIssuers = [
      `https://sts.windows.net/${MSAL_CONFIG.TENANT_ID}/`,
      `https://login.microsoftonline.com/${MSAL_CONFIG.TENANT_ID}/v2.0`,
    ];
    
    const validAudiences = [
      MSAL_CONFIG.CLIENT_ID,
      `api://${MSAL_CONFIG.CLIENT_ID}`,
    ];
    
    if (!validIssuers.includes(tokenIssuer)) {
      console.error("❌ Invalid issuer:", tokenIssuer);
      console.error("Valid issuers:", validIssuers);
      return false;
    }
    
    if (!validAudiences.includes(tokenAudience)) {
      console.error("❌ Invalid audience:", tokenAudience);
      console.error("Valid audiences:", validAudiences);
      return false;
    }
    
    const dynamicVerifyOptions = {
      algorithms: ["RS256"],
      issuer: tokenIssuer,
      audience: tokenAudience,
    };
    
    console.log("Expected issuer:", dynamicVerifyOptions.issuer);
    console.log("Expected audience:", dynamicVerifyOptions.audience);
    
    let jwksClient;
    if (tokenIssuer.includes('sts.windows.net')) {
      jwksClient = jwksClientV1();
    } else {
      jwksClient = jwksClientV2();
    }

    const decoded = await new Promise((resolve, reject) => {
      jwt.verify(token, (header, callback) => {
        jwksClient.getSigningKey(header.kid, (err, key) => {
          if (err) {
            console.error('Failed to get signing key:', err);
            return callback(err);
          }
          const signingKey = key.getPublicKey();
          callback(null, signingKey);
        });
      }, dynamicVerifyOptions, (err, decoded) => {
        if (err) reject(err);
        else resolve(decoded);
      });
    });


    if (tokenIsValid(decoded)) {
      const tokenScopes = decoded.scp || decoded.scope || "";
      if (!tokenScopes.includes('access_as_user')) {
        console.log("⚠️ Warning: Token missing expected 'access_as_user' scope");
      }

      const user = {
        name: decoded.given_name + " " + decoded.family_name,
        preferred_username: decoded.upn || decoded.preferred_username || decoded.email,
        user_id: decoded.sub,
        roles: decoded.roles || [],
        scopes: tokenScopes.split(' ')
      };

      req.user = user;
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.error("❌ Token verification failed:", error.message);
    return false;
  }
}

function tokenIsValid(decodedToken) {
  const now = Date.now() / 1000;
  return decodedToken.exp > now;
}

module.exports = verifyToken;
