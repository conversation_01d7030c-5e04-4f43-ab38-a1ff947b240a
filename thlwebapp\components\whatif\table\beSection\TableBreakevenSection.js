import React, { useEffect, useRef } from "react";
import TableBeTd from "./TableBeTd";
import TableColumnTh from "../TableColumnTh";
import useOnScreen from "../../hooks/useOnScreen";

function TableBreakevenSection({
  currentData,
  rowHeight,
  noOfWeeks,
  checkedStates,
  columnRefs,
  columnWidths,
  currentStartWeek,
  currentWeek,
  calendarWeeks,
  setModal,
  currency,
  setIsBreakevenRendered,
  isGridVisible,fiscalyear,currentFiscalYear
}) {
  const rowBreakevenRef = useRef();
  const isRowBreakevenVisible = useOnScreen(rowBreakevenRef);

  useEffect(() => {
    if (isRowBreakevenVisible) {
      setIsBreakevenRendered(true);
    }
  }, [isRowBreakevenVisible]);

  return (
    <>
      <tr
        style={{ top: `${rowHeight}px` }}
        className="sticky z-10 breakeven"
        ref={rowBreakevenRef}
      >
        {!isRowBreakevenVisible && <td style={{ minHeight: '10px' }}></td>}
        {isGridVisible && isRowBreakevenVisible && (
          <TableColumnTh
            rowId="sectionrow"
            rowTitle="BREAKEVEN"
            headHeight={rowHeight}
            noOfWeeks={noOfWeeks}
            checkedStates={checkedStates}
            columnRefs={columnRefs}
            columnWidths={columnWidths}
          />
        )}
      </tr>
      {isGridVisible &&
        isRowBreakevenVisible &&
        currentData?.map((row, index) => {
          const quarterData = [
            ...(row.quarters?.Q1 || []),
            ...(row.quarters?.Q2 || []),
            ...(row.quarters?.Q3 || []),
            ...(row.quarters?.Q4 || []),
            ...(row.quarters?.Q5 || []),
          ];

          return (
            <tr
              key={index}
              title={`${
                !!row.isLockedBy
                  ? `${row.isLockedBy} is currently working on this product`
                  : ""
              }`}
            >
              <TableColumnTh
                isTd={true}
                checkedStates={checkedStates}
                columnRefs={columnRefs}
                columnWidths={columnWidths}
                row={row}
                tdValue={row?.total_product_be?.toFixed(2)}
                isLockedBy={row.isLockedBy}
              />
              <TableBeTd
                quarterData={quarterData}
                row={row}
                previousSunday={currentStartWeek}
                currentWeek={currentWeek}
                calendarWeeks={calendarWeeks}
                setModal={setModal}
                currency={currency}
                isLockedBy={row.isLockedBy}
                fiscalyear={fiscalyear}
                currentFiscalYear={currentFiscalYear}
              />
            </tr>
          );
        })}
    </>
  );
}

export default TableBreakevenSection;
