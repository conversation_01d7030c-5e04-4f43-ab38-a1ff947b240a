SELECT dp.[id],
       dp.[from_dp],
       dp.[name],
       dp.[prophet_id],
       sdp.[isActive],
       sdp.[supplier_id],
       sdp.[distribution_point_id]
  FROM [distribution_points] dp
  LEFT JOIN [supplier_distribution_point] sdp ON sdp.distribution_point_id = dp.id
 WHERE (dp.[prophet_id] = @prophet OR dp.[prophet_id] = 5)
   AND (dp.[name] LIKE '%' + @value + '%' OR dp.[from_dp] LIKE '%' + @value + '%')
