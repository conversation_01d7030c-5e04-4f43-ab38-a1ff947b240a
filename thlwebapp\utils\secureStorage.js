// Simple module with functions - no classes

import Cookies from "js-cookie";

const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8081";

// Get current user from server session
export const getCurrentUser = async () => {
  try {
    const response = await fetch(`${apiBase}/api/auth/me`, {
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (response.ok) {
      const data = await response.json();
      return data.user;
    }

    return null;
  } catch (error) {
    console.error("Failed to get current user:", error);
    return null;
  }
};

// Login with MSAL token
export const login = async (token, additionalData = {}) => {
  try {
    const response = await fetch(`${apiBase}/api/auth/login`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify(additionalData), // This will include the graphToken
    });

    if (response.ok) {
      const data = await response.json();
      return { success: true, user: data.user };
    } else {
      const error = await response.json();
      return { success: false, error: error.message };
    }
  } catch (error) {
    console.error("Login failed:", error);
    return { success: false, error: error.message };
  }
};

// Logout
export const logout = async () => {
  try {
    await fetch(`${apiBase}/api/auth/logout`, {
      method: "POST",
      credentials: "include",
    });

    // Clear client-side data
    clearClientStorage();

    return true;
  } catch (error) {
    console.error("Logout failed:", error);
    return false;
  }
};

// Clear all client-side storage
export const clearClientStorage = () => {
  const keysToRemove = [
    "superUser",
    "company",
    "id",
    "name",
    "role",
    "email",
    "allowedSections",
    "selectedQuarterString",
    "prophet",
    "prophets",
    "isFormNew",
    "current",
    "curr",
    "supplier_id",
    "formType",
    "isEdit",
  ];
  keysToRemove.forEach((key) => localStorage.removeItem(key));

  const cookiesKeysToRemove = [
    "finishWarning","rawWarning",
    "PreviousPage",
    "slFilters",
    "filters",
    "currentWeek",
    "prophets",
  ];
  cookiesKeysToRemove.forEach((key) => Cookies.remove(key));
};

// Check if user is authenticated
export const isAuthenticated = async () => {
  const user = await getCurrentUser();
  return !!user;
};

// Refresh session (extend expiry)
export const refreshSession = async () => {
  try {
    const response = await fetch(`${apiBase}/api/auth/refresh`, {
      method: "POST",
      credentials: "include",
    });

    return response.ok;
  } catch (error) {
    console.error("Session refresh failed:", error);
    return false;
  }
};
