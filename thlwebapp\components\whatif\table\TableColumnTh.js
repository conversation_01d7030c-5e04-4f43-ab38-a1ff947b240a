import { Tooltip } from "@fluentui/react-components";
import React, { forwardRef } from "react";

const TableColumnTh = ({
  isTd = false,
  isDummy = false,
  isMainHeader = false,
  calenderName = "",
  checkedStates,
  columnRefs,
  columnWidths,
  rowId = "",
  rowTitle = "",
  headHeight,
  noOfWeeks,
  rowRef,
  row,
  tdValue,
  isLockedBy,
}) => {
  return (
    <>
      {!isTd && (
        <>
          {!!rowId && (
            <th
              ref={rowRef ?? null}
              className={`${rowId} !h-0 !p-0  sticky !z-[10] left-0 text-sm whitespace-nowrap`}
            >
              <span className="pl-2">{rowTitle}</span>
            </th>
          )}

          {checkedStates.columns.customerCode && (
            <th
              className={`${rowId} !h-0 !p-0 sticky ${isDummy?"!w-32":"!w-28"} !z-[9] bg-[#f3f8ff] text-sm custCode`}
              ref={isDummy ? columnRefs.customerCode : null}
              style={{ left: `${0}px` }}
            >
              {isMainHeader ? "Customer Code" : row ? row?.customer_code : ""}
            </th>
          )}
          {checkedStates.columns.masterProduct && (
            <th
              className={`${rowId} !h-0 !w-44 !p-0 sticky !z-[9] bg-[#f3f8ff] text-sm mastProd`}
              ref={isDummy ? columnRefs.masterCode : null}
              style={{
                left: `${
                  rowId == "titlerow" || rowId == "sectionrow"
                    ? 0
                    : columnWidths.customerCode
                }px`,
              }}
            >
              {isMainHeader ? "Master Code" : row ? row?.Master_code : ""}
            </th>
          )}
          {checkedStates.columns.businessUnit && (
            <th
              className={`${rowId} !h-0 !w-52 !p-0 sticky !z-[9] bg-[#f3f8ff] text-sm busiUnit`}
              ref={isDummy ? columnRefs.businessUnit : null}
              style={{
                left: `${
                  rowId == "titlerow" || rowId == "sectionrow"
                    ? 0
                    : columnWidths.masterCode
                }px`,
              }}
            >
              {isMainHeader ? "Business Unit" : row ? row?.Business_unit : ""}
            </th>
          )}
          {checkedStates.columns.altFillID && (
            <th
              className={`${rowId} !h-0 !p-0 sticky !z-[9] bg-[#f3f8ff] text-sm altFil`}
              ref={isDummy ? columnRefs.altfillID : null}
              style={{
                left: `${
                  rowId == "titlerow" || rowId == "sectionrow"
                    ? 0
                    : columnWidths.businessUnit
                }px`,
              }}
            >
              {isMainHeader ? "Altfill ID" : row ? row?.altfilid : ""}
            </th>
          )}
          {checkedStates.columns.skuDescription && (
            <th
              className={`${rowId} !h-0 !p-0 sticky !z-[9] bg-[#f3f8ff] text-sm !w-72 sku`}
              ref={isDummy ? columnRefs.SKUDescription : null}
              style={{
                left: `${
                  rowId == "titlerow" || rowId == "sectionrow"
                    ? 0
                    : columnWidths.altfillID
                }px`,
              }}
            >
              {isMainHeader ? "SKU Description" : row ? row.product_desc : ""}
            </th>
          )}
          {checkedStates.columns.caseSize && (
            <th
              className={`${rowId} !h-0 !p-0 sticky !z-[9] bg-[#f3f8ff] text-sm caseSize`}
              ref={isDummy ? columnRefs.caseSize : null}
              style={{
                left: `${
                  rowId == "titlerow" || rowId == "sectionrow"
                    ? 0
                    : columnWidths.SKUDescription
                }px`,
              }}
            >
              {isMainHeader ? "Case Size" : row ? row.case_size : ""}
            </th>
          )}

          {!!rowId && (
            <th
              colSpan={noOfWeeks}
              className={rowId}
              style={{ top: `${headHeight}px` }}
            ></th>
          )}
          {!rowId && (
            <th
              className={`${
                isDummy ? "!h-0 !p-0" : ""
              } !text-center sticky !z-[9] bg-[#f3f8ff] !w-32 text-sm totals`}
              ref={isDummy ? columnRefs.totals : null}
              style={{
                left: `${
                  rowId == "titlerow" || rowId == "sectionrow"
                    ? 0
                    : columnWidths.caseSize
                }px`,
              }}
            >
              {isMainHeader
                ? "Totals"
                : row
                ? row.total_product_be
                : calenderName}
            </th>
          )}
        </>
      )}

      {isTd && (
        <>
          {checkedStates.columns.customerCode && (
            <td
              className={`!text-left sticky !z-[9] left-0 ${
                !!isLockedBy ? "bg-locked-products" : "bg-[#f3f8ff]"
              }`}
            >
              {row.customer_code}
            </td>
          )}
          {checkedStates.columns.masterProduct && (
            <td
              className={`!text-left sticky !z-[9] ${
                !!isLockedBy ? "bg-locked-products" : "bg-[#f3f8ff]"
              }`}
              style={{
                left: `${
                  rowId == "titlerow" || rowId == "sectionrow"
                    ? 0
                    : columnWidths.customerCode
                }px`,
              }}
            >
              {row.Master_code}
            </td>
          )}
          {checkedStates.columns.businessUnit && (
            <td
              className={`!text-left sticky !z-[9] ${
                !!isLockedBy ? "bg-locked-products" : "bg-[#f3f8ff]"
              }`}
              style={{
                left: `${
                  rowId == "titlerow" || rowId == "sectionrow"
                    ? 0
                    : columnWidths.masterCode
                }px`,
              }}
            >
              {row.Business_unit}
            </td>
          )}
          {checkedStates.columns.altFillID && (
            <td
              className={`!text-center sticky !z-[9] ${
                !!isLockedBy ? "bg-locked-products" : "bg-[#f3f8ff]"
              }`}
              style={{
                left: `${
                  rowId == "titlerow" || rowId == "sectionrow"
                    ? 0
                    : columnWidths.businessUnit
                }px`,
              }}
            >
              {row.altfilid}
            </td>
          )}
          {checkedStates.columns.skuDescription && (
            <td
              className={`!w-72 !text-left sticky !z-[9] ${
                !!isLockedBy ? "bg-locked-products" : "bg-[#f3f8ff]"
              }`}
              style={{
                left: `${
                  rowId == "titlerow" || rowId == "sectionrow"
                    ? 0
                    : columnWidths.altfillID
                }px`,
              }}
            >
              {row.product_desc}
            </td>
          )}
          {checkedStates.columns.caseSize && (
            <td
              className={`!text-center sticky !z-[9] ${
                !!isLockedBy ? "bg-locked-products" : "bg-[#f3f8ff]"
              }`}
              style={{
                left: `${
                  rowId == "titlerow" || rowId == "sectionrow"
                    ? 0
                    : columnWidths.SKUDescription
                }px`,
              }}
            >
              {row.case_size}
            </td>
          )}
          <Tooltip
            content="Total of the product for 52 weeks"
            relationship="label"
            className="!bg-white"
          >
            <td
              className={`!text-center sticky !z-[9] font-bold ${
                !!isLockedBy ? "bg-locked-products" : "bg-[#f3f8ff]"
              }`}
              style={{
                left: `${
                  rowId == "titlerow" || rowId == "sectionrow"
                    ? 0
                    : columnWidths.caseSize
                }px`,
              }}
            >
              {tdValue}
            </td>
          </Tooltip>
        </>
      )}
    </>
  );
};

export default TableColumnTh;
