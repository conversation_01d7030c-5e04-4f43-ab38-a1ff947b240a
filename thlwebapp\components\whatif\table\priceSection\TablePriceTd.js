import React, { useRef } from "react";
import PromoPopover from "../../PromoPopover";
import { GRID_PRICE } from "@/utils/whatif/utils/constants";
import { Tooltip } from "@fluentui/react-components";
import { toast } from "react-toastify";

const TablePriceTd = ({
  quarterData,
  row,
  previousSunday,
  currentWeek,
  calendarWeeks,
  setModal,
  currency,
  isLockedBy,
  fiscalyear,
  currentFiscalYear,
}) => {
  let endDatePlusOne = null;
  const positioningRef = useRef(null);

  return (
    <>
      {calendarWeeks.map((weekStartDate, weekIndex) => {
        const productData = quarterData.find(
          (data) => data.startWeek === weekStartDate
        );
        if (!productData) return;

        const isToday =
          weekStartDate === previousSunday &&
          row.fiscalyear == currentFiscalYear;

        const promotions = row?.promotions?.filter((data) => {
          const currentDate = new Date(productData?.startWeek);
          const currentMonth = currentDate.getMonth() + 1;
          const startWeekYear = currentDate.getFullYear();

          const financialYear =
            currentMonth >= 10 ? startWeekYear + 1 : startWeekYear;

          if (data?.promo_end_week_no >= data?.promo_start_week_no) {
            return (
              productData?.week >= data?.promo_start_week_no && //start week 51 and end week 1 current week is 52, 1
              productData?.week <= data?.promo_end_week_no &&
              financialYear === data?.promo_fiscalYear
            );
          } else {
            return (
              (productData?.week >= data?.promo_start_week_no &&
                financialYear === data?.promo_fiscalYear) ||
              (productData?.week <= data?.promo_end_week_no &&
                financialYear === data?.promo_fiscalYear + 1)
            );
          }
        });
        const valueToDisplay = (() => {
          if (promotions && promotions.length > 0) {
            for (const promo of promotions) {
              const promoWeekData = promo.weekData[productData?.week]?.current;
              if (
                (promoWeekData?.price != null &&
                  productData?.week >= currentWeek) ||
                (promoWeekData?.price != null &&
                  productData?.week == currentWeek - 1 &&
                  new Date().getDay() === 0)
              ) {
                return { data: promoWeekData.price, isPromo: true };
              }
            }
          }

          return {
            data:
              fiscalyear != productData.fiscalYear &&
              productData.NoDataNextFY_Q1 == 1
                ? ""
                : productData?.data[3]?.value ?? 0,
            isPromo: false,
          };
        })();
        let promoDataColor = (() => {
          if (promotions && promotions.length > 0) {
            for (const promotion of promotions) {
              if (
                promotion.type_id == 5 &&
                productData?.week == promotion.promo_end_week_no
              ) {
                endDatePlusOne = promotion.promo_end_week_no + 1;
              }

              if (
                promotion.type_id == 5 &&
                productData?.week === promotion.promo_start_week_no
              ) {
                return "pricechange-status";
              }
              if (promotion.type_id == 5) {
                return "white";
              }
            }
          }

          return fiscalyear != productData.fiscalYear &&
            productData.NoDataNextFY_Q1 == 1
            ? "no-productsbg"
            : productData.hasData == 0
            ? "no-productsbg"
            : "white";
        })();

        if (productData?.week == endDatePlusOne) {
          promoDataColor = "pricechange-status";
        }
        const popoverData = (() => {
          let promoArr = [];
          if (promotions && promotions.length > 0) {
            for (const promotion of promotions) {
              if (promotion.type_id == 5) {
                promoArr.push(promotion);
                promoArr.push(productData);
                return promoArr;
              }
            }
          }
          return [];
        })();
        return (
          <td
            title={
              fiscalyear != productData.fiscalYear &&
              productData.NoDataNextFY_Q1 == 1
                ? "Data does not exist for this product"
                : ""
            }
            ref={positioningRef}
            key={weekIndex}
            className={`${isToday ? "bg-currentWeek" : ""} ${
              !!isLockedBy && promoDataColor === "white"
                ? "bg-locked-products"
                : promoDataColor === "white" || promoDataColor === "gray"
                ? `bg-${promoDataColor}`
                : `!bg-${promoDataColor}`
            } !text-center ${
              !!isLockedBy ? "" : "hover:cursor-pointer"
            } relative`}
            onDoubleClick={() => {
              if (!!isLockedBy) {
                toast.info(
                  `You cannot edit the product while someone is already working on it.`,
                  {
                    theme: "colored",
                    autoClose: 5000,
                  }
                );
                return;
              } else if (productData.hasData == 0) {
                toast.info(
                  `You can't add a promo as the current week doesn't have data.`,
                  {
                    theme: "colored",
                    autoClose: 5000,
                  }
                );
                return;
              }

              if (
                fiscalyear != productData.fiscalYear &&
                productData.NoDataNextFY_Q1 == 1
              ) {
                toast.info(
                  `You cannot edit the product as it does not exist in this fiscal year.`,
                  {
                    theme: "colored",
                    autoClose: 5000,
                  }
                );
                return;
              }
              if (
                (valueToDisplay.isPromo &&
                  promoDataColor != "white" &&
                  promoDataColor != "gray") ||
                (!valueToDisplay.isPromo &&
                  (promoDataColor == "white" ||
                    promoDataColor == "gray" ||
                    promoDataColor == "pricechange-status"))
              ) {
                setModal(GRID_PRICE, true, row.pkey, productData);
              }
            }}
          >
            {productData.hasData == 0
              ? ""
              : valueToDisplay &&
                valueToDisplay.data !== undefined &&
                valueToDisplay.data !== "" &&
                !isNaN(valueToDisplay.data) &&
                (valueToDisplay.data < 0
                  ? `-${currency}${Math.abs(
                      Number(valueToDisplay.data)
                    ).toFixed(3)}`
                  : `${currency}${Number(valueToDisplay.data).toFixed(3)}`)}
            <div
              className={`
               ${
                 valueToDisplay.isPromo &&
                 promoDataColor != "white" &&
                 promoDataColor != "gray"
                   ? "top-0"
                   : (valueToDisplay.isPromo &&
                       promoDataColor == "white" &&
                       promoDataColor == "white") ||
                     promoDataColor == "gray"
                   ? "top-1"
                   : ""
               }
               absolute right-1 z-[5]`}
              onClick={(e) => e.stopPropagation()}
            >
              {valueToDisplay.isPromo &&
              promoDataColor != "white" &&
              promoDataColor != "gray" ? (
                <PromoPopover
                  positioningref={positioningRef}
                  popoverData={popoverData}
                  currency={currency}
                />
              ) : weekStartDate >= previousSunday &&
                valueToDisplay.isPromo &&
                promoDataColor == "white" ? (
                <Tooltip
                  content="There is already a promo in this week. Please remove the promo to change to Unit Price value"
                  relationship="label"
                  className="!bg-white"
                >
                  <svg
                    xmlns="
http://www.w3.org/2000/svg"
                    viewBox="0 0 512 512"
                    className="w-4 h-4"
                    fill="#000"
                  >
                    <path d="M248.4 84.3c1.6-2.7 4.5-4.3 7.6-4.3s6 1.6 7.6 4.3L461.9 410c1.4 2.3 2.1 4.9 2.1 7.5c0 8-6.5 14.5-14.5 14.5H62.5c-8 0-14.5-6.5-14.5-14.5c0-2.7 .7-5.3 2.1-7.5L248.4 84.3zm-41-25L9.1 385c-6 9.8-9.1 21-9.1 32.5C0 452 28 480 62.5 480h387c34.5 0 62.5-28 62.5-62.5c0-11.5-3.2-22.7-9.1-32.5L304.6 59.3C294.3 42.4 275.9 32 256 32s-38.3 10.4-48.6 27.3zM288 368a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zm-8-184c0-13.3-10.7-24-24-24s-24 10.7-24 24v96c0 13.3 10.7 24 24 24s24-10.7 24-24V184z" />
                  </svg>
                </Tooltip>
              ) : (
                ""
              )}
            </div>
          </td>
        );
      })}
    </>
  );
};

export default TablePriceTd;
