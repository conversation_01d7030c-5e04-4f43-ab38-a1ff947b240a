import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

export default function Unauthorized() {
  const router = useRouter();
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    if (countdown === 0) {
      router.push('/suppliers');
      return;
    }

    const timer = setInterval(() => {
      setCountdown(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [countdown, router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Access Denied
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            You don't have permission to access this page.
          </p>
          <p className="mt-2 text-center text-sm text-gray-600">
            Redirecting to suppliers page in{' '}
            <span className="font-bold text-skin-primary">{countdown}</span>{' '}
            second{countdown !== 1 ? 's' : ''}...
          </p>
        </div>
        
        <div className="flex justify-center space-x-4">
          <button
            onClick={() => router.push('/suppliers')}
            className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-skin-primary hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Go to Suppliers
          </button>
        </div>
      </div>
    </div>
  );
}