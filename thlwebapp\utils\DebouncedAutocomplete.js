import React, { useCallback, useEffect, useState } from "react";
import debounce from "lodash/debounce";
import { apiConfig } from "@/services/apiConfig";
import { Oval } from "react-loader-spinner";
import { getCookieData } from "@/utils/getCookieData";

const DebouncedAutocomplete = ({
  onChange,
  supplierName,
  isValidSupplierName,
  setFormChange,
  setSupplierValidName,
  prophets,
  setNewProphets,
  setProphetChange,
  supplierRoles,
  symbol,
  urgentRequest,setSupplierNameChange,isCodeSystemGenerated,handleSessionError
}) => {
  const serverAddress = apiConfig.serverAddress;
  const [inputValue, setInputValue] = useState(supplierName);
  const [filteredItems, setFilteredItems] = useState([]);
  const [isValidName, setValidName] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  const handleBlur = () => {
    setFilteredItems([]);
  };

  const filterItems = useCallback((searchString) => {
    fetch(
      `${serverAddress}suppliers/get-filtered-supplier-names/${searchString.trim()}`,
      {
        method: "get",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        credentials: 'include',
      }
    )
      .then((res) => {
        if (res.status === 200) {
          return res.json();
        }else if(res.status === 401){
          handleSessionError();
          return;
        }
        return Promise.reject(res);
      })
      .then((data) => {
        if (data) {
          setFilteredItems(() => data);
        }
        setIsLoading(false);
      })
      .catch((error) => {
        console.log(error);
        setIsLoading(false);
      });
  }, []);

  // Debounce the filtering function
  const debouncedFilterItems = useCallback(debounce(filterItems, 500), [
    filterItems,
  ]);

  useEffect(() => {
    return () => {
      // Cleanup the debouncedFilterItems function on component unmount
      debouncedFilterItems.cancel();
    };
  }, [debouncedFilterItems]);

  const handleInputChange = (supplierName) => {
    setFormChange(true);
    onChange(supplierName);

    setInputValue(supplierName);
    if (!supplierName || supplierName.length > 50) {
      setValidName(false);
      return false;
    } else {
      setSupplierValidName(true);
      setValidName(true);
      if (isCodeSystemGenerated &&
        prophets?.length > 0 &&
        supplierRoles?.includes(1) &&
        symbol !== null &&
        symbol !== undefined
      ) {
        setSupplierNameChange(true);
        const supplierNameWithoutSpaces = supplierName
          .replace(/\s/g, "")
          .toUpperCase();
        const supplierPrefix = supplierNameWithoutSpaces?.substring(0, 4);
        let generatePhophetCode = "";

        if (prophets[0] == 1) {
          const supplierPrefixDPS = supplierNameWithoutSpaces?.substring(0, 5);
          generatePhophetCode = `${supplierPrefixDPS?.padEnd(5, "X")}${symbol}`;
        } else if (prophets[0] == 2) {
          generatePhophetCode = `${supplierPrefix?.padEnd(4, "X")}9${symbol}`;
        } else if (prophets[0] == 3) {
          generatePhophetCode = (supplierPrefix + "3").padEnd(6, "X");
        } else if (prophets[0] == 4) {
          generatePhophetCode = (
            supplierPrefix + "2" + symbol ? symbol : ""
          ).padEnd(6, "X");
        }

        const sanitizedCode = generatePhophetCode?.replace(/[\r\n]/g, "");
       
        // Set the sanitized code in newProphets
        setNewProphets([[prophets[0], sanitizedCode, prophets[2]]]);
      }
    }

    if (supplierName.length <= 0) {
      debouncedFilterItems.cancel();
      setIsLoading(false);
      setFilteredItems(() => []);
    } else {
      // Debounce the filtering logic
      setIsLoading(true);
      debouncedFilterItems(supplierName);
    }
  };

  return (
    <div className="relative">
      <input
        type="text"
        className={`w-2/3 px-2 2xl:px-3 border  ${
          !isValidName ? "!border-red-500" : ""
        } searchbar rounded-md`}
        maxLength={50}
        value={inputValue}
        onChange={(e) => {
          handleInputChange(e.target.value);
          if (urgentRequest) {
            setProphetChange(true);
          }
        }}
        onBlur={handleBlur}
        style={{ textTransform: "capitalize" }}
      />
      {(!isValidName || !isValidSupplierName) && (
        <span className="text-red-500 mt-1 block">
          Please Enter Valid Suppllier Name of max 50 chars
        </span>
      )}

      {isLoading && (
        <ul
          className="absolute border p-1 w-2/3 z-50"
          style={{ backgroundColor: "white", borderRadius: "4px" }}
        >
          <li>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                height: "20px",
                width: "20px",
              }}
            >
              <Oval
                color="#002D73"
                height={10}
                width={10}
                visible={true}
                ariaLabel="oval-loading"
                secondaryColor="#0066FF"
                strokeWidth={2}
                strokeWidthSecondary={2}
              />
            </div>
          </li>
        </ul>
      )}
      {!isLoading && filteredItems && filteredItems.length > 0 && (
        <ul
          className="absolute border p-1 w-2/3 z-50"
          style={{ backgroundColor: "white", borderRadius: "4px" }}
        >
          {filteredItems.map((item, key) => (
            <li
              key={key}
              className={`${
                key < filteredItems.length - 1
                  ? "border-b border-gray-100 py-1"
                  : ""
              }`}
            >
              {item.supcode} - {item.name}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default DebouncedAutocomplete;
