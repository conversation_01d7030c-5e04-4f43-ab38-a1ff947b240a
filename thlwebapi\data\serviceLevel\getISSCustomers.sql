select
    distinct cust_customer.hocustcode as [value],
    cust_customer.hocustcode as [label],
    sales.servicecustcode as [Service Customer]
from
    FLRS_DEV_TEST_ISS_DW.[dbo].vw_dm_sales_received_sold_rep sales
    join FLRS_DEV_TEST_ISS_DW.base.custac_nl cust_customer on cust_customer.custcode = sales.custcode
    join FLRS_DEV_TEST_ISS_DW.base.custac_nl delcust_customer on sales.delivery_cust_code = delcust_customer.custcode
WHERE
    --  altfilid > 0
    delivery_date >= @start_date
    AND delivery_date <= @end_date
    -- AND cust_customer.catnum = 1
    AND custcatnum = 1
    AND (
        @serviceCustomer = 'All Service Customers'
        OR sales.servicecustcode = @serviceCustomer
    )
order by
    cust_customer.hocustcode