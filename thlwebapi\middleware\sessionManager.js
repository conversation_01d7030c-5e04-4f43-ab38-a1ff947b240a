"use strict";

const crypto = require("crypto");

const sessions = new Map();
const userSessions = new Map();

const generateSessionId = () => {
  return crypto.randomBytes(32).toString('hex');
};

const createSession = (userId, userData, maxAge = 8 * 60 * 60 * 1000) => {
  const sessionId = generateSessionId();
  const expires = new Date(Date.now() + maxAge);
  
  const sessionData = {
    id: sessionId,
    userId,
    userData,
    expires,
    createdAt: new Date(),
    lastAccessed: new Date()
  };
  
  sessions.set(sessionId, sessionData);
  userSessions.set(userId, sessionId);
  
  return { sessionId, expires };
};

const getSession = (sessionId) => {
  if (!sessionId) return null;
  
  const session = sessions.get(sessionId);
  if (!session) return null;
  
  if (new Date() > session.expires) {
    destroySession(sessionId);
    return null;
  }
  
  session.lastAccessed = new Date();
  return session;
};

const updateSession = (sessionId, newUserData) => {
  const session = getSession(sessionId);
  if (!session) return false;
  
  session.userData = { ...session.userData, ...newUserData };
  session.lastAccessed = new Date();
  return true;
};

const destroySession = (sessionId) => {
  const session = sessions.get(sessionId);
  if (session) {
    userSessions.delete(session.userId);
    sessions.delete(sessionId);
  }
};

const cleanupExpiredSessions = () => {
  const now = new Date();
  for (const [sessionId, session] of sessions.entries()) {
    if (now > session.expires) {
      destroySession(sessionId);
    }
  }
};

setInterval(cleanupExpiredSessions, 30 * 60 * 1000);

module.exports = {
  createSession,
  getSession,
  updateSession,
  destroySession,
  cleanupExpiredSessions
}; 