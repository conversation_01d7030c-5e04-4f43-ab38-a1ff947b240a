import React, { useEffect, useState } from "react";
import Navbar from "./Navbar";
import Sidebar from "./Sidebar";
import { useRouter } from "next/router";
import { usePathname } from "next/navigation";

const Layout = ({ children, userData, blockScreen }) => {
  const router = useRouter();
  const pathname = usePathname();
  const [showDesktopViewMessage, setShowDesktopViewMessage] = useState(false);

  useEffect(() => {
    // Redirect to login if no userData
    if (!userData) {
      router.push('/login');
      return;
    }

    const handleResize = () => {
      if (window.innerWidth <= 767) {
        setShowDesktopViewMessage(true);
      } else {
        setShowDesktopViewMessage(false);
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize();
    
    return () => window.removeEventListener("resize", handleResize);
  }, [userData, router]);
  
  return (
    <div id="wrapper">
      {showDesktopViewMessage ? (
        <div className="desktop-view-message">
          <div className="message-content">
            <h2>Please Open in Desktop View</h2>
            <p>This website is best viewed on a desktop or laptop.</p>
          </div>
        </div>
      ) : blockScreen ? (
        <>
          <div className="block-view-message">
            <div className="message-content">
              <h2>Service Unavailable</h2>
              <p>
                We are currently experiencing issues and are working to resolve
                them as quickly as possible. Please check back later.
              </p>
            </div>
          </div>
        </>
      ) : (
        <>
          <Sidebar userData={userData} />
          <div className="page-container">
            <Navbar userData={userData} />
            <div className="main-root">
              <div
                className={`relative md:ml-[55px] lg:ml-[60px] py-2 2xl:h-[calc(100%-60px)] ${
                  pathname == "/whatif" || pathname == "/service_level"
                    ? "w-[100%-70px] px-0 pl-3 mt-[45px]"
                    : "w-full px-8 mt-[60px]"
                }`}
              >
                {children}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default Layout;
