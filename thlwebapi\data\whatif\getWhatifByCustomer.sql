DECLARE @CurrentDate DATE;

DECLARE @CurrentFinancialYearFirstDate CHAR(8);

DECLARE @NextFinancialYearFirstDate CHAR(8);

DECLARE @CurrentFinancialYear INT;

SET
    @CurrentFinancialYear = CASE
        WHEN MONTH(GETDATE()) >= 10 THEN YEAR(GETDATE()) + 1
        ELSE YEAR(GETDATE())
    END;

SET
    @CurrentDate = CASE
        WHEN @financialYear = @CurrentFinancialYear THEN GETDATE() -- Use current date if year is current
        ELSE CONVERT(
            DATE,
            CAST(@financialYear - 1 AS CHAR(4)) + '1001',
            112
        ) -- 1 Oct of the input year if it's a past year
    END;

-- Calculate the first date of the current financial year
SET
    @CurrentFinancialYearFirstDate = CASE
        WHEN MONTH(@CurrentDate) >= 10 THEN CONVERT(
            CHAR(8),
            CAST(YEAR(@CurrentDate) AS CHAR(4)) + '1001',
            112
        )
        ELSE CONVERT(
            CHAR(8),
            CAST(YEAR(@CurrentDate) - 1 AS CHAR(4)) + '1001',
            112
        )
    END;

-- Calculate the first date of the next financial year
SET
    @NextFinancialYearFirstDate = CASE
        WHEN MONTH(@CurrentDate) >= 10 THEN CONVERT(
            CHAR(8),
            CAST(YEAR(@CurrentDate) + 1 AS CHAR(4)) + '1001',
            112
        )
        ELSE CONVERT(
            CHAR(8),
            CAST(YEAR(@CurrentDate) AS CHAR(4)) + '1001',
            112
        )
    END;

WITH cte AS (
    SELECT
        ff_actuals.deldate AS [Delivery Date],
        alt.altfilid,
        ff_actuals.prodnum,
        ff_actuals.hocustcode_customer,
        alt.product_desc,
        pgd.business_unit AS [Business_unit],
        plg.mascode_desc AS [Master_Code],
        CAST(alt.countsize AS NUMERIC(18, 0)) AS [Count_Size],
        alt.product_number,
        cast(
            CASE
                WHEN ff_actuals.hocustcode_customer IN ('RIVERF', 'ABELON', 'AB&COL')
                AND alt.weight_g > 1000 THEN ff_actuals.inv_qty_cases * alt.wgtouter
                ELSE ff_actuals.inv_qty_cases
            END as numeric(18, 0)
        ) AS [Volume],
        cast(ff_actuals.inv_grossline_base as numeric(18, 2)) AS [Value],
        cast(
            add_cost_base.net_line_value_base - add_cost_base.cost_of_sale_base + add_cost_base.service_income_base as numeric(18, 6)
        ) AS [Gross Profit],
        cast(
            CASE
                WHEN ff_actuals.hocustcode_customer IN ('RIVERF', 'ABELON', 'AB&COL')
                AND alt.weight_g > 1000 THEN FLOOR((ff_actuals.gross_price_base / alt.wgtouter) * 1000) / 1000
                ELSE FLOOR((ff_actuals.gross_price_base / alt.countsize) * 1000) / 1000
            END as decimal(18,3)
        ) AS [Unit Price],
        cast(ff_actuals.inv_qty_units as numeric(18, 0)) AS Units,
        CONCAT_WS(
            '|',
            alt.altfilid,
            ff_actuals.prodnum,
            ff_actuals.hocustcode_customer,
            alt.product_desc
        ) AS pkey
    FROM
        [FLR_DEV_TEST_off_BI_lookup].[dbo].[finance_fact] ff_actuals
        LEFT OUTER JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[finance_additional_costings_base] add_cost_base ON ff_actuals.orddetid = add_cost_base.orddetid
        AND ff_actuals.lotdetid = add_cost_base.lotdetid
        INNER JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_product_grouping] pg ON ff_actuals.altfilid = pg.altfilid
        INNER JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_altfil] alt ON ff_actuals.altfilid = alt.altfilid
        INNER JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_product_lvl_group] plg ON ff_actuals.prodnum = plg.prodnum
        INNER JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_prod_group_desc] pgd ON plg.mascode = pgd.mascode
    WHERE
        ff_actuals.deldate >= @CurrentFinancialYearFirstDate
        AND ff_actuals.deldate < dateadd(day,-1,cast(getdate() as date))
        AND ff_actuals.hocustcode_customer NOT IN (
            'DUMPED',
            'RETURN',
            'SAMPLE',
            'PPACK',
            'REJISS',
            'SMOVE'
        )
    UNION
    ALL
    SELECT
        fcast_daily.forecast_date AS [Delivery Date],
        alt.altfilid,
        fcast_daily.product_number,
        fcast_daily.hocust_code,
        alt.product_desc,
        pgd.business_unit AS [Business_unit],
        plg.mascode_desc AS [Master_Code],
        CAST(alt.countsize AS NUMERIC(18, 0)) AS [Count_Size],
        alt.product_number,
        cast(
            CASE
                WHEN fcast_daily.hocust_code IN ('RIVERF', 'ABELON', 'AB&COL')
                AND alt.weight_g > 1000 THEN fcast_daily.forecast_qty * alt.wgtouter
                ELSE fcast_daily.forecast_qty
            END as numeric(18, 0)
        ) AS [Volume],
        cast(
            fcast_daily.forecast_base_sales_value as numeric(18, 2)
        ) AS forecast_base_sales_value,
        fcast_daily.forecast_base_price * (fcast_daily.forecast_unit_qty) * (COALESCE(fgm.gross_margin_pc, 0)) AS Profit_Base,
        cast(
            CASE
                WHEN fcast_daily.hocust_code IN ('RIVERF', 'ABELON', 'AB&COL')
                AND alt.weight_g > 1000 THEN FLOOR((fcast_daily.forecast_base_price / alt.wgtouter) * 1000) / 1000
                ELSE FLOOR((fcast_daily.forecast_base_price) * 1000) / 1000
            END as decimal(18,3)
        ) AS forecast_unit_base_price,
        cast(fcast_daily.forecast_unit_qty as numeric(18, 0)) AS Units,
        CONCAT_WS(
            '|',
            alt.altfilid,
            fcast_daily.product_number,
            fcast_daily.hocust_code,
            alt.product_desc
        ) AS pkey
    FROM
        [FLR_DEV_TEST_off_BI_lookup].[dbo].[forecast_daily] fcast_daily
        INNER JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_product_grouping] pg ON fcast_daily.altfilid = pg.altfilid
        INNER JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_altfil] alt ON fcast_daily.altfilid = alt.altfilid
        INNER JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_product_lvl_group] plg ON fcast_daily.product_number = plg.prodnum
        INNER JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_prod_group_desc] pgd ON plg.mascode = pgd.mascode
        LEFT OUTER JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[finance_gross_margin] fgm ON fcast_daily.altfilid = fgm.altfilid
        AND fcast_daily.forecast_date BETWEEN fgm.gm_start_date
        AND fgm.gm_end_date
        AND fcast_daily.hocust_code = fgm.hocustcode
    WHERE
        fcast_daily.forecast_date >=dateadd(day,-1,cast(getdate() as date))
        AND fcast_daily.forecast_date < (
            SELECT
                DISTINCT [fcastendate]
            FROM
                [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_fcastenddate]
        )
        AND fcast_daily.forecast_unit_qty > 0
    UNION
    ALL
    SELECT
        ff_actuals.deldate AS [Delivery Date],
        ff_actuals.altfilid,
        ff_actuals.prodnum,
        jbhocustcode,
        productdesc,
        pgd.business_unit AS [Business_unit],
        plg.mascode_desc AS [Master_Code],
        NULL AS Count_Size,
        ff_actuals.prodnum,
        0 Volume,
        0 sales_value,
        add_cost_base.net_line_value_base - add_cost_base.cost_of_sale_base + add_cost_base.service_income_base AS [Gross Profit],
        --only consider GP for WASTE
        0 base_price,
        0 unit_base_price,
        CONCAT_WS(
            '|',
            ff_actuals.altfilid,
            ff_actuals.prodnum,
            jbhocustcode,
            productdesc
        ) AS pkey
    FROM
        [FLR_DEV_TEST_off_BI_lookup].[dbo].[finance_fact] ff_actuals
        INNER JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_product_lvl_group] plg ON ff_actuals.prodnum = plg.prodnum
        INNER JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_prod_group_desc] pgd ON plg.mascode = pgd.mascode
        LEFT OUTER JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[finance_additional_costings_base] add_cost_base ON ff_actuals.orddetid = add_cost_base.orddetid
        AND ff_actuals.lotdetid = add_cost_base.lotdetid
    WHERE
        ff_actuals.deldate >= @CurrentFinancialYearFirstDate
        AND ff_actuals.deldate < dateadd(day,-1,cast(getdate() as date))
        AND custcode = 'WASTE'
    -- UNION
    -- ALL
    -- SELECT
    --     ff_actuals.deldate AS [Delivery Date],
    --     ff_actuals.altfilid,
    --     ff_actuals.prodnum,
    --     hocustcode_customer,
    --     ff_actuals.productdesc,
    --     pgd.business_unit AS [Business_unit],
    --     plg.mascode_desc AS [Master_Code],
    --     alt.countsize AS cs,
    --     ff_actuals.prodnum,
    --     ff_actuals.inv_qty_cases AS [Volume],
    --     ff_actuals.inv_grossline_base AS [Value],
    --     add_cost_base.net_line_value_base - add_cost_base.cost_of_sale_base + add_cost_base.service_income_base AS [Gross Profit],
    --     0 AS [Unit Price],
    --     ff_actuals.inv_qty_units AS Units,
    --     CONCAT_WS(
    --         '|',
    --         ff_actuals.altfilid,
    --         ff_actuals.prodnum,
    --         hocustcode_customer,
    --         ff_actuals.productdesc
    --     ) AS pkey
    -- FROM
    --     [FLR_DEV_TEST_off_BI_lookup].[dbo].[finance_fact] ff_actuals
    --     LEFT OUTER JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[finance_additional_costings_base] add_cost_base ON ff_actuals.orddetid = add_cost_base.orddetid
    --     AND ff_actuals.lotdetid = add_cost_base.lotdetid
    --     Left join [FLR_DEV_TEST_off_BI_lookup].[dbo].dm_altfil as alt on ff_actuals.altfilid = alt.altfilid
    --     INNER JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_product_lvl_group] plg ON ff_actuals.prodnum = plg.prodnum
    --     INNER JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_prod_group_desc] pgd ON plg.mascode = pgd.mascode
    --     INNER JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_customer] cust on ff_actuals.custcode = cust.custcode
    -- WHERE
    --     ff_actuals.deldate >= @CurrentFinancialYearFirstDate
    --     AND ff_actuals.deldate < dateadd(day,-1,cast(getdate() as date))
    --     and cust.category_no = 5 -- Shortages
),
default_calendar AS (
    SELECT
        DISTINCT [calendar_name],
        [fiscalyear],
        [fiscalquarter],
        [fiscalweek],
        [startweek],
        [endweek]
    FROM
        [FLR_DEV_TEST_off_BI_lookup].[dbo].[off_cal_start_end_week]
    where
        calendar_name = 'off financial calendar'
        and startweek >= @CurrentFinancialYearFirstDate
        and [endweek] < (
            SELECT
                DISTINCT [fcastendate]
            FROM
                [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_fcastenddate]
        )
),
Budget_data AS(
    select
        blr.budget_date,
        cast(
            case
                when blr.hocustcode in ('RIVERF', 'ABELON', 'AB&COL')
                and alt.weight_g > 1000 then budget_qty * alt.wgtouter
                else budget_qty
            end as decimal(18, 0)
        ) [Volume],
        cast(
            budget_qty * alt.countsize * price as decimal(18, 2)
        ) as [Value],
        cast(bw.budget_PROFIT as decimal(18, 2)) as [Gross Profit],
        cast(
            case
                when blr.hocustcode in ('RIVERF', 'ABELON', 'AB&COL') 
                and alt.weight_g > 1000 then FLOOR((price / alt.wgtouter) * 1000) / 1000
                else FLOOR(price * 1000) / 1000
            end as decimal(18,3)
        ) [Unit Price],
        cast(budget_units as decimal(18, 0)) as Units,
        concat(
            alt.altfilid,
            '|',
            prodnum,
            '|',
            blr.hocustcode,
            '|',
            product_desc
        ) as pkey
    from
        [FLR_DEV_TEST_off_BI_lookup].[dbo].[budget_latest_revision] blr
        join [FLR_DEV_TEST_off_BI_lookup].[dbo].[dm_altfil] alt on blr.altfilid = alt.altfilid
        join [FLR_DEV_TEST_off_BI_lookup].[dbo].[budget_dy_weekly_additional_totals] bw on blr.altfilid = bw.altfilid
        and blr.budget_date = bw.budget_date
        and blr.hocustcode = bw.hocust_code
    where
        blr.budget_date >= @CurrentFinancialYearFirstDate
        and blr.budget_date < @NextFinancialYearFirstDate
),
weekly_budget_data as (
    select
        bd.budget_date,
        default_calendar.fiscalweek,
        bd.Volume AS Weekly_Budget_Volume,
        bd.[Unit Price] AS Weekly_Avg_Budget_Unit_Price,
        bd.[Value] AS Weekly_Budget_Value,
        bd.[Gross Profit] AS Weekly_Budget_Gross_Profit,
        bd.Units AS Weekly_Budget_Units,
        bd.pkey
    from
        Budget_data bd
        left JOIN default_calendar ON bd.budget_date >= default_calendar.startweek
        AND bd.budget_date <= default_calendar.endweek
    GROUP BY
        fiscalweek,
        pkey,
        bd.Volume,
        bd.[Unit Price],
        bd.[Value],
        bd.[Gross Profit],
        bd.Units,
        bd.budget_date
),
product_keys AS (
    SELECT
        DISTINCT product_desc,
        altfilid,
        prodnum,
        pkey,
        hocustcode_customer,
        Master_code,
        Business_unit,
        Count_Size
    FROM
        cte
),
calendar_product_keys AS (
    SELECT
        dc.fiscalyear,
        dc.fiscalquarter,
        dc.fiscalweek,
        dc.startweek,
        dc.endweek,
        pk.product_desc,
        pk.altfilid,
        pk.prodnum,
        pk.pkey,
        pk.hocustcode_customer,
        pk.Master_code,
        pk.Business_unit,
        pk.Count_Size
    FROM
        default_calendar dc
        CROSS JOIN product_keys pk
),
weekly_sales_with_default_calendar AS(
    SELECT
        cpk.fiscalyear,
        cpk.fiscalquarter,
        cpk.fiscalweek,
        cpk.startweek,
        cpk.product_desc,
        cpk.altfilid,
        cpk.prodnum,
        cpk.pkey,
        cpk.hocustcode_customer,
        cpk.Master_code,
        cpk.Business_unit,
        cpk.Count_Size,
        SUM(CAST(cte.Volume AS DECIMAL(18, 2))) AS Weekly_Volume,
         CAST(AVG(CAST(cte.[Unit Price] AS DECIMAL(18,3)))
            AS DECIMAL(18,3)
        ) AS Weekly_Avg_Unit_Price,        
        SUM(CAST(cte.[Value] AS DECIMAL(18, 2))) AS Weekly_Value,
        SUM(CAST(cte.[Gross Profit] AS DECIMAL(18, 2))) AS Weekly_Gross_Profit,
        SUM(CAST(cte.Units AS DECIMAL(18, 2))) AS Weekly_Units
    FROM
        calendar_product_keys cpk
        LEFT JOIN cte ON cte.[Delivery Date] >= cpk.startweek
        AND cte.[Delivery Date] <= cpk.endweek
        AND cte.product_desc = cpk.product_desc
        AND cte.altfilid = cpk.altfilid
        AND cte.prodnum = cpk.prodnum
        AND cte.pkey = cpk.pkey
        AND cte.hocustcode_customer = cpk.hocustcode_customer
        AND cte.Master_code = cpk.Master_code
        AND cte.Business_unit = cpk.Business_unit
        AND cte.Count_Size = cpk.Count_Size
    GROUP BY
        cpk.fiscalyear,
        cpk.fiscalquarter,
        cpk.fiscalweek,
        cpk.startweek,
        cpk.product_desc,
        cpk.altfilid,
        cpk.prodnum,
        cpk.pkey,
        cpk.hocustcode_customer,
        cpk.Master_code,
        cpk.Business_unit,
        cpk.Count_Size
),
filtered_weekly_sales AS (
    SELECT
        fiscalyear,
        fiscalquarter,
        fiscalweek,
        startweek,
        product_desc,
        altfilid,
        prodnum,
        pkey,
        hocustcode_customer,
        Master_code,
        Business_unit,
        Count_Size,
        Weekly_Avg_Unit_Price,
        Weekly_Value,
        Weekly_Gross_Profit,
        Weekly_Units,
        Weekly_Volume,
        CASE
            WHEN Weekly_Value > 0 THEN (Weekly_Gross_Profit / Weekly_Value) * 100
            ELSE 0
        END AS Weekly_Gross_Profit_Percentage,
        CASE
            WHEN Weekly_Avg_Unit_Price > 0 THEN Weekly_Avg_Unit_Price - Weekly_Gross_Profit / NULLIF(Weekly_Units, 0)
            ELSE 0
        END AS BE
    FROM
        weekly_sales_with_default_calendar
    WHERE
        (
            fiscalyear = @financialYear
            OR (
                fiscalyear = @financialYear + 1
                AND fiscalquarter = '1'
            )
        )
        and hocustcode_customer = @cust_code
),
whatifJoin AS (
    select
        wf.[id] as what_if_id,
        wf.[task_type_id],
        wf.[start_week_no],
        wf.[start_week],
        wf.[end_week_no],
        wf.[pkey],
        wf.[product_name],
        wf.[breakeven_status_id],
        wf.[fiscal_year],
        wf.[description],
        wf.[is_confirmed],
        wf.[is_deleted] as isPromoDeleted,
        wfd.[id] as what_if_details_id,
        wfd.[whatif_id],
        wfd.[week_no],
        wfd.[new_volume],
        wfd.[new_be],
        wfd.[new_price],
        wfd.[new_gp],
        wfd.[new_sales],
        wfd.[new_gp_percentage],
        wfd.[is_deleted] as isPromoWeekDeleted,
        wf.[created_by]
    from
        [THL_Webapp_Portal].[dbo].[whatif] wf
        left join [THL_Webapp_Portal].[dbo].[whatif_details] wfd on wf.id = wfd.whatif_id
    where
        wf.[is_deleted] = 0
        and wfd.[is_deleted] = 0
),
whaifLockJoin AS (
    select
        customer_id,
        [pkey],
        [user_name]
    FROM
        [whatif_locks]
    WHERE
        lock_active = 1
),
Users as (
    select
        u.user_id,
        u.email
    from
        [THL_Webapp_Portal].[dbo].[users] u
),
NextFYQuarter1 AS (
    SELECT
        fws.product_desc,
        fws.altfilid,
        fws.prodnum,
        fws.hocustcode_customer,
        fws.Master_Code,
        fws.Business_unit,
        fws.Count_Size,
        SUM(fws.Weekly_Volume) AS NextFY_Q1_Volume
    FROM
        filtered_weekly_sales fws
    WHERE
        fws.fiscalyear = @financialYear + 1
        AND fws.fiscalquarter = '1'
    GROUP BY
        fws.product_desc,
        fws.altfilid,
        fws.prodnum,
        fws.hocustcode_customer,
        fws.Master_Code,
        fws.Business_unit,
        fws.Count_Size
)
SELECT
    fws.product_desc,
    CASE
        WHEN fws.Weekly_Volume IS NULL AND wf.new_volume IS NULL THEN 0
        ELSE 1
    END AS HasData,
    fws.altfilid,
    bd.budget_date,
    fws.fiscalweek,
    fws.startweek,
    fws.fiscalyear,
    fws.fiscalquarter,
    fws.pkey,
    bd.Weekly_Avg_Budget_Unit_Price,
    bd.Weekly_Budget_Gross_Profit,
    bd.Weekly_Budget_Units,
    bd.Weekly_Budget_Volume,
    bd.Weekly_Budget_Value,
    fws.prodnum,
    fws.hocustcode_customer,
    fws.Master_Code,
    fws.Business_unit,
    fws.Count_Size,
    wf.what_if_id,
    wf.what_if_details_id,
    wf.[task_type_id],
    wf.[start_week_no],
    wf.[start_week],
    wf.[end_week_no],
    wf.[product_name],
    wf.[breakeven_status_id],
    wf.[fiscal_year],
    wf.[description],
    wf.[is_confirmed],
    wf.isPromoDeleted,
    wf.[whatif_id],
    wf.[week_no],
    wf.[new_volume],
    wf.[new_be],
    wf.[new_price],
    wf.[new_gp],
    wf.[new_sales],
    wf.[new_gp_percentage],
    wf.isPromoWeekDeleted,
    (COALESCE(fws.Weekly_Volume, 0)) as Weekly_Volume,
    (COALESCE(fws.BE, 0)) as BE,
    (COALESCE(fws.Weekly_Avg_Unit_Price, 0)) as Weekly_Avg_Unit_Price,
    (COALESCE(fws.Weekly_Gross_Profit, 0)) as Weekly_Gross_Profit,
    (COALESCE(fws.Weekly_Value, 0)) as Weekly_Value,
    (COALESCE(fws.Weekly_Units, 0)) as Weekly_Units,
    (COALESCE(fws.Weekly_Gross_Profit_Percentage, 0)) as Weekly_Gross_Profit_Percentage,
    (
        COALESCE(fws.Weekly_Volume, 0) * COALESCE(fws.Count_Size, 0)
    ) * COALESCE(fws.Weekly_Avg_Unit_Price, 0) AS sales,
    u.email,
    wl.[user_name] AS locked_by,
    CASE
        WHEN nq1.NextFY_Q1_Volume IS NULL THEN 1
        ELSE 0
    END AS NoDataNextFY_Q1
FROM
    filtered_weekly_sales fws
    LEFT JOIN whatifJoin wf ON fws.pkey COLLATE DATABASE_DEFAULT = wf.pkey COLLATE DATABASE_DEFAULT
    AND (
        fws.fiscalyear = wf.fiscal_year
        OR (
            wf.fiscal_year = fws.fiscalyear - 1
            AND wf.start_week_no > wf.end_week_no
        )
    )
    AND fws.fiscalweek = wf.week_no
    LEFT JOIN weekly_budget_data bd ON fws.pkey = bd.pkey
    AND fws.fiscalweek = bd.fiscalweek
    LEFT JOIN users u ON wf.created_by = u.user_id
    LEFT JOIN whaifLockJoin wl ON wl.pkey COLLATE DATABASE_DEFAULT = fws.pkey COLLATE DATABASE_DEFAULT
    LEFT JOIN NextFYQuarter1 nq1 ON fws.product_desc = nq1.product_desc
    AND fws.altfilid = nq1.altfilid
    AND fws.prodnum = nq1.prodnum
    AND fws.hocustcode_customer = nq1.hocustcode_customer
    AND fws.Master_Code = nq1.Master_Code
    AND fws.Business_unit = nq1.Business_unit
    AND fws.Count_Size = nq1.Count_Size
ORDER BY
    fws.product_desc;