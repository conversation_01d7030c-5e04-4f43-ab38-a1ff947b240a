select
    distinct cust_customer.hocustcode as [value],
    cust_customer.hocustcode as [label]
from
    [fpp_bi_lookup].[dbo].dm_fpp_sales_totals sales
    join [fpp_bi_lookup].[dbo].dm_fpp_customer cust_customer on cust_customer.custcode = sales.custcode
    join [fpp_bi_lookup].[dbo].dm_fpp_customer delcust_customer on sales.delcustcode = delcust_customer.custcode
WHERE
    case
        when cust_customer.custcode <> 'FPP' then cust_customer.hocustcode
        else delcust_customer.hocustcode
    end NOT IN ('PPACK', 'REJISS', 'SMOVE')
    AND altfilid > 0
    AND delivery_date >= @start_date
    AND delivery_date <= @end_date
    AND cust_customer.category_no = 1
order by
    cust_customer.hocustcode