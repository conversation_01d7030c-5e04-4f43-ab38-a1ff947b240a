import React, { useCallback, useEffect, useState } from "react";
import {
  makeStyles,
  Popover,
  PopoverSurface,
  PopoverTrigger,
  Tag,
  TagGroup,
} from "@fluentui/react-components";
import {
  getFiltredProductsByNameFromIdb,
  getProductsFromIdbByMasterCodeAndBusinessUnit,
  getProductsFromIdbByPkeys,
} from "@/utils/whatif/utils/indexedDB";
import { toast } from "react-toastify";
import debounce from "@/utils/whatif/utils/debounce";
import { Tooltip } from "@fluentui/react-components";

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    rowGap: "10px",
    paddingTop: "10px",
    paddingBottom: "10px",
    fontSize: "small",
    paddingLeft: "15px",
    paddingRight: "15px",
  },
  tags: {
    display: "flex",
    flexWrap: "wrap",
    flexDirection: "row",
    rowGap: "5px",
    margin: "2px",
    fontSize: "small",
  },
});

const ProductDropDown = ({
  loading,
  currentData,
  setCurrentData,
  selectedBusinessUnit,
  selectedMasterCode,
  productType,
  setProductType,
  selectWhereTotalIsZero,
  setSelectWhereTotalIsZero,
  selectWhereTotalIsNotZero,
  setSelectWhereTotalIsNotZero,
  selectedQuarters,
  setSearchPreferedProducts
}) => {
  const styles = useStyles();

  const [isProductDropDownOpen, setIsProductDropDownOpen] = useState(false);

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProducts, setSelectedProducts] = useState(
    currentData.map((cd) => {
      return { key: cd.pkey, label: cd.product_desc };
    })
  );

  const [selectedProductsString, setSelectedProductsString] = useState("");
  const [characterwiseProducts, setCharacterwiseProducts] = useState({
    "a-c": [],
    "d-f": [],
    "g-i": [],
    "j-l": [],
    "m-o": [],
    "p-r": [],
    "s-u": [],
    "v-x": [],
    "y-z": [],
    "all": [],
  });
  const [selectedCharacterRange, setSelectedCharacterRange] = useState("all");
  const [selectWhereTotalIsZeroTemp, setSelectWhereTotalIsZeroTemp] = useState(
    selectWhereTotalIsZero
  );
  const [selectWhereTotalIsNotZeroTemp, setSelectWhereTotalIsNotZeroTemp] =
    useState(selectWhereTotalIsNotZero);

  const removeItem = (_e, { value }) => {
    setSelectedProducts(
      [...selectedProducts].filter((tag) => tag.key !== value)
    );
  };

  const clearFilterHandler = () => {
    setSelectedProducts([]);
    setSelectedProductsString("");
    setSelectWhereTotalIsZeroTemp(selectWhereTotalIsZero);
    setSelectWhereTotalIsNotZeroTemp(selectWhereTotalIsNotZero);
    setProductType("finishedproduct");
  };

  const checkProductHandler = (checked, pkey, product_desc) => {
    if (checked) {
      // if (selectedProducts.length >= 10) {
      //   toast.warning(
      //     `You cannot add more than 10 products. Please unselect some to proceed.`,
      //     {
      //       theme: "colored",
      //       autoClose: 5000,
      //     }
      //   );
      //   return;
      // }

      setSelectedProducts((prev) => {
        return [...prev, { key: pkey, label: product_desc }];
      });
    } else {
      const removed = selectedProducts.filter(
        (product) => product.key !== pkey
      );
      setSelectedProducts(removed);
    }
  };

  const handleTextSearch = debounce(async (value) => {
    const queryResults = await getFiltredProductsByNameFromIdb(
      value,
      selectedBusinessUnit.value === "all" ? "" : selectedBusinessUnit.value,
      selectedMasterCode.value === "all" ? "" : selectedMasterCode.value,
      productType,
      selectWhereTotalIsZeroTemp,
      selectWhereTotalIsNotZeroTemp,
      selectedQuarters
    );

    setCharacterwiseProducts((prev) => {
      return { ...prev, ["all"]: queryResults };
    });
  }, 500); // 500ms delay

  const searchChangeHandler = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    setSelectedCharacterRange("all");
    handleTextSearch(value);
  };

  const filterSearchHandler = useCallback(async () => {
    if (selectedProducts.length <= 0) {
      toast.warning(`Please select atleast 1 product to search.`, {
        theme: "colored",
        autoClose: 5000,
      });
      return;
    }
    const selectedPkeys = selectedProducts.map((prod) => prod.key);
    const fetchedProducts = await getProductsFromIdbByPkeys(selectedPkeys);

    setSelectWhereTotalIsZero(selectWhereTotalIsZeroTemp);
    setSelectWhereTotalIsNotZero(selectWhereTotalIsNotZeroTemp);
    setCurrentData(fetchedProducts);
    setSearchPreferedProducts(true);
    
  }, [
    selectedProducts,
    selectWhereTotalIsZeroTemp,
    selectWhereTotalIsNotZeroTemp,
  ]);

  const fetchAllProductsWithFilters = async (
    selectWhereTotalIsZeroProp,
    selectWhereTotalIsNotZeroProp,
    isInitial = false
  ) => {
    const products = await getProductsFromIdbByMasterCodeAndBusinessUnit(
      selectedMasterCode.value === "all" ? null : selectedMasterCode.value,
      selectedBusinessUnit.value === "all" ? null : selectedBusinessUnit.value,
      true,
      productType,
      selectWhereTotalIsZeroProp === "1"
        ? true
        : selectWhereTotalIsZeroProp === "0"
        ? false
        : isInitial
        ? selectWhereTotalIsZero
        : selectWhereTotalIsZeroTemp,
      selectWhereTotalIsNotZeroProp === "1"
        ? true
        : selectWhereTotalIsNotZeroProp === "0"
        ? false
        : isInitial
        ? selectWhereTotalIsNotZero
        : selectWhereTotalIsNotZeroTemp,
      selectedQuarters
    );

    //split characterwise and save
    const categories = {
      "a-c": [],
      "d-f": [],
      "g-i": [],
      "j-l": [],
      "m-o": [],
      "p-r": [],
      "s-u": [],
      "v-x": [],
      "y-z": [],
      "all": isInitial? currentData: [],
    };

    products.forEach((product) => {
      const firstChar = product.product_desc.charAt(0).toLowerCase();
      if ("abc".includes(firstChar)) {
        categories["a-c"].push(product);
      } else if ("def".includes(firstChar)) {
        categories["d-f"].push(product);
      } else if ("ghi".includes(firstChar)) {
        categories["g-i"].push(product);
      } else if ("jkl".includes(firstChar)) {
        categories["j-l"].push(product);
      } else if ("mno".includes(firstChar)) {
        categories["m-o"].push(product);
      } else if ("pqr".includes(firstChar)) {
        categories["p-r"].push(product);
      } else if ("stu".includes(firstChar)) {
        categories["s-u"].push(product);
      } else if ("vwx".includes(firstChar)) {
        categories["v-x"].push(product);
      } else if ("yz".includes(firstChar)) {
        categories["y-z"].push(product);
      }
    });

    setCharacterwiseProducts(categories);

    if (!isInitial) {
      const filtered = selectedProducts.filter((prod) => {
        return products.some((pr) => pr.pkey === prod.key);
      });
      setSelectedProducts(filtered);
    } else {
      setSelectedProducts(currentData.map((cd) => {
        return { key: cd.pkey, label: cd.product_desc };
      }));
    }
  };

  useEffect(() => {
    setIsProductDropDownOpen(false);
    setSelectedProducts(
      currentData.map((cd) => {
        return { key: cd.pkey, label: cd.product_desc };
      })
    );
    
  }, [currentData]);

  useEffect(() => {
    let productsString = "";
    if (selectedProducts.length > 1) {
      productsString =
        (selectedProducts[0].label + ", " + selectedProducts[1].label).slice(
          0,
          15
        ) + "...";
    } else if (selectedProducts.length > 0) {
      productsString = selectedProducts[0].label.slice(0, 15);
    }

    setSelectedProductsString(productsString);
  }, [selectedProducts]);

  useEffect(() => {
    const fetchAllProducts = async () => {
      setSearchTerm("");
      setSelectWhereTotalIsZeroTemp(selectWhereTotalIsZero);
      setSelectWhereTotalIsNotZeroTemp(selectWhereTotalIsNotZero);
      await fetchAllProductsWithFilters(null, null, true);
    };
    if (isProductDropDownOpen) {
      fetchAllProducts();
    }else{
      setSelectedProducts(
        currentData.map((cd) => {
          return { key: cd.pkey, label: cd.product_desc };
        })
      );
    }

  }, [isProductDropDownOpen]);

  return (
    <div className="flex-1">
      <Popover open={isProductDropDownOpen}>
        <PopoverTrigger disableButtonEnhancement>
          <div
            onClick={() => {
              setIsProductDropDownOpen((prev) => !prev);
            }}
            className="px-2 2xl:px-3 border border-[#cccccc] rounded-md w-full flex justify-between h-[36px] items-center" style={{fontFamily:"poppinsregular"}}
          >
            <span>{`${selectedProductsString}`}</span>
            <span className="inline-flex items-center rounded-full bg-[#00E0D5] px-2 py-1 text-xs font-bold text-white m-3 px-4">
              {selectedProducts.length}
            </span>
          </div>
        </PopoverTrigger>

        <PopoverSurface tabIndex={-1} className="w-[45%] !p-0 !mt-2" style={{fontFamily:"poppinsregular"}}>
          <div className="">
             <div className={styles.container}>
           
               
              {selectedProducts.length !== 0 ? (
                <TagGroup
                  onDismiss={removeItem}
                  aria-label="selected products"
                  className={styles.tags}
                >
                  {selectedProducts.map((tag) => (
                    <Tag
                      dismissible
                      dismissIcon={{ "aria-label": "remove", "className":"tagicon" }}
                      value={tag.key}
                      key={tag.key}
                      appearance="outline"
                      size="small"
                      shape="circular"
                      className="!border !border-skin-primary"
                    >
                      {tag.label}
                    </Tag>
                  ))}
                </TagGroup>
              )
              : <div className="nodata flex justify-center items-center py-2 font-semibold" style={{fontFamily:"poppinsregular"}}>
                  Atleast one product needs to be selected
                </div>
              }
            </div>
            <hr className="border-b border-gray-200 w-full" />
            <div className="px-4 pt-4">
              <input
                type="text"
                name=""
                className="px-2 2xl:px-3 border rounded-md w-full text-sm"
                placeholder="Search..."
                maxLength={200}
                value={searchTerm}
                onChange={searchChangeHandler}
                disabled={loading}
              />
              <div className="flex flex-row justify-between ">
                <span className="text-xs text-gray-400 pl-1 py-1">
                  {/* Can select max 10 products */}
                </span>
                <span
                  className="text-xs py-1 cursor-pointer text-skin-primary"
                  onClick={clearFilterHandler}
                >
                  Clear Filters
                </span>
              </div>
            </div>
            <hr className="border-b border-gray-200 w-full" />
           
            <div className="flex flex-column 2xl:flex-row gap-5 justify-between px-4 py-3">
              <div className="flex flex-row w-full py-3 gap-10">
                <div className="flex flex-row gap-3">
                  <label htmlFor="total0" className="cursor-pointer flex gap-3">
                  <input
                    type="checkbox"
                    id="total0"
                    checked={selectWhereTotalIsZeroTemp}
                    onChange={async (e) => {
                      const { checked } = e.target;
                      setSelectWhereTotalIsZeroTemp(checked);
                      await fetchAllProductsWithFilters(
                        checked ? "1" : "0",
                        null
                      );
                    }}
                    disabled={loading}
                  />
                    Where Total 0
                  </label>
                  <Tooltip
                    content="Total is sum of values in weeks in the selected quarter(s)"
                    relationship="label"
                    className="bg-white"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 512 512"
                      className="w-4 h-4"
                      fill="#000"
                    >
                      <path d="M256 32a224 224 0 1 1 0 448 224 224 0 1 1 0-448zm0 480A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM208 352c-8.8 0-16 7.2-16 16s7.2 16 16 16l96 0c8.8 0 16-7.2 16-16s-7.2-16-16-16l-32 0 0-112c0-8.8-7.2-16-16-16l-40 0c-8.8 0-16 7.2-16 16s7.2 16 16 16l24 0 0 96-32 0zm48-168a24 24 0 1 0 0-48 24 24 0 1 0 0 48z" />
                    </svg>
                  </Tooltip>
                </div>
                <div className="flex flex-row gap-3">
                <label htmlFor="totalnot0" className="cursor-pointer flex gap-3">
                  <input
                    type="checkbox"
                    id="totalnot0"
                    checked={selectWhereTotalIsNotZeroTemp}
                    onChange={async (e) => {
                      const { checked } = e.target;
                      setSelectWhereTotalIsNotZeroTemp(checked);
                      await fetchAllProductsWithFilters(
                        null,
                        checked ? "1" : "0"
                      );
                    }}
                    disabled={loading}
                  />
                  
                    Where Total Not 0
                  </label>
                  <Tooltip
                    content="Total is sum of values in weeks in the selected quarter(s)"
                    relationship="label"
                    className="bg-white"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 512 512"
                      className="w-4 h-4"
                      fill="#000"
                    >
                      <path d="M256 32a224 224 0 1 1 0 448 224 224 0 1 1 0-448zm0 480A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM208 352c-8.8 0-16 7.2-16 16s7.2 16 16 16l96 0c8.8 0 16-7.2 16-16s-7.2-16-16-16l-32 0 0-112c0-8.8-7.2-16-16-16l-40 0c-8.8 0-16 7.2-16 16s7.2 16 16 16l24 0 0 96-32 0zm48-168a24 24 0 1 0 0-48 24 24 0 1 0 0 48z" />
                    </svg>
                  </Tooltip>
                </div>
              </div>
              {/* <div className="flex flex-col w-1/2 py-3 gap-2">
                <div className="flex flex-row gap-3">
                  <input
                    type="radio"
                    id="finishedproduct"
                    name="rm"
                    checked={productType === "finishedproduct"}
                    onChange={() => {
                      setProductType("finishedproduct");
                    }}
                    disabled={loading}
                  />
                  <label htmlFor="finishedproduct" className="pointer-cursor">
                    Finished Product
                  </label>
                </div> */}
                {/* <div className="flex flex-row gap-3">
                  <input
                    type="radio"
                    id="rawmaterial"
                    name="rm"
                    checked={productType === "rawmaterial"}
                    onChange={() => {
                      setProductType("rawmaterial");
                    }}
                  />
                  <label htmlFor="rawmaterial" className="pointer-cursor">
                    Raw Material
                  </label>
                </div> */}
              {/* </div> */}
            </div>
            <div className="flex flex-row gap-4 bg-gray-100 text-sm justify-between mx-4 font-semibold">
              <span
                className={`${
                  selectedCharacterRange === "a-c" ? "text-skin-primary border-b-2 border-skin-primary" : ""
                } cursor-pointer py-2 px-4`}
                onClick={() => {
                  setSelectedCharacterRange("a-c");
                }}
              >
                {" "}
                A-C{" "}
              </span>
              <span
                className={`${
                  selectedCharacterRange === "d-f" ? "text-skin-primary border-b-2 border-skin-primary" : ""
                } cursor-pointer py-2 px-4`}
                onClick={() => {
                  setSelectedCharacterRange("d-f");
                }}
              >
                {" "}
                D-F{" "}
              </span>
              <span
                className={`${
                  selectedCharacterRange === "g-i" ? "text-skin-primary border-b-2 border-skin-primary" : ""
                } cursor-pointer py-2 px-4`}
                onClick={() => {
                  setSelectedCharacterRange("g-i");
                }}
              >
                {" "}
                G-I{" "}
              </span>
              <span
                className={`${
                  selectedCharacterRange === "j-l" ? "text-skin-primary border-b-2 border-skin-primary" : ""
                } cursor-pointer py-2 px-4`}
                onClick={() => {
                  setSelectedCharacterRange("j-l");
                }}
              >
                {" "}
                J-L{" "}
              </span>
              <span
                className={`${
                  selectedCharacterRange === "m-o" ? "text-skin-primary border-b-2 border-skin-primary" : ""
                } cursor-pointer py-2 px-4`}
                onClick={() => {
                  setSelectedCharacterRange("m-o");
                }}
              >
                {" "}
                M-O{" "}
              </span>
              <span
                className={`${
                  selectedCharacterRange === "p-r" ? "text-skin-primary border-b-2 border-skin-primary" : ""
                } cursor-pointer py-2 px-4`}
                onClick={() => {
                  setSelectedCharacterRange("p-r");
                }}
              >
                {" "}
                P-R{" "}
              </span>
              <span
                className={`${
                  selectedCharacterRange === "s-u" ? "text-skin-primary border-b-2 border-skin-primary" : ""
                } cursor-pointer py-2 px-4`}
                onClick={() => {
                  setSelectedCharacterRange("s-u");
                }}
              >
                {" "}
                S-U{" "}
              </span>
              <span
                className={`${
                  selectedCharacterRange === "v-x" ? "text-skin-primary border-b-2 border-skin-primary" : ""
                } cursor-pointer py-2 px-4`}
                onClick={() => {
                  setSelectedCharacterRange("v-x");
                }}
              >
                {" "}
                V-X{" "}
              </span>
              <span
                className={`${
                  selectedCharacterRange === "y-z" ? "text-skin-primary border-b-2 border-skin-primary" : ""
                } cursor-pointer py-2 px-4`}
                onClick={() => {
                  setSelectedCharacterRange("y-z");
                }}
              >
                {" "}
                Y-Z{" "}
              </span>
            </div>
            <div className="h-52 flex flex-col gap-2 overflow-x-auto my-2 px-2 mx-4">
              {characterwiseProducts[selectedCharacterRange].map((prod) => {
                return (
                  <label
                    key={prod.pkey}
                    htmlFor={`${prod.pkey}`}
                    className="flex flex-row gap-2 cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      id={`${prod.pkey}`}
                      checked={selectedProducts.some(
                        (product) => product.key === prod.pkey
                      )}
                      onChange={(e) => {
                        const { checked } = e.target;

                        checkProductHandler(
                          checked,
                          prod.pkey,
                          prod.product_desc
                        );
                      }}
                      disabled={loading}
                    />
                    {prod.product_desc}
                  </label>
                );
              })}
              {characterwiseProducts[selectedCharacterRange].length <= 0 && (
                <div className="nodata flex justify-center items-center py-2 font-semibold">
                  No matching products to display
                </div>
              )}
            </div>
            <div className="flex flex-row gap-4 bg-gray-200 py-2 px-4 text-sm justify-end">
              <button
                onClick={() => {
                  setIsProductDropDownOpen(false);
                }}
                className="border-skin-primary border bg-white text-skin-primary rounded-lg px-6 py-1 font-bold"
              >
                Close
              </button>
              <button
                onClick={filterSearchHandler}
                className="bg-skin-primary text-white rounded-lg px-6 py-1 font-bold"
                disabled={loading}
              >
                Search
              </button>
            </div>
          </div>
        </PopoverSurface>
      </Popover>
    </div>
  );
};

export default ProductDropDown;
