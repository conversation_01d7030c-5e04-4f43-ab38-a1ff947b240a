import Layout from "@/components/Layout";
import React, {
  useState,
  useRef,
  useEffect,
  useMemo,
  useCallback,
} from "react";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles//ag-grid.css";
import "ag-grid-community/styles//ag-theme-alpine.css";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSearch } from "@fortawesome/free-solid-svg-icons";
import { useLoading } from "@/utils/loaders/loadingContext";
import { useRouter } from "next/router";
import { apiConfig } from "@/services/apiConfig";
import productActionRenderer from "@/utils/renderer/productActionRenderer";
import {
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogBody,
  DialogActions,
  DialogContent,
  Button as FluentButton,
  Input,
  Switch,
  FluentProvider,
  webLightTheme,
} from "@fluentui/react-components";
import AddProductDialog from "@/components/AddProductDialog";

const PPEConsumables = ({ userData }) => {
  console.log("PARETN component rendered", userData);
  const router = useRouter();
  const [rowData, setRowData] = useState([]);
  const [requestRowData, setRequestRowData] = useState([]);
  const [pageSize, setPageSize] = useState(15);
  const gridRef = useRef();
  const { setIsLoading } = useLoading();
  const [searchInput, setSearchInput] = useState("");
  const [blockScreen, setBlockScreen] = useState(false);
  const [selectedView, setSelectedView] = useState("Orders");
  const [orderView, setOrderView] = useState(true);

  //#region pop up states
  const [showAddProduct, setShowAddProduct] = useState(false);
  const [productName, setProductName] = useState("");
  const [printing, setPrinting] = useState(false);
  const [sizes, setSizes] = useState([""]);
  const [newSize, setNewSize] = useState("");
// const [showAddProduct, setShowAddProduct] = useState(false);


  // const [draggedIdx, setDraggedIdx] = useState(null);

  // // Handler functions:
  // const handleDragStart = (idx) => setDraggedIdx(idx);

  // const handleDragOver = (e, idx) => {
  //   e.preventDefault();
  //   if (draggedIdx === null || draggedIdx === idx) return;
  //   const updated = [...sizes];
  //   const [removed] = updated.splice(draggedIdx, 1);
  //   updated.splice(idx, 0, removed);
  //   setDraggedIdx(idx);
  //   setSizes(updated);
  // };

  // const handleDragEnd = () => setDraggedIdx(null);

  // Handler to add a new size
  const handleAddSize = () => {
    if (newSize.trim() !== "") {
      setSizes([...sizes, newSize]);
      setNewSize("");
    }
  };

  // Handler to remove a size
  const handleRemoveSize = (idx) => {
    setSizes(sizes.filter((_, i) => i !== idx));
  };

  // Handler for dialog submit
  const handleProductSubmit = () => {
    // You can handle the submit logic here (e.g., API call)
    console.log({ productName, printing, sizes });
    setShowAddProduct(false);
    setProductName("");
    setPrinting(false);
    setSizes([""]);
    setNewSize("");
  };

  //endredtion pop up states

  //#region call get data
  useEffect(() => {
    if (typeof document !== "undefined") {
      document.title = "Products";
    }

    setIsLoading(false);
    getData()
      .then((data) => {
        console.log("data", data);
        const formattedData = data?.map((row) => ({
          id: row.id,
          request_id: row.request_id,
          required_date: new Date(row.required_date)
            .toISOString()
            .split("T")[0],
          site_name: row.site_name,
          requestor: row.actioned_by_email,
          status:
            row.action_id === 1
              ? "Pending"
              : row.action_id === 2
              ? "Approved"
              : "Rejected",
        }));
        console.log("Formatted Data:", formattedData);
        setRowData(formattedData);
        setRequestRowData(formattedData);
      })
      .catch((error) => {
        return error;
      });
  }, []);

  useEffect(() => {
    const fetchProducts = async () => {
      if (selectedView === "Orders") {
        setRowData(requestRowData);
      } else {
        const productsJson = await getAllProducts();
        console.log("Fetched Products JSON:", productsJson);
        if (productsJson && productsJson.length > 0) {
          const formattedProductData = productsJson.map((product) => ({
            product_id: product.ProductId,
            product_name: product.ProductName || "Unnamed Product",
            category: product.ProductType || "Uncategorized",
            stock: 0,
            unit: deriveUnitFromPackageQuantity(product.PackageQuantity),
            status: "Available",
          }));
          setRowData(formattedProductData);
        } else {
          const fallbackProductData = [
            {
              product_name: "Nitrile Gloves",
              category: "Gloves",
              stock: 1200,
              unit: "Box",
              status: "Available",
            },
            // ... other fallback products
          ];
          setRowData(fallbackProductData);
        }
      }
    };
    fetchProducts();
  }, [selectedView]);
  // }, [typeId]);

  // #region get data
  function getData() {
    setRowData([]);
    setRequestRowData([]);
    let serverAddress = apiConfig.serverAddress;
    const company_name =
      userData?.company || extractCompanyFromEmail(userData?.email);
    const AdCompany = userData?.companyName || userData?.ADCompanyName;
    return fetch(`${serverAddress}ppe-consumables/all-ppe-requests`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include", // Use session authentication
    })
      .then(async (res) => {
        if (res.status == 502) {
          setBlockScreen(true);
          return;
        }
        setBlockScreen(false);
        if (res.status === 200) {
          return res.json();
        }
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async () => {
            await logout();
            router.push("/login");
          }, 3000);
        }
        throw new Error("Failed to fetch data");
      })
      .catch((error) => {
        console.error(error);
      });
  }
  function getAllProducts() {
  setRowData([]);
  let serverAddress = apiConfig.serverAddress;
  const company_name =
    userData?.company || extractCompanyFromEmail(userData?.email);
  const AdCompany = userData?.companyName || userData?.ADCompanyName;
  return fetch(`${serverAddress}ppe-consumables/all-products`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include", // Use session authentication
  })
    .then(async (res) => {
      if (res.status == 502) {
        setBlockScreen(true);
        return;
      }
      setBlockScreen(false);
      if (res.status === 200) {
        return res.json();
      }
      if (res.status === 400) {
        toast.error(
          "There was an error with your request. Please check your data and try again."
        );
      } else if (res.status === 401) {
        toast.error("Your session has expired. Please log in again.");
        setTimeout(async () => {
          await logout();
          router.push("/login");
        }, 3000);
      }
      throw new Error("Failed to fetch products");
    })
    .catch((error) => {
      console.error(error);
    });
}

  //#region column def
  // Example column definitions for Products

  const productColumnDefs = [
    { headerName: "Product Name", field: "product_name", flex: 1 },
    { headerName: "Category", field: "category", flex: 1 },
    { headerName: "Stock", field: "stock", flex: 1 },
    { headerName: "Unit", field: "unit", flex: 1 },
    { headerName: "Status", field: "status", flex: 1 },
    // Add more columns as needed
  ];

  // Example column definitions
  const requestColumnDefs = useMemo(
    () => [
      // { headerName: "ID", field: "id", flex: 1 },
      {
        headerName: "Request ID",
        field: "request_id",
        flex: 1,
        hide: !orderView,
      },
      {
        headerName: "Required Date",
        field: "required_date",
        flex: 1,
        hide: !orderView,
      },
      {
        headerName: "Type of PPE",
        field: "site_name",
        flex: 1,
        hide: !orderView,
      },
      { headerName: "Site", field: "site_name", flex: 1, hide: !orderView },
      { headerName: "Size", field: "site_id", flex: 1, hide: !orderView },
      { headerName: "Quantity", field: "site_id", flex: 1, hide: !orderView },
      {
        headerName: "Requestor",
        field: "requestor",
        flex: 1,
        hide: !orderView,
      },
      { headerName: "Comment", field: "site_id", flex: 1, hide: !orderView },
      { headerName: "Status", field: "status", flex: 1, hide: !orderView },

      //product view
      {
        headerName: "Product Name",
        field: "product_name",
        flex: 1,
        hide: orderView,
      },
      { headerName: "Category", field: "category", flex: 1, hide: orderView },
      // { headerName: "Stock", field: "stock", flex: 1, hide: orderView },
      { headerName: "Sizes", field: "unit", flex: 1, hide: orderView },
      {
        headerName: "name Printable",
        field: "status",
        flex: 1,
        hide: orderView,
      },

      //common column for both views
      {
        headerName: "Actions",
        field: "site_id",
        flex: 1,
        cellRenderer: (params) =>
          productActionRenderer(
            params,
            userData
            // company,
            // typeId,
            // setIsLoading,
            // isIssUser,
            // isIssProcurementTeamUser,
            // isIssAdmin,
            // superAdmin
          ),
        // flex: "2%",
        // cellStyle: () => ({}),
        // cellStyle: { justifyContent: "end", paddingRight: "10px" },
        sortable: false,
      },
      // Add more columns as needed
    ],
    [orderView]
  );

  const defaultColDef = useMemo(
    () => ({
      sortable: true,
      filter: false,
      resizable: true,
      flex: 1,
      suppressMenu: false,
    }),
    []
  );

  useEffect(() => {
    setIsLoading(false);
    setOrderView(selectedView === "Orders");
    console.log(
      "Selected View:",
      selectedView,
      "Order View:",
      orderView,
      selectedView === "Orders"
    );
  }, [selectedView]);
  const handleGridReady = (params) => {
    params.api.setColumnDefs(requestColumnDefs);
  };
  useEffect(() => {
    if (gridRef.current && gridRef.current.api) {
      gridRef.current.api.setColumnDefs(requestColumnDefs);
    }
  }, [requestColumnDefs]);

  const onFilterTextBoxChanged = useCallback(() => {
    gridRef.current.api.setQuickFilter(
      document.getElementById("filter-text-box").value
    );
    setSearchInput(document.getElementById("filter-text-box").value);
  }, []);

  const handlePageSizeChange = (event) => {
    const newPageSize = parseInt(event.target.value, 15);
    setPageSize(newPageSize);
    3;
    gridRef.current.api.paginationSetPageSize(newPageSize);
  };

  return (
    <>
      <ToastContainer limit={1} />
      <Layout userData={userData}>
        <div className="mr-20 md:mr-12 lg:mr-14">
          <div className="flex flex-row md:flex-col lg:flex-row justify-between">
            <div className="flex items-center gap-4">
              <div className="inline-flex rounded-full bg-gray-200 p-1">
                <button
                  className={`px-4 py-1 rounded-full text-sm font-medium transition ${
                    selectedView === "Orders"
                      ? "bg-white shadow text-black"
                      : "text-gray-500 hover:text-black"
                  }`}
                  onClick={() => setSelectedView("Orders")}
                >
                  Requests
                </button>
                <button
                  className={`px-4 py-1 rounded-full text-sm font-medium transition ${
                    selectedView === "Products"
                      ? "bg-white shadow text-black"
                      : "text-gray-500 hover:text-black"
                  }`}
                  onClick={() => setSelectedView("Products")}
                >
                  Products
                </button>
              </div>
              <label className="relative block w-[47vh] text-gray-400  mt-0 py-2">
                <span className="absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black">
                  <FontAwesomeIcon icon={faSearch} className="fw-bold" />
                </span>
                <input
                  type="text"
                  id="filter-text-box"
                  placeholder="Search..."
                  onInput={onFilterTextBoxChanged}
                  value={searchInput}
                  className="block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none shadow-[0_0_5px_rgba(0,0,0,0.1)]"
                />
              </label>
            </div>
            <button
              className="ml-2 my-2 px-3 py-1 p-[6px] border rounded-md bg-skin-primary text-white text-sm cursor-pointer"
              onClick={() => {
                if (selectedView === "Orders") {
                  router.push("/ppe-consumable/add");
                } else {
                  setShowAddProduct(true);
                }
              }}
            >
              {orderView ? "New Order" : "Add Product"}
            </button>
          </div>




<AddProductDialog
  open={showAddProduct}
  onOpenChange={(_, data) => setShowAddProduct(data.open)}
  onSubmit={({ productName, printing, sizes }) => {
    // handle your submit logic here
    console.log({ productName, printing, sizes });
    setShowAddProduct(false);
  }}
/>
          {/* <FluentProvider
            theme={webLightTheme}
            className="!bg-transparent"
            style={{ fontFamily: "poppinsregular" }}
          >
            <Dialog
              open={showAddProduct}
              onOpenChange={(_, data) => setShowAddProduct(data.open)}
            >
              <DialogSurface className="!bg-white">
                <DialogBody>
                  <DialogTitle>Add New Product</DialogTitle>
                  <DialogContent>
                    <div style={{ marginBottom: 12 }}>
                      <input
                        placeholder="Product Name"
                        value={productName}
                        onChange={(e) => setProductName(e.target.value)}
                        className="flex w-full p-2 mt-2 border rounded-md"
                        // style={{ width: "100%" }}
                      ></input>
                    </div>
                    <div style={{ marginBottom: 12 }}>
                      <Switch
                        checked={printing}
                        onChange={(_, data) => setPrinting(data.checked)}
                        label="Printing"
                      />
                    </div>
                    <div>
                      <div className="!flex !flex-row !justify-between !items-center mb-2">
                        <label>Sizes:</label>
                        <label className="!text-xs !text-red-500">
                          add sizes in order you want them to be displayed
                        </label>
                      </div>
                      <div style={{ display: "flex", gap: 8, marginBottom: 8 }}>
                        <input
                          placeholder="Add size"
                          value={newSize}
                          onChange={(e) => setNewSize(e.target.value)}
                          className="flex p-2 border rounded-md"
                          style={{ flex: 1 }}
                        ></input>
                        <button
                          className="bg-skin-primary p-1 px-3 text-white rounded-md"
                          onClick={handleAddSize}
                        >
                          Add Size Option
                        </button>
                      </div>

                      <ul className="space-y-2">
                        {sizes
                          .filter((s) => s)
                          .map((size, idx) => (
                            <li
                              key={size + idx}
                              className={`flex items-center justify-between bg-gray-100 rounded-md px-3 py-1 transition-shadow ${
                                draggedIdx === idx
                                  ? "ring-2 ring-skin-primary"
                                  : ""
                              }`}
                              draggable
                              onDragStart={() => handleDragStart(idx)}
                              onDragOver={(e) => handleDragOver(e, idx)}
                              onDragEnd={handleDragEnd}
                              style={{ cursor: "grab" }}
                            >
                              <span className="inline-block bg-skin-primary/10 text-skin-primary font-medium px-3 py-1 rounded-full text-xs">
                                {size}
                              </span>
                              <FluentButton
                                size="small"
                                appearance="subtle"
                                icon={
                                  <span className="fa fa-trash text-red-500" />
                                }
                                onClick={() => handleRemoveSize(idx)}
                                className="ml-2"
                                aria-label="Remove size"
                              >
                                Remove
                              </FluentButton>
                            </li>
                          ))}
                      </ul>
                    </div>
                  </DialogContent>
                  <DialogActions>
                    <FluentButton
                      appearance="secondary"
                      onClick={() => setShowAddProduct(false)}
                    >
                      Cancel
                    </FluentButton>
                    <button
                      className="bg-skin-primary text-white p-2 rounded-md"
                      onClick={handleProductSubmit}
                    >
                      Add Product
                    </button>
                  </DialogActions>
                </DialogBody>
              </DialogSurface>
            </Dialog>
          </FluentProvider> */}
          <div className="my-5">
            <div
              className="relative ag-theme-alpine !rounded-md"
              style={{ height: "calc(100vh - 151px)" }}
            >
              <AgGridReact
                rowData={requestRowData}
                ref={gridRef}
                requestColumnDefs={requestColumnDefs}
                defaultColDef={defaultColDef}
                suppressRowClickSelection
                pagination={true}
                paginationPageSize={pageSize}
                onPageSizeChanged={handlePageSizeChange}
                tooltipShowDelay={0}
                tooltipHideDelay={1000}
                onGridReady={handleGridReady}
              />
              <div className="flex justify-start mt-2 pagination-style">
                <label htmlFor="page-size-select pagination" className="inputs">
                  Show{" "}
                  <select
                    id="page-size-select"
                    onChange={handlePageSizeChange}
                    value={pageSize}
                    className="focus:outline-none"
                  >
                    <option value={10}>10</option>
                    <option value={15}>15</option>
                    <option value={25}>25</option>
                    <option value={50}>50</option>
                    <option value={100}>100</option>
                  </select>{" "}
                  Entries
                </label>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default PPEConsumables;

export const getServerSideProps = async (context) => {
  try {
    const { req, resolvedUrl } = context;
    const sessionId = req.cookies.thl_session;
    // const previousPage = req.cookies.PreviousPage || "RM";

    if (!sessionId) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(resolvedUrl)}`,
          permanent: false,
        },
      };
    }

    const apiBase =
      process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8081";

    const response = await fetch(`${apiBase}/api/auth/me`, {
      method: "GET",
      headers: {
        Cookie: `thl_session=${sessionId}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(resolvedUrl)}`,
          permanent: false,
        },
      };
    }

    const { user } = await response.json();

    // Determine pageTypeId and PreviousPage value
    // const pageTypeId =
    //   previousPage === "PK" ? 4 : previousPage === "NV" ? 3 : 1;

    return {
      props: {
        userData: user,
        // PreviousPage: pageTypeId,
        // pageTypeId,
      },
    };
  } catch (error) {
    console.error("Error in getServerSideProps:", error);
    return {
      redirect: {
        destination: "/login",
        permanent: false,
      },
    };
  }
};

// Helper function to derive unit from package quantity
function deriveUnitFromPackageQuantity(packageQuantity) {
  if (!packageQuantity) return "Unit";
  // Example logic: if packageQuantity contains "box", "pack", etc.
  const lower = packageQuantity.toLowerCase();
  if (lower.includes("box")) return "Box";
  if (lower.includes("pack")) return "Pack";
  if (lower.includes("pair")) return "Pair";
  // Add more rules as needed
  return "Unit";
}
