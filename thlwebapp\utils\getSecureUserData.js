// Secure replacement for getCookieData
// This provides backward compatibility during migration

import { getCurrentUser } from './secureStorage';

// Cache user data to avoid multiple API calls
let cachedUser = null;
let cacheTimestamp = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Get current user data from secure session (replaces getCookieData("user"))
export const getSecureUserData = async () => {
  // Check cache first
  if (cachedUser && cacheTimestamp && (Date.now() - cacheTimestamp < CACHE_DURATION)) {
    return cachedUser;
  }

  try {
    const user = await getCurrentUser();
    if (user) {
      cachedUser = user;
      cacheTimestamp = Date.now();
      return user;
    }
    return null;
  } catch (error) {
    console.error('Failed to get secure user data:', error);
    return null;
  }
};

// Clear cached user data
export const clearUserCache = () => {
  cachedUser = null;
  cacheTimestamp = null;
};

// Synchronous fallback for immediate access (uses cache)
export const getSecureUserDataSync = () => {
  return cachedUser;
};

// Backward compatibility: replaces getCookieData function
export const getCookieDataSecure = async (cookieName) => {
  switch (cookieName) {
    case 'user':
      return await getSecureUserData();
    
    case 'theme':
      // Temporary: Get from localStorage during migration
      if (typeof window !== 'undefined') {
        try {
          const theme = localStorage.getItem('tempTheme');
          return theme ? JSON.parse(theme) : null;
        } catch (error) {
          return localStorage.getItem('tempTheme');
        }
      }
      return null;
    
    case 'company':
      // Temporary: Get from localStorage during migration
      if (typeof window !== 'undefined') {
        return localStorage.getItem('tempCompany');
      }
      return null;
    
    case 'ADCompanyName':
      // Temporary: Get from localStorage during migration
      if (typeof window !== 'undefined') {
        return localStorage.getItem('tempADCompanyName');
      }
      return null;
    
    default:
      console.warn(`getCookieDataSecure: Unknown cookie '${cookieName}'. Consider using specific secure methods.`);
      return null;
  }
};

// Helper: Get user property safely
export const getUserProperty = async (property) => {
  const user = await getSecureUserData();
  if (!user) return null;
  
  // Map old property names to new structure
  const propertyMap = {
    'user_id': 'id',
    'role_id': 'role',
    'email': 'email', 
    'name': 'name'
  };
  
  const mappedProperty = propertyMap[property] || property;
  return user[mappedProperty];
};

// Helper: Check if user is authenticated
export const isUserAuthenticated = () => {
  return !!cachedUser;
};

// Helper: Get user role
export const getUserRole = async () => {
  return await getUserProperty('role');
};

// Helper: Check if user is super user
export const isSuperUser = async () => {
  const role = await getUserRole();
  return role === 1; // Adjust based on your role system
}; 