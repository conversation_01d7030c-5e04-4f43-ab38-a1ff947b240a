-- DECLARE @company VARCHAR(50) = 'flrs'
-- DECLARE @varietyName VARCHAR(50) = 'Variety11'
-- DECLARE @code VARCHAR(50) = 'CVariety11'
-- DECLARE @description VARCHAR(50) = 'testing11'
-- DECLARE @masterProductCode INT = 12
-- DECLAR<PERSON> @actionId INT = 2
-- DECLARE @actionedByEmail VARCHAR(100) = '<EMAIL>'
-- DECLARE @actionedByName VARCHAR(100) = 'test1 test'
-- DECLARE @comment VARCHAR(100) = 'Test11'
-- DECLARE @reqNumber VARCHAR(100) = '237'
-- DECLARE @varietyId INT = 6

BEGIN TRANSACTION;

	BEGIN TRY

		DECLARE @currentActiveAction INT;
		DECLARE @isAllowed BIT = 0;
		DECLARE @departmentId INT = 0;
		DECLARE @roleId INT = 0;

		SELECT @currentActiveAction = action_id FROM product_nvariety_status WHERE variety_id = @varietyId and is_latest=1

		IF @currentActiveAction = 1 AND (@actionId = 1 OR @actionId = 2)
		BEGIN
			SET @isAllowed = 1
		END
		ELSE IF @currentActiveAction = 5 AND (@actionId = 11)
		BEGIN
			SET @isAllowed = 1
		END
		ELSE IF @currentActiveAction = 2 AND (@actionId = 3 OR @actionId = 4)
		BEGIN
			SET @isAllowed = 1
		END
		ELSE IF @currentActiveAction = 4 AND @actionId = 2
		BEGIN
			SET @isAllowed = 1
		END
		ELSE IF @currentActiveAction = 4 AND @actionId = 1
		BEGIN
			SET @isAllowed = 1
		END
		ELSE IF @currentActiveAction = 3 AND (@actionId =11 OR @actionId =5)
		BEGIN
			SET @isAllowed = 1
		END
		ELSE IF @currentActiveAction = 1 OR @currentActiveAction = 2 OR @currentActiveAction = 3 OR @currentActiveAction = 4 AND @actionId = 6
		BEGIN
			SET @isAllowed = 1
		END
		
		IF @isAllowed = 0
		BEGIN
			RAISERROR('Invalid request.', 16, 1);
		END

		IF(@actionId!=4 AND @actionId!=6 AND @actionId!=11)
		BEGIN
			UPDATE product_nvariety
			SET
				code = @code,
				[description] = @description,
				master_product_code = @masterProductCode
			WHERE request_no = @reqNumber
		END

		IF(@actionId=6)
		BEGIN
			UPDATE product_nvariety
			SET is_active=0
			WHERE id = @varietyId
		END

		UPDATE product_nvariety_status
		SET is_latest = 0
		WHERE variety_id = @varietyId;

	SELECT @departmentId = department_id FROM users WHERE email = @actionedByEmail

IF (@actionId = 2 AND @departmentId = 5)
    BEGIN
		INSERT INTO
			product_nvariety_status (
				variety_id,
				action_id,
				actioned_by_email,
				actioned_by_name,
				comment,
				is_latest
			) 
		VALUES
			(
				@varietyId,
				@actionId,
				@actionedByEmail,
				@actionedByName,
				@comment,
				0
			);
	INSERT INTO
		product_nvariety_status (
			variety_id,
			action_id,
			actioned_by_email,
			actioned_by_name,
			comment
		) 
	VALUES
		(
			@varietyId,
			3,
			@actionedByEmail,
			@actionedByName,
			'Approved by default as the application is created by a technical person'
		);
END
    ELSE
    BEGIN
	INSERT INTO
			product_nvariety_status (
				variety_id,
				action_id,
				actioned_by_email,
				actioned_by_name,
				comment
			) 
		VALUES
			(
				@varietyId,
				@actionId,
				@actionedByEmail,
				@actionedByName,
				COALESCE(@comment,NULL)
			);
			END
    COMMIT TRANSACTION;
    PRINT 'Transaction committed.';
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;

    PRINT 'Transaction rolled back.';
    PRINT ERROR_MESSAGE();
END CATCH;

