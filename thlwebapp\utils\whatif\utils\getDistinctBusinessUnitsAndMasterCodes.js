// Function to return distinct Business_unit and Master_code lists
const getDistinctBusinessUnitsAndMasterCodes = async (products) => {
  const businessUnitsSet = new Set();
  const masterCodesSet = new Set();

  products.forEach((product) => {
    if (product.Business_unit) {
      businessUnitsSet.add(product.Business_unit);
    }
    if (product.Master_code) {
      masterCodesSet.add(product.Master_code);
    }
  });

  return {
    businessUnits: Array.from(businessUnitsSet),
    masterCodes: Array.from(masterCodesSet),
  };
};

export default getDistinctBusinessUnitsAndMasterCodes;
