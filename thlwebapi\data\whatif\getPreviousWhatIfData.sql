select
    wf.[id] as what_if_id,
    wf.[task_type_id],
    wf.[start_week_no],
    wf.[start_week],
    wf.[end_week_no],
    wf.[pkey],
    wf.[product_name],
    wf.[breakeven_status_id],
    wf.[fiscal_year],
    wf.[description],
    wf.[is_confirmed],
    wf.[is_deleted] as isPromoDeleted,
    wfd.[id] as what_if_details_id,
    wfd.[whatif_id],
    wfd.[week_no],
    wfd.[new_volume],
    wfd.[new_be],
    wfd.[new_price],
    wfd.[new_gp],
    wfd.[new_sales],
    wfd.[new_gp_percentage],
    wfd.[is_deleted] as isPromoWeekDeleted,
    wf.[created_by],
    wftt.type as task_type_name,
    wfbs.name as breakeven_status_name
from
    [THL_Webapp_Portal].[dbo].[whatif] wf
    left join [THL_Webapp_Portal].[dbo].[whatif_details] wfd on wf.id = wfd.whatif_id
    left join [THL_Webapp_Portal].[dbo].[whatif_task_type] wftt on wf.task_type_id = wftt.id
    left join [THL_Webapp_Portal].[dbo].[whatif_breakeven_status] wfbs on wf.breakeven_status_id = wfbs.id
where
    wf.id = @what_if_id and
    wf.[is_deleted] = 0
    and wfd.[is_deleted] = 0