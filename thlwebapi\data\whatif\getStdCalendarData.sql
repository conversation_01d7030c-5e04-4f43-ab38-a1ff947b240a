DECLARE @QuarterList TABLE (FiscalQuarter INT);
DECLARE @Quarters VARCHAR(MAX);
SET
    @Quarters = @quartersSelected;

INSERT INTO
    @QuarterList
SELECT
    value
FROM
    STRING_SPLIT(@Quarters, ',');

SELECT
    std.[fiscalweek],std.[fiscalquarter] std_fiscalquarter,offcal.fiscalquarter as fiscalquarter
FROM
    [FLR_DEV_TEST_off_BI_lookup].[dbo].[off_cal_start_end_week] std
    LEFT JOIN [FLR_DEV_TEST_off_BI_lookup].[dbo].[off_cal_start_end_week] offcal ON std.off_fiscal_wk = offcal.fiscalweek
    AND std.fiscalyear = offcal.fiscalyear
    AND offcal.calendar_name = 'OFF Financial Calendar'
WHERE
    std.calendar_name = 'StdYearlyCalendar'
    AND std.fiscalyear = @financialYear
    AND offcal.fiscalquarter IN (
        SELECT
            FiscalQuarter
        FROM
            @QuarterList
    )
ORDER BY
    offcal.fiscalquarter,
    std.fiscalweek;