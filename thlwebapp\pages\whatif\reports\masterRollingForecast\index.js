import Layout from "@/components/Layout";
import React, { useEffect, useState } from "react";

export default function MasterRollingForecast({userData}) {
  return (
    <Layout userData={userData}>
      {/* <Link href="/whatif" >
            <div className='bg-white border border-theme-blue2 rounded-full px-4 py-1 text-theme-blue2 w-[150px]'>Forecast Planner</div>
      </Link> */}
      <div className="flex justify-center items-center mr-[55px] mt-5">
        {/* <iframe
          className="h-[calc(100vh-100px)]"
          title="EFC Master Rolling Forecast Report"
          width="100%"
          height="680"
          src="
https://app.powerbi.com/reportEmbed?reportId=a397f90a-1f15-4699-a3c0-ee4d3fb2e491&autoAuth=true&ctid=0a6ac99f-7136-40dd-9ec3-ddedf46edbc9"
          frameborder="0"
          allowFullScreen="true"
        ></iframe> */}

        <iframe
          title="EFC Master Rolling Forecast Report- Live Promos"
          width="100%"
         className="h-[85vh]"
          src="https://app.powerbi.com/reportEmbed?reportId=5d1937b0-d123-46b1-9954-1d3000d8d7b5&autoAuth=true&ctid=0a6ac99f-7136-40dd-9ec3-ddedf46edbc9"
          frameborder="0"
          allowFullScreen="true"
        ></iframe>
      </div>
    </Layout>
  );
}

export const getServerSideProps = async (context) => {
  try {
    // Use secure session validation
    const sessionId = context.req.cookies.thl_session;

    if (!sessionId) {
    return {
      redirect: {
          destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
        permanent: false,
      },
    };
  }

    // Validate session with our backend API
    const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8081';
    
    try {
      const response = await fetch(`${apiBase}/api/auth/me`, {
        method: 'GET',
        headers: {
          'Cookie': `thl_session=${sessionId}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        // Session invalid or expired - redirect to login
        return {
          redirect: {
            destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
            permanent: false,
          },
        };
      }

      const { user } = await response.json();

  return {
    props: {
          userData: user,
        },
      };

    } catch (fetchError) {
      console.error('Session validation failed:', fetchError);
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
          permanent: false,
        },
      };
    }

  } catch (error) {
    console.error('Authentication error:', error);
    return {
      redirect: {
        destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
        permanent: false,
      },
    };
  }
};
