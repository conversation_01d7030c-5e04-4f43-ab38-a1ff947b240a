import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPenToSquare
} from "@fortawesome/free-solid-svg-icons";
import { useRouter } from "next/router";
import { apiConfig } from "@/services/apiConfig";
import { useState} from "react";
import "react-toastify/dist/ReactToastify.css";

const productReferenceRenderer = (params, userData, isUpdateMode) => {
  
  const editProduct = (params) => {
    if (typeof window !== "undefined") {
        params.setCode(params.data.code);
        params.setDescription(params.data.label);
        params.setValue(params.data.value);
        params.setIsButtonDisabled(false);
        params.setIsUpdateMode(true); 
    }
  };

  return ( 
    <>
      <div className="flex flex-row gap-4 justify-center text-blue-500">
        {params.data.is_new == true &&
            <button
            onClick={() => editProduct(params)}
            disabled = {isUpdateMode}
            >
            <FontAwesomeIcon
                icon={faPenToSquare}
                size="lg"
                title="Edit Product"
                className="text-skin-primary"
            />
            </button>
        }
      </div>
    </>
  );
};

export default productReferenceRenderer;
