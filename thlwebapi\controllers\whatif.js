"use strict";

const whatif = require("../data/whatif");
const logger = require("../utils/logger");
const ExcelJS = require("exceljs");
const fs = require("fs");
const path = require("path");

const getWhatifByCustomer = async (req, res, next) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const cust_code = req?.params?.cust_code;
    const quartersSelected = req.query.quarters;
    const whatifByCustomer = await whatif.getWhatifByCustomer(
      cust_code,
      quartersSelected,
      name,
      email
    );
    res.send(whatifByCustomer);
  } catch (error) {
    console.error("error in getWhatifByCustomer", error);
    res.status(400).send(error.message);
  }
};
const getWhatifTotalsByCustomer = async (req, res, next) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const cust_code = req?.params?.cust_code;
    const whatifByCustomer = await whatif.getWhatifTotalsByCustomer(
      cust_code,
      name,
      email
    );
    res.send(whatifByCustomer);
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const getWhatifProductByCustomer = async (req, res, next) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const cust_code = req?.params?.cust_code;
    const getWhatifProductByCustomer =
      await whatif.getWhatifProductByCustomer(cust_code, name, email);
    res.send(getWhatifProductByCustomer);
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const getWhatifByQuarter = async (req, res, next) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const qrt_number = req?.params?.qrt_number;
    const getWhatifByQuarter = await whatif.getWhatifByQuarter(
      qrt_number,
      name,
      email
    );
    res.send(getWhatifByQuarter);
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const getDefaultCalendarData = async (req, res, next) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const quartersSelected = req.query.quarters;
    const getCalendarDataByName = await whatif.getDefaultCalendarData(
      quartersSelected,
      name,
      email
    );
    res.send(getCalendarDataByName);
  } catch (error) {
    res.status(400).send(error.message);
  }
};
const getstdCalendarData = async (req, res, next) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const quartersSelected = req.query.quarters;
    const stdCalendarData = await whatif.getstdCalendarData(
      quartersSelected,
      name,
      email
    );
    res.send(stdCalendarData);
  } catch (error) {
    res.status(400).send(error.message);
  }
};

//get list of customers
const getWhatifCustomers = async (req, res, next) => {
  //const {userData} = req.body
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const whatifCustomerData = await whatif.getWhatifCustomers(name, email);
    if (whatifCustomerData && whatifCustomerData?.length > 0) {
      res.send(whatifCustomerData);
    } else {
      res.status(404).send("No whatif data found");
    }
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const getWhatifProducts = async (req, res, next) => {
  //const {userData} = req.body
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const whatifCustomerData = await whatif.getWhatifCustomers(name, email);
    if (whatifCustomerData && whatifCustomerData?.length > 0) {
      res.send(whatifCustomerData);
    } else {
      res.status(404).send("No whatif data found");
    }
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const addWhatif = async (req, res, next) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const promotions = req?.body;
    const whatifPromotions = await whatif.addWhatif(promotions, name, email);
    // const pkeyParts = promotions.pkey.split("|");
    // const altfilid = pkeyParts[0];
    // const customer_code = pkeyParts[2];
    // const todayDate = new Date();
    // const currentMonth = todayDate.getMonth() + 1;
    // const currentYear = todayDate.getFullYear();
    // const financialYear = currentMonth >= 10 ? currentYear + 1 : currentYear;
    // const workbook = new ExcelJS.Workbook();
    // const worksheet = workbook.addWorksheet("WhatIf Data"); //Sheet name
    // const headers = [
    //   "account code - fpdemanddet.custcode",
    //   "altfil id - altfil.altfilid",
    //   "delivery date - fpdemanddet.deldate",
    //   "forecast cases - fpdemanddet.uomqty",
    //   "unit price - fpdemanddet_user.orddetnumeric1",
    //   "dp (distribution point) - fpdemanddet.dp",
    //   "order type - fpdemanddet.ordertype",
    //   "Exchange Rate - fpdemanddet_user.orddetnumeric2",
    //   "Promo Type - fpdemanddet.promotypeid",
    //   "Gross margin - fpdemanddet_user.orddetnumeric3",
    //   "Break Even - fpdemanddet_user.orddetnumeric4",
    // ];

    // worksheet.addRow(headers);
    // let currentDate = new Date(promotions.startWeekDate);

    // promotions.apiWeeksData.forEach((item) => {
    //   let weekStartDate = new Date(item.weekStartDate);
    //   let weekEndDate = new Date(item.weekEndDate);
    //   const timeDiff = weekEndDate - weekStartDate; // Difference in milliseconds
    //   const numberOfDays = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
    //   if (isNaN(weekStartDate) || isNaN(weekEndDate)) {
    //     console.error("Invalid date format for week start or end date.");
    //     return;
    //   }

    //   for (let day = 0; day < numberOfDays; day++) {
    //     const currentDay = new Date(weekStartDate);
    //     currentDay.setDate(weekStartDate.getDate() + day); // Increment the day

    //     const dayFormatted = String(currentDay.getDate()).padStart(2, "0");
    //     const monthFormatted = String(currentDay.getMonth() + 1).padStart(
    //       2,
    //       "0"
    //     );
    //     const yearFormatted = currentDay.getFullYear();

    //     const baseRow = [
    //       customer_code,
    //       altfilid,
    //       `${dayFormatted}-${monthFormatted}-${yearFormatted}`,
    //       parseFloat(item.newVolume) / numberOfDays,
    //       parseFloat(item.newPrice),
    //       6,
    //       3,
    //       1,
    //       1,
    //       parseFloat(item.newGp) / numberOfDays,
    //       parseFloat(item.newBe) / numberOfDays,
    //     ];
    //     worksheet.addRow(baseRow);
    //   }
    // });

    // // Define the file path
    // const filePath = path.join(
    //   __dirname,
    //   "exports", // Folder where the file will be saved
    //   `whatif_data_${financialYear}_${Date.now()}.xlsx`
    // );

    // // Ensure the directory exists
    // fs.mkdirSync(path.dirname(filePath), { recursive: true });

    // // Write workbook to the file system
    // await workbook.xlsx.writeFile(filePath);

      // console.log(`File successfully saved at ${filePath}`);

    // // Send a response to confirm success
    // res.status(200).send(`File successfully saved at ${filePath}`);
    res.send(whatifPromotions);

    //#end region export excel
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const removeWhatif = async (req, res, next) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const removeAllPromotion = req?.body;
    const whatif_id = removeAllPromotion.whatif_id;
    const productName = removeAllPromotion.productName;
    const fiscalYear = removeAllPromotion.fiscalYear;
    const whatifRemovePromotions = await whatif.removeWhatif(
      whatif_id,
      productName,
      fiscalYear,
      name,
      email
    );
    res.send(whatifRemovePromotions);
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const getCurrentQuarterCalendarData = async (req, res, next) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    // const calName = req?.params?.calName;
    const getCurrentQuarterCalendarData =
      await whatif.getCurrentQuarterCalendarData(name, email);
    res.send(getCurrentQuarterCalendarData);
  } catch (error) {
    res.status(400).send(error.message);
  }
};
const getCurrentQuarterStdCalendarData = async (req, res, next) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    // const calName = req?.params?.calName;
    const getCurrentQuarterStdCalendarData =
      await whatif.getCurrentQuarterStdCalendarData(name, email);
    res.send(getCurrentQuarterStdCalendarData);
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const getBreakevenStatuses = async (req, res, next) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const breakEvenStatues = await whatif.getBreakevenStatuses(name, email);
    res.send(breakEvenStatues);
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const getTaskTypes = async (req, res, next) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const taskTypes = await whatif.getTaskTypes(name, email);
    res.send(taskTypes);
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const removeLocks = async (req, res, next) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const whatifLocks = await whatif.removeLocks(name, email);
    res.send(whatifLocks);
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const addLock = async (req, res, next) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const payload = req?.body;
    const response = await whatif.addLock(payload, name, email);
    res.status(201).send(response);
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const getIntialCustomerData = async (req, res, next) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const cust_code = req?.params?.cust_code;

    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();

    const financialYear = currentMonth >= 10 ? currentYear + 1 : currentYear;

    const initialData = await whatif.getIntialCustomerData(
      cust_code,
      financialYear,
      name,
      email
    );
    res.send(initialData);
  } catch (error) {
    res.status(400).send(error.message);
  }
};

// const exportWhatIfData = async (req, res) => {
// };

const getIntialData = async (req, res, next) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const cust_code = req.params.cust_code;

    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();

    const financialYear = currentMonth >= 10 ? currentYear + 1 : currentYear;

    const initialData = await whatif.getIntialData(
      name,
      email,
      cust_code,
      financialYear
    );
    res.send(initialData);
  } catch (error) {
    res.status(400).send(error.message);
  }
};
const getNextQuarterProductDataByWeek = async (req, res, next) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const pkey = req?.query.pkey;
    const weekNo = parseInt(req?.query.weekNo) + 1;
    const whatif_id = req?.query?.whatif_id;
    const financialYear = req?.query.year;

    const initialData = await whatif.getNextQuarterProductDataByWeek(
      pkey,
      weekNo,
      name,
      email,
      financialYear
    );
    const whatifPromoWeeks = await whatif.getNextPromosByPkeyWhatifId(
      pkey,
      whatif_id,
      weekNo,
      name,
      email
    );
    const nextQuarterDataWhatifPromos = {
      nextQuarterProductDataByWeek: initialData,
      whatifPromoWeeks: whatifPromoWeeks,
    };
    res.send(nextQuarterDataWhatifPromos);
  } catch (error) {
    console.error(error);
    res.status(400).send(error.message);
  }
};

const getRequiredFilteredData = async (req, res) => {
  try {
    const email = req?.session?.user?.email;
    const name = req?.session?.user?.name;
    const cust_code = req.params.cust_code;
    const selectedQuarters = req.query.quarters;
    const financialYear = req.query.year;
    const isTotalsRequired = req.query.totals === "true";
    const isCalendarDataRequired = req.query.calendarData === "true";
    let filteredCustomerData = {};

    const customerData = await whatif.getWhatifByCustomer(
      cust_code,
      financialYear,
      selectedQuarters,
      name,
      email
    );
    filteredCustomerData.whatifData = JSON.parse(customerData);

    if (isTotalsRequired == true) {
      const totals = await whatif.getWhatifTotalsByCustomer(
        cust_code,
        name,
        email,
        financialYear
      );
      filteredCustomerData.totals = totals;
    }

    if (isCalendarDataRequired == true) {
      const defaultCalendarData = await whatif.getDefaultCalendarData(
        selectedQuarters,
        name,
        email,
        financialYear
      );
      const stdCalendarData = await whatif.getstdCalendarData(
        selectedQuarters,
        name,
        email,
        financialYear
      );

      filteredCustomerData.defaultCalendarData = defaultCalendarData;
      filteredCustomerData.stdCalendarData = stdCalendarData;
    }

    res.send(filteredCustomerData);
  } catch (error) {
    console.log("error", error);
    res.status(400).send(error.message);
  }
};

module.exports = {
  getWhatifByCustomer,
  getWhatifTotalsByCustomer,
  getWhatifProductByCustomer,
  getWhatifByQuarter,
  getDefaultCalendarData,
  getstdCalendarData,
  getWhatifCustomers,
  getWhatifProducts,
  addWhatif,
  removeWhatif,
  getBreakevenStatuses,
  getTaskTypes,
  removeLocks,
  addLock,
  removeLocks,
  getCurrentQuarterCalendarData,
  getCurrentQuarterStdCalendarData,
  getIntialData,
  getNextQuarterProductDataByWeek,
  getIntialCustomerData,
  // exportWhatIfData,
  getRequiredFilteredData,
};
