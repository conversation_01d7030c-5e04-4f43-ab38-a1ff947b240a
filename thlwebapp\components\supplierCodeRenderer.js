const supplierCodeRenderer = (params) => {
  const code_count =
    params.data?.prophets.length > 0 && params.data?.prophets[0].code_count;

  const prophet_id =
    params.data?.prophets.length > 0 && params.data?.prophets[0].prophet_id;

  const isSupplierAccount = params.data?.roleIds?.includes(1) || params.data?.roleIds?.includes(6);

  const supCode = params.data?.supplier_code;

  let currency =
    params.data?.currency == "$"
      ? `\\${params.data?.currency}`
      : params.data?.currency;
  let actualCurr;
  if (currency && currency == "Not Entered") {
    actualCurr = "";
  } else {
    actualCurr = currency;
  }
  let isValid = true;

  if (isSupplierAccount) {
    if (prophet_id == 1) {
      let regexPattern;
      
        regexPattern = new RegExp(`^[A-Z0-9]{4}[A-Z0145678]${actualCurr}$`);
      

      isValid = regexPattern.test(supCode);
    } else if (prophet_id == 2) {
      let regexPattern;
    
        regexPattern = new RegExp(`^([A-Z0]{4})9${actualCurr}$`);
      

      isValid = regexPattern.test(supCode);
    } else if (prophet_id == 3) {
      let regexPattern = new RegExp(`^[A-Z0-9]{4}[A-*********][A-Z0-9]*$`);
      isValid = regexPattern.test(supCode) && supCode.length == 6;
    } else if (prophet_id == 4) {
      let regexPattern;

      
        regexPattern = new RegExp(`^([A-Z0]{4})2${actualCurr}$`);
      

      isValid = regexPattern.test(supCode);
    }
  }
  const spanStyle = {
    width: "80px",
    textAlign: "left",
    display: "inline-block",
    verticalAlign: "middle",
    lineHeight: "24px",
    height: "32px",
    color: code_count > 1 || !isValid ? "#B31312" : "#3EAB58",
    ...(params.colDef.field === "status" && statusColumnStyles[params.value]),
  };

  return (
    <>
      <span style={spanStyle}>{params?.data?.supplier_code}</span>
    </>
  );
};

export default supplierCodeRenderer;
