SELECT 
    s.[id],
    s.[name],
    srs.[role_id],
STUFF((SELECT ', ' + CAST(spr.role_name AS VARCHAR(100)) [text()]
      FROM [dbo].[supplier_role] spr 
      WHERE spr.id = srs.role_id and s.id = srs.supplier_id
      FOR XML PATH(''), TYPE)
    .value('.','NVARCHAR(MAX)'),1,2,' ') role_names
    FROM [dbo].[supplier] s
    FULL OUTER JOIN [dbo].[supplier_sendac_group] ssg ON s.id = ssg.supplier_id
LEFT JOIN [dbo].[supplier_roles] srs ON s.id = srs.supplier_id
    WHERE srs.role_id != 1 and srs.role_id != 5 and srs.role_id != 6 and ssg.supplier_id is null and company=@company
