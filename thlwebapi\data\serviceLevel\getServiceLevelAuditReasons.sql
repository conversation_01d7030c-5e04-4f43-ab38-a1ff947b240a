SELECT reason.id
      ,reason.order_id
      ,reason.quantity
      ,reason.reason_id
      ,reason_master.reason as reason
      ,reason.subreason_id
      ,reason_master_sub.reason as sub_reason
      ,reason.comment
      ,reason.added_by
      ,reason.is_deleted
      ,reason.deleted_by
      ,reason.delete_reason
      ,reason.updated_by,
      reason.timestamp,reason.action_type
      FROM sl_reasons_audit reason
      LEFT JOIN sl_reasons_master reason_master ON reason.reason_id = reason_master.id
      LEFT JOIN sl_reasons_master reason_master_sub ON reason.subreason_id = reason_master_sub.id where order_id=@orderId order by reason.timestamp desc