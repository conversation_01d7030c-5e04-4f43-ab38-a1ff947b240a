import React, { useEffect, useState } from "react";
import {
  FluentProvider,
  SSRProvider,
  webLightTheme,
} from "@fluentui/react-components";
import Layout from "@/components/Layout";
import SLRoot from "@/components/service_level/SLRoot";
import { getSLData } from "@/utils/service-level/utils/getSLData";
import { toast, ToastContainer } from "react-toastify";
import { useLoading } from "@/utils/loaders/loadingContext";
import { useServiceCustomers } from "@/utils/serviceCustomerContext";
import { ThreeCircles } from "react-loader-spinner";
import { logout } from "@/utils/secureStorage";
import { useRouter } from "next/router";

const ServiceLevel = ({userData}) => {
  const router = useRouter();
  const { setIsLoading } = useLoading();
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);


  // State variables
  const [productList, setProductList] = useState([]);
  const [customerList, setCustomerList] = useState([]);
  const { updateServiceCustomersList } = useServiceCustomers();
  const [customerSLData, setCustomerSLData] = useState([]);
  const [currentCustomer, setCurrentCustomer] = useState([]);
  const [isTokenExpired, setIsTokenExpired] = useState(false);
  const [requestError, setRequestError] = useState(false);
  const [initalDate, setInitalDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [allReasonsSubreasons, setAllReasonsSubreasons] = useState({});
  const [loading, setLoading] = useState(true);
  const [initialDataExists, setInitialDataExists] = useState(null);
  const [isTakingLong, setIsTakingLong] = useState(false);
  const getLastTwoDays = () => {
    const today = new Date();
    const twoWeeksAgo = new Date(today);
    twoWeeksAgo.setDate(today.getDate() - 1);

    const formatDate = (date) => {
      const day = String(date.getDate()).padStart(2, "0");
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const year = date.getFullYear();
      return `${month}-${day}-${year}`;
    };

    return {
      start_date: formatDate(twoWeeksAgo),
      end_date: formatDate(today),
    };
  };
  const loadingMessages = [
    "Loading service-level data...",
    "Fetching customers...",
    "Fetching products...",
    "Preparing and organizing the data...",
    "Finalizing the dashboard for display...",
    "Almost there...",
  ];

  useEffect(() => {
    if (!loading) return;

    const interval = setInterval(() => {
      setCurrentMessageIndex((prevIndex) =>
        prevIndex < loadingMessages.length - 1 ? prevIndex + 1 : prevIndex
      );
    }, 5000); // 5 seconds per message

    const longDelayTimeout = setTimeout(() => {
      setIsTakingLong(true);
    }, 40000); // 40s total

    return () => {
      clearInterval(interval);
      clearTimeout(longDelayTimeout);
    };
  }, [loading]);

  useEffect(() => {
    if (typeof document !== "undefined") {
      document.title = "Service Level";
    }
    const fetchData = async () => {
      const slFilters = JSON.parse(userData?.slFilters || "{}");
      const orderTypeId = slFilters?.orderTypeId || 3;
      const cust_code = slFilters?.cust_code || "All Customers";
      let start_date = slFilters?.start_date || null;
      let end_date = slFilters?.end_date || null;

      if (
        !start_date ||
        start_date === "null" ||
        !end_date ||
        end_date === "null"
      ) {
        const lastTwoWeeks = getLastTwoDays();
        start_date =
          start_date === "null" || !start_date
            ? lastTwoWeeks.start_date
            : start_date;

        end_date =
          end_date === "null" || !end_date ? lastTwoWeeks.end_date : end_date;
      }

      setInitalDate(start_date);
      setEndDate(end_date);

      if (!userData) {
        window.location.href = `/login?redirect=${encodeURIComponent(
          window.location.pathname
        )}`;
        return;
      }
      const company = userData?.company;
      const ADCompanyName = userData?.companyName || userData?.ADCompanyName;
      
      try {

        const data = await getSLData(
          `get-initial-sl-data/${cust_code}/${start_date}/${end_date}/true/${orderTypeId}/${company}/${ADCompanyName}`
        );
        if (!data) {
          setLoading(false);
          setInitialDataExists(false);
          setIsTokenExpired(true);
        } else if (data?.length === 0) {
          setInitialDataExists(false);
          setRequestError(true);
          setLoading(false);
        } else {
          const uniqueCustomersMap = new Map();

          uniqueCustomersMap.set("All Customers", {
            value: "All Customers",
            label: "All Customers",
          });

          // Add all other customers, overwriting any duplicates
          data.sLCustomers.forEach((customer) => {
            uniqueCustomersMap.set(customer.value, customer);
          });

          // Convert the Map values back to an array
          const customers = Array.from(uniqueCustomersMap.values());

          setCustomerList(customers);
          if (ADCompanyName == "Integrated Service Solutions Ltd") {
            const slServiceCustomers = [
              {
                value: "All Service Customers",
                label: "All Service Customers",
              },
              ...data?.sLServiceCustomers,
            ];
            updateServiceCustomersList(slServiceCustomers);
          }
          setAllReasonsSubreasons(data.allReasonSubReasons);
          const dataArray = Object.values(data.formattedSLData);
          if (dataArray.length > 0) {
            setInitialDataExists(true);
          } else {
            setInitialDataExists(false);
          }
          const sortedData = Object.values(dataArray).sort((a, b) => {
            const dateComparison =
              new Date(a.DEPOT_DATE) - new Date(b.DEPOT_DATE);
            if (dateComparison !== 0) return dateComparison;
            const customerComparison = a.CUSTOMER.localeCompare(b.CUSTOMER);
            if (customerComparison !== 0) return customerComparison;
            return a.PRODUCT_DESCRIPTION.localeCompare(b.PRODUCT_DESCRIPTION);
          });

          setCustomerSLData(sortedData);
          setProductList(
            dataArray?.map((item) => ({
              label: item.PRODUCT_DESCRIPTION,
              value: item.ALT_FILID,
            }))
          );
          setCurrentCustomer(
            cust_code
              ? { value: cust_code, label: cust_code }
              : customers[0] || {}
          );
          setLoading(false);
        }
      } catch (error) {
        setInitialDataExists(false);
        toast.error("Failed to fetch data", {
          position: "top-right",
        });
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
    setIsLoading(false);
  }, [setIsLoading]);

  useEffect(() => {
    if (isTokenExpired) {
      toast.error("Your session has expired. Please log in again.");
      setTimeout(async() => {
        await logout();
        const redirectUrl = `/login?redirect=${encodeURIComponent(
          window.location.pathname
        )}`;
        router.push(redirectUrl);
      }, 3000);
    } else if (requestError) {
      toast.error(
        "There was an error with your request. Please check your data and try again."
      );
    }
  }, [isTokenExpired, requestError]);
  return (
    <Layout userData={userData}>
      <ToastContainer limit={1} />
      <SSRProvider>
        <FluentProvider
          theme={webLightTheme}
          className="!bg-transparent"
          style={{ fontFamily: "poppinsregular" }}
        >
          {loading ? (
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                height: "100vh",
              }}
            >
              <ThreeCircles
                color="#002D73"
                height={50}
                width={50}
                visible={true}
                ariaLabel="oval-loading"
                secondaryColor="#0066FF"
                strokeWidth={2}
                strokeWidthSecondary={2}
              />
              <p
                style={{
                  marginTop: "1rem",
                  fontStyle: "italic",
                  color: "#002D73",
                }}
              >
                {isTakingLong
                  ? "This is taking longer than expected. Please wait..."
                  : loadingMessages[currentMessageIndex]}{" "}
              </p>
            </div>
          ) : (
            <>
              {initialDataExists != null && (
                <SLRoot
                  productList={productList}
                  customerList={customerList}
                  customerSLDataOg={customerSLData}
                  initialDataExists={initialDataExists}
                  userData={userData}
                  currentCustomer={currentCustomer}
                  initalDate={initalDate}
                  endDate={endDate}
                  setCustomerList={setCustomerList}
                  allReasonsSubreasons={allReasonsSubreasons}
                  setAllReasonsSubreasons={setAllReasonsSubreasons}
                />
              )}
            </>
          )}
        </FluentProvider>
      </SSRProvider>
    </Layout>
  );
};

export default ServiceLevel;

export const getServerSideProps = async (context) => {
  try {
    const { req, resolvedUrl } = context;
    const sessionId = req.cookies.thl_session;

    if (!sessionId) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(resolvedUrl)}`,
          permanent: false,
        },
      };
    }

    const apiBase =
      process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8081";

    const response = await fetch(`${apiBase}/api/auth/me`, {
      method: "GET",
      headers: {
        Cookie: `thl_session=${sessionId}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(resolvedUrl)}`,
          permanent: false,
        },
      };
    }

    const { user } = await response.json();

    return {
      props: {
        userData: user,
      },
    };
  } catch (error) {
    console.error("Error in getServerSideProps:", error);
    return {
      redirect: {
        destination: "/login",
        permanent: false,
      },
    };
  }
};
