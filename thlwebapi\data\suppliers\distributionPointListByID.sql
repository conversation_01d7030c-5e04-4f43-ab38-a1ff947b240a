SELECT dp.[id]
      ,dp.[from_dp]
      ,dp.[name] as distribution_name
      ,sdp.[distribution_point_id],
	  sdp.[isActive],
	  ps.[prophet_name],
    sdp.[supplier_id]
  FROM [distribution_points] dp
  LEFT JOIN [supplier_distribution_point] sdp ON sdp.distribution_point_id=dp.id
  LEFT JOIN [prophet_system] ps ON ps.id=dp.prophet_id
  WHERE 
  sdp.[isActive] = 1
  AND sdp.supplier_id=@supplier_id