SELECT
    s.[id],
    s.[name],
    s.[requestor_name],
    s.[requestor_email],
    s.[compliance],
    s.[financial],
    s.[technical],
    s.[procurement],
    s.[emergency_request],
    s.[status],
    sp_inner.[prophet_id],
    (
        SELECT
            sr_inner.[role_id]
        FROM
            [dbo].[supplier_roles] sr_inner
        WHERE
            s.id = sr_inner.supplier_id FOR JSON PATH
    ) AS role_ids,
    (
        SELECT
            sp_inner.[prophet_id],
            sp_inner.[prophet_code]
        FROM
            [dbo].[supplier_prophets] sp_inner
        WHERE
            s.id = sp_inner.supplier_id
        FOR JSON PATH
    ) AS prophet_ids
FROM
    [dbo].[Supplier] s
LEFT JOIN [supplier_prophets] sp_inner ON s.id = sp_inner.supplier_id
WHERE
    sp_inner.[prophet_id] = @prophet_id
    AND status NOT IN (5, 6);