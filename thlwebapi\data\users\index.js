"use strict";

const utils = require("../utils");
const config = require("../../config");
const sql = require("mssql");
const logger = require("../../utils/logger");

const getUsers = async () => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("users");
    const list = await pool.request().query(sqlQueries.usersList);
    return list.recordset;
  } catch (error) {
    return error.message;
  }
};

const getUserById = async (id, name) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("users");
    const user = await pool
      .request()
      .input("user_id", sql.Int, id)
      .query(sqlQueries.userById);
    return user.recordset;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `Fetch user failed due to ${error.message}`,
    });
    return error.message;
  }
};

const getUserByEmail = async (email, name) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("users");
    const user = await pool
      .request()
      .input("email", sql.VarChar, email)
      .query(sqlQueries.userByEmail);
    return user.recordset;
  } catch (error) {
    console.error(error);
    logger.error({
      username: name,
      type: "error",
      description: `Loggedin user failed. Unauthorized user.,${error}`,
    });
    return error.message;
  }
};

const createUser = async (userData, email, name, department_id) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("users");
    const checkExistingUser = await pool
      .request()
      .input("email", sql.VarChar, userData?.email)
      .query(sqlQueries.checkExistingUser);

    if (checkExistingUser.recordset?.length > 0) {
      return { data: "exists" };
    } else {
      const insertUser = await pool
        .request()
        .input("role_id", sql.Int, userData?.role_id)
        .input("email", sql.VarChar, userData?.email)
        .input("department_id", sql.Int, department_id)
        .query(sqlQueries.createUser);

      logger.info({
        username: name,
        type: "success",
        description: `User ${email} created a new user ${
          userData?.email
        } with ${
          userData?.role_id == 1
            ? "admin"
            : userData?.role_id == 2
            ? "approver"
            : ""
        } role`,
      });
      return insertUser.recordset;
    }
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `Error creating user ${error.message}`,
    });
    return error.message;
  }
};

const updateUser = async (id, userData, name, email, department_id) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("users");
    const updateUser = await pool
      .request()
      .input("id", sql.Int, id)
      .input("role_id", sql.Int, userData?.role_id)
      .input("email", sql.VarChar, userData?.email)
      .input("department_id", sql.Int, department_id)
      .query(sqlQueries.updateUser);
    logger.info({
      username: name,
      type: "success",
      description: `User ${email} updated user ${userData?.email}`,
    });
    return updateUser.recordset;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `Error updating user ${error.message}`,
    });
    return error.message;
  }
};

const deleteUser = async (id, email, name) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("users");
    const user = await pool
      .request()
      .input("user_id", sql.Int, id)
      .query(sqlQueries.userById);
    const deleted = await pool
      .request()
      .input("id", sql.Int, id)
      .query(sqlQueries.deleteUser);
    logger.info({
      username: name,
      type: "success",
      description: `User ${email} deleted user ${user?.recordset[0]?.email}`,
    });
    return deleted.recordset;
  } catch (error) {
    console.error(error);
    logger.error({
      username: name,
      type: "error",
      description: `Error deleting user ${error.message}`,
    });
    return error.message;
  }
};

const getTheme = async (company, ADCompany) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("users");
    const actualCompany =
      company == "dpsltd" && ADCompany == "DPS MS" ? "dpsms" : company;
    const user = await pool
      .request()
      .input("company", sql.VarChar, actualCompany)
      .query(sqlQueries.getTheme);

    return user.recordset;
  } catch (error) {
  console.log(error);
    return error.message;
  }
};

module.exports = {
  getUsers,
  getUserById,
  getUserByEmail,
  createUser,
  deleteUser,
  updateUser,
  getTheme
};
