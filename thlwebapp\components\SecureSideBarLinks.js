"use client";
import { faUser } from "@fortawesome/free-regular-svg-icons";
import {
  faCartArrowDown,
  faCartFlatbed,
  faCartFlatbedSuitcase,
  faCartShopping,
  faClipboardQuestion,
  faList,
  faTruck,
  faUsersLine,
  faSquareQuestion,
  faCircleQuestion,
  faFileCircleQuestion,
} from "@fortawesome/free-solid-svg-icons";
import { faChartLine } from "@fortawesome/free-solid-svg-icons";
import { faBoxArchive } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Link from "next/link";
import React from "react";
import { useLoading } from "@/utils/loaders/loadingContext";
import { useSecurePermissions } from "@/utils/securePermissions";

export default function SecureSideBarLinks({
  userData,
  currentPathname,
  // Remove individual active state props - we'll calculate them
}) {
  const { setIsLoading } = useLoading();
  const { permissions, isLoading } = useSecurePermissions(userData);

  // Calculate active states based on current path
  const isActive = (path) => {
    return currentPathname?.startsWith(path);
  };

  const handleLinkClick = (e, path) => {
    if (isActive(path)) {
      e.preventDefault();
      window.location.reload();
    } else {
      setIsLoading(true);
    }
  };

  if (isLoading) {
    return <div>Loading permissions...</div>;
  }

  return (
    <ul className="nav navbar-nav">
      {/* Suppliers */}
      {permissions.canViewSuppliers && (
        <li className="flex justify-center mb-3">
          <Link
            href="/suppliers"
            title="Supplier"
            className={`!px-4 !py-3 bg-white rounded-md text-center ${
              isActive("/suppliers") ? "opacity-100" : "opacity-70"
            }`}
            onClick={(e) => handleLinkClick(e, "/suppliers")}
          >
            <FontAwesomeIcon
              icon={faTruck}
              size="lg"
              className="text-skin-primary"
            />
          </Link>
        </li>
      )}

      {/* Products */}
      {permissions.canViewProducts && (
        <li className="flex justify-center mb-3">
          <Link
            href="/products"
            title="Products"
            className={`!px-4 !py-3 bg-white rounded-md text-center ${
              isActive("/products") ? "opacity-100" : "opacity-70"
            }`}
            onClick={(e) => handleLinkClick(e, "/products")}
          >
            <FontAwesomeIcon
              icon={faBoxArchive}
              size="lg"
              className="text-skin-primary"
            />
          </Link>
        </li>
      )}

      {/* What If */}
      {permissions.canViewWhatif && (
        <li className="flex justify-center mb-3">
          <Link
            href="/whatif"
            title="What if"
            className={`!px-4 !py-3 bg-white rounded-md text-center ${
              isActive("/whatif") ? "opacity-100" : "opacity-70"
            }`}
            onClick={(e) => handleLinkClick(e, "/whatif")}
          >
            <FontAwesomeIcon
              icon={faChartLine}
              size="lg"
              className="text-skin-primary"
            />
          </Link>
        </li>
      )}

      {/* Service Level */}
      {permissions.canViewServiceLevel && (
        <li className="flex justify-center mb-3">
          <Link
            href="/service_level"
            title="Service Level"
            className={`!px-4 !py-3 bg-white rounded-md text-center ${
              isActive("/service_level") ? "opacity-100" : "opacity-70"
            }`}
            onClick={(e) => handleLinkClick(e, "/service_level")}
          >
            <FontAwesomeIcon
              icon={faFileCircleQuestion}
              size="lg"
              className="text-skin-primary"
            />
          </Link>
        </li>
      )}

      {/* Users */}
      {permissions.canViewUsers && (
        <li className="flex justify-center mb-3">
          <Link
            href="/users"
            title="Users"
            className={`!px-4 !py-3 bg-white rounded-md text-center ${
              isActive("/users") ? "opacity-100" : "opacity-70"
            }`}
            onClick={(e) => handleLinkClick(e, "/users")}
          >
            <FontAwesomeIcon
              icon={faUsersLine}
              size="lg"
              className="text-skin-primary"
            />
          </Link>
        </li>
      )}

      {/* Logs */}
      {permissions.canViewLogs && (
        <li className="flex justify-center mb-3">
          <Link
            href="/viewlogs"
            title="Logs"
            className={`!px-4 !py-3 bg-white rounded-md text-center ${
              isActive("/viewlogs") ? "opacity-100" : "opacity-70"
            }`}
            onClick={(e) => handleLinkClick(e, "/viewlogs")}
          >
            <FontAwesomeIcon
              icon={faList}
              size="lg"
              className="text-skin-primary"
            />
          </Link>
        </li>
      )}
    </ul>
  );
} 