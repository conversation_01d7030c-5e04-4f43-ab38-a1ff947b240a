import React, { useEffect, useRef } from "react";
import useOnScreen from "../../hooks/useOnScreen";
import TableColumnTh from "../TableColumnTh";
import TableValueTd from "./TableValueTd";
import { Tooltip } from "@fluentui/react-components";

function TableValueSection({
  currentData,
  rowHeight,
  noOfWeeks,
  checkedStates,
  columnRefs,
  columnWidths,
  currentStartWeek, currentWeek,
  calendarWeeks,
  setIsValueRendered,
  currency,
  ctxCalenderData,
  customerDataTotals,fiscalyear,currentFiscalYear
}) {
  const rowValueRef = useRef();
  const isRowValueVisible = useOnScreen(rowValueRef);

  useEffect(() => {
    if (isRowValueVisible) {
      setIsValueRendered(true);
    }
  }, [isRowValueVisible]);
  return (
    <>
      <tr
        style={{ top: `${rowHeight}px` }}
        className="sticky z-10 value"
        ref={rowValueRef}
      >
        {!isRowValueVisible && <td style={{ minHeight: '10px' }}></td>}
        {isRowValueVisible && (
          <TableColumnTh
            rowId="sectionrow"
            rowTitle="VALUE"
            headHeight={rowHeight}
            noOfWeeks={noOfWeeks}
            checkedStates={checkedStates}
            columnRefs={columnRefs}
            columnWidths={columnWidths}
          />
        )}
      </tr>
      {isRowValueVisible &&
        currentData?.map((row, index) => {
          const quarterData = [
            ...(row.quarters?.Q1 || []),
            ...(row.quarters?.Q2 || []),
            ...(row.quarters?.Q3 || []),
            ...(row.quarters?.Q4 || []),
            ...(row.quarters?.Q5 || []),
          ];

          return (
            <tr key={index}>
              <TableColumnTh
                isTd={true}
                checkedStates={checkedStates}
                columnRefs={columnRefs}
                columnWidths={columnWidths}
                row={row}
                tdValue={row?.total_product_value?.toFixed(2)}
              />
              <TableValueTd
                quarterData={quarterData}
                row={row}
                previousSunday={currentStartWeek}
                currentWeek={currentWeek}
                calendarWeeks={calendarWeeks}
                currency={currency}
                fiscalyear={fiscalyear}
                currentFiscalYear={currentFiscalYear}
              />
            </tr>
          );
        })}

      {/* table row to display section totals */}
      <tr>
        <TableColumnTh
          checkedStates={checkedStates}
          columnRefs={columnRefs}
          columnWidths={columnWidths}
        />
        {ctxCalenderData.map((ele, index) => {
          const weekNo = ele.fiscalweek.toString();

          return (
            <Tooltip
              key={index}
              content="Total of all products for this week"
              relationship="label"
              className="!bg-white"
            >
              <td
                key={index}
                className={`!text-center font-bold border-[#ddd] ${ele.currentWeek == weekNo && ele.fiscalyear==currentFiscalYear ? "bg-currentWeek" : ""
                  }`}
              >
                £{
                  customerDataTotals.weeklyAllProductsTotals?.[
                            ele.fiscalyear
                          ]?.[weekNo]
                    ?.total_all_products_value?.toFixed(2)
                }
              </td>
            </Tooltip>
          );
        })}
      </tr>
    </>
  );
}

export default TableValueSection;
