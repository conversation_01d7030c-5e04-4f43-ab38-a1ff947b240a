BEGIN TRANSACTION;

	BEGIN TRY

		DECLARE @currentActiveAction INT;
		DECLAR<PERSON> @isAllowed BIT = 0;
		DECLARE @departmentId INT = 0;
		DECLARE @roleId INT = 0;

		SELECT @currentActiveAction = action_id FROM [product_packaging_status] WHERE packaging_request_id = @packaging_request_id and is_latest=1

		IF @currentActiveAction = 1 AND (@actionId = 1 OR @actionId = 2)
		BEGIN
			SET @isAllowed = 1
		END
		
		IF @isAllowed = 0
		BEGIN
			RAISERROR('Invalid request.', 16, 1);
		END

    UPDATE product_packaging_status
    SET is_latest = 0
    WHERE packaging_request_id = @packaging_request_id;

    IF (@actionId = 1 OR @actionId = 2)
    BEGIN
        UPDATE [product_packaging_request]
        SET
            change_launch_date = @change_launch_date,
            master_product_code_id = @master_product_code_id,
            existing_packaging_code = @existing_packaging_code,
            end_customer = @end_customer,
            packaging_name = @packaging_name,
            type_of_packaging = @type_of_packaging,
            type_of_material = @type_of_material,
            colour_of_material = @colour_of_material,
            [dimension_size(mm)] = @dimension_size,
            [component_weight(g)] = @component_weight,
            recyclable_to_oprl = @recyclable_to_oprl,
            paper_from_sustainable_forestry = @paper_from_sustainable_forestry,
            recyclable_content = @recyclable_content,
            supplier = @supplier,
            reason = @reason,
            sub_product_code = @subProdCode,
            sub_product_code_label = @subProdCodeLabel,
            trading_business = @trading_business
        WHERE request_no = @reqNumber;
        
        INSERT INTO product_packaging_status (
            packaging_request_id,
            action_id,
            actioned_by_email,
            actioned_by_name,
            comment,
            is_latest
        ) 
        VALUES (
            @packaging_request_id,
            @actionId,
            @actionedByEmail,
            @actionedByName,
            COALESCE(@comment, NULL),
            1
        );
    END

    COMMIT TRANSACTION;
    PRINT 'Transaction committed.';
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;

    PRINT 'Transaction rolled back.';
    PRINT ERROR_MESSAGE();
END CATCH;

