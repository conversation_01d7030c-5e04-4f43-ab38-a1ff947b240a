{"name": "th<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "THL Web API", "main": "index.js", "scripts": {"start": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "FLRS", "license": "ISC", "dependencies": {"@azure/msal-node": "^2.6.1", "@microsoft/microsoft-graph-client": "^3.0.7", "axios": "^1.6.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "fs-extra": "^11.2.0", "jsonwebtoken": "^9.0.2", "jsonwebtoken-promisified": "^1.0.3", "jwks-rsa": "^3.1.0", "lodash": "^4.17.21", "mssql": "^10.0.1", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.8", "passport": "^0.7.0", "passport-azure-ad": "^4.3.5", "postmark": "^4.0.2", "socket.io": "^4.7.5", "tedious": "^16.6.1", "winston": "^3.11.0", "winston-sql-transport": "^3.1.0"}, "devDependencies": {"nodemon": "^3.1.0"}}