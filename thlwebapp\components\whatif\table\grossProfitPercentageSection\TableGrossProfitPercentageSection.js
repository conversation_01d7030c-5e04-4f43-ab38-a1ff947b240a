import React, { useRef } from "react";
import useOnScreen from "../../hooks/useOnScreen";
import TableColumnTh from "../TableColumnTh";
import TableGrossProfitPercentageTd from "./TableGrossProfitPercentageTd";

function TableGrossProfitPercentageSection({
  currentData,
  rowHeight,
  noOfWeeks,
  checkedStates,
  columnRefs,
  columnWidths,
  currentStartWeek,currentWeek,
  calendarWeeks,fiscalyear,currentFiscalYear
}) {
  const rowGrossProfitPercentageRef = useRef();
  const isRowGrossProfitPercentageVisible = useOnScreen(rowGrossProfitPercentageRef);

  return (
    <>
      <tr
        style={{ top: `${rowHeight}px` }}
        className="sticky z-10 gpp"
        ref={rowGrossProfitPercentageRef}
      >
        {!isRowGrossProfitPercentageVisible && <td style={{ minHeight: '10px' }}></td>}
        {isRowGrossProfitPercentageVisible && (
          <TableColumnTh
            rowId="sectionrow"
            rowTitle="GROSS PROFIT PERCENTAGE"
            headHeight={rowHeight}
            noOfWeeks={noOfWeeks}
            checkedStates={checkedStates}
            columnRefs={columnRefs}
            columnWidths={columnWidths}
          />
        )}
      </tr>
      {isRowGrossProfitPercentageVisible &&
        currentData?.map((row, index) => {
          const quarterData = [
            ...(row.quarters?.Q1 || []),
            ...(row.quarters?.Q2 || []),
            ...(row.quarters?.Q3 || []),
            ...(row.quarters?.Q4 || []),
            ...(row.quarters?.Q5 || []),
          ];
          
          return (
            <tr key={index}>
              <TableColumnTh
                isTd={true}
                checkedStates={checkedStates}
                columnRefs={columnRefs}
                columnWidths={columnWidths}
                row={row}
                tdValue={""}
              />
              <TableGrossProfitPercentageTd
                quarterData={quarterData}
                previousSunday={currentStartWeek}
                currentWeek={currentWeek}
                calendarWeeks={calendarWeeks}
                row={row}
                fiscalyear={fiscalyear}
                currentFiscalYear={currentFiscalYear}
              />
            </tr>
          );
        })}
    </>
  );
}

export default TableGrossProfitPercentageSection;
